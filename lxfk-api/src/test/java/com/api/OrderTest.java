package com.api;

import org.springframework.beans.factory.annotation.Autowired;

import com.api.bean.UserRequest;
import com.api.client.ServiceClient;
import com.common.constant.Platform;
import com.common.util.POIUtil;
import com.service.UserHistoryService;

public class OrderTest {

	@Autowired
	private UserHistoryService userHistoryService;

	public static void main(String[] args) {
		//createSysUserMaster();
		/*String[] special = { ",", ".", "?", "!", "{", "}", "[", "]", "(", ")", "-", "+", "=", "*", "|", "/", "@", "#",
				"$", "%", "^", "&" };*/
		String[] special = { "(", ")"};
		StringBuffer buffer = new StringBuffer();
		for (String str : special) {
			buffer.append("\\" + str);
		}

		String reg_special = buffer.toString();
		//String regx = "[^\\u4e00-\\u9fa5\\(\\)" + reg_special + "]";
		//String reg_chinese = "，。？！（）【】￥、\\";
		String regx = "[^\u4e00-\u9fa5]*[\\(\\)]";
		String word = "你在哪，在(做什么呢)？@$#*&*&|||/";
		word = word.replaceAll(regx, "");
		System.out.println(word);
	}
	
	public static void importOrder() {
		String url = "/Users/<USER>/develop/1.xlsx";
		String[][] datas = POIUtil.parse(url);
		int counter = 900858;
		for (String[] row : datas) {
			StringBuilder sql = new StringBuilder();
//			System.err.println(row[0]);
//			System.err.println(row[1]);
			sql.append("insert into tb_order ")
			.append("(")
			.append("code,")
			.append("catalog,")
			.append("top,")
			.append("mobile,")
			.append("order_time,")
			.append("user_id,")
			.append("user_catalog,")
			.append("order_user_id,")
			.append("chance_user_id,")
			.append("service_user_id,")
			.append("pay_time,")
			.append("pay_channel,")
			.append("amount,")
			.append("stage,")
			.append("audit,")
			.append("audit_time,")
			.append("comment,")
			.append("status,")
			.append("modify_time,")
			.append("create_time")
			.append(")")
			.append(" values ")
			.append(" (")
			.append(counter).append(",")
			.append("'1000'").append(",")
			.append("'0'").append(",")
			.append("'").append(row[1]).append("'").append(",")
			.append("now()").append(",")
			.append(row[0]).append(",")
			.append("'1'").append(",")
			.append("10000099").append(",")
			.append("10000099").append(",")
			.append("10000099").append(",")
			.append("now()").append(",")
			.append("'1002'").append(",")
			.append("0").append(",")
			.append("0").append(",")
			.append("2").append(",")
			.append("now()").append(",")
			.append("'批量导入'").append(",")
			.append("0").append(",")
			.append("now()").append(",")
			.append("now()")
			.append(");")
			;
			System.out.println(sql);
			counter ++;
		}
	}
	
	public static void createSysUserMaster() {
		try {
			String url = "C:/Users/<USER>/Desktop/查询.xlsx";
			String[][] rows = POIUtil.parse(url);
			for (String[] row : rows) {
				System.out.println(row[0]);
				UserRequest request = new UserRequest();
				request.setSaleStaffId(10000933);
				request.setOrderId(Integer.parseInt(row[0]));
				ServiceClient<String> serviceClient = new ServiceClient<String>();
				serviceClient.setToken("eyJhbGciOiJIUzI1NiJ9.eyJuYW1lIjpudWxsLCJ0eXBlIjoiUyIsImV4cCI6MTY0NjcwMzA0MywidXNlcklkIjoxMDAwMDc0MywidXNlcm5hbWUiOiLnjovotZtfMjAwODQ3In0.3KYWcqP4TVko0a0UE0gtla530ZfCxPNf0zl_v4HtjC0");
				serviceClient.setPlatform(Platform.SYS.getCode());
				serviceClient.execute("/v1/user/teacher/distribution/first/consult", request, String.class);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
}
