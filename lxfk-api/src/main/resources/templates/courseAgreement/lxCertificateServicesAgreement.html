<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">

<head>
  <meta charset="UTF-8" />
  <title>签署协议</title>
  <style>
    * {
      padding: 0;
      margin: 0;
    }

    body {
      font-family: Sim<PERSON>ei;
    }

    @page {
      size: a4
    }

    .box {
      width: 750px;
      margin: 0 auto;
      box-sizing: border-box;
      box-shadow: 0px 0px 12px #ccc;
    }

    .agreement {
      color: #333;
      text-align: center;
      /* background: #1a8fe7; */
      font-size: 20px;
      padding: 30px 0;
      border-top: 1px solid #ccc;
    }

    .content {
      width: 93%;
      margin: 0 auto;
    }

    p {
      font-size: 16px;
      font-weight: normal;
      font-stretch: normal;
      line-height: 40px;
      letter-spacing: 0rem;
      color: #333333;
      text-indent: 24px;
    }
    .content_title{
      font-weight: bold;
    }
    .content_p {
      font-weight: bold;
    }

    .hr_line {
      width: 150px;
      display: inline-block;
      height: 1px;
      background: #333;
    }

    .content_boot {
      padding: 300px 0;
    }

    .content_boot_pt {
      width: 50%;
      position: relative;
      float: left;
    }

    .content_boot_bt {
      width: 40%;
      position: relative;
      float: right;
    }

    .content_boot_img {
      position: absolute;
      left: 60px;
      top: -10px;
    }

    .clear {
      clear: both
    }

    .course_radio {
      text-align: center
    }
  </style>
</head>

<body>
  <div class="box">
    <img src="https://api.zhonglvzhixue.com/v1/file/download?url=/2023/08/14/10/22/32155148.png" width="20%" />
    <h3 class="agreement">资格证书服务协议</h3>
    <!-- 协议内容 -->
    <div class="content">
      <p>甲方：众律智学教育咨询（北京）有限公司（以下简称甲方）</p>
      <p>乙方：<u class="hr_line" th:if="${userAgreement.sign} == null"></u><img th:if="${userAgreement.sign} != null"
          th:src="${userAgreement.sign}" width="100" />（以下简称乙方）</p>
      <p>身份证号:<u class="hr_line" th:if="${userAgreement.idNumber} == null"></u><u
          th:if="${userAgreement.idNumber} != null" th:text="${userAgreement.idNumber}"></u></p>
      <p>联系方式:<u class="hr_line" th:if="${userAgreement.username} == null"></u><u
          th:if="${userAgreement.username} != null" th:text="${userAgreement.username}"></u></p>
      <p>为了规范培训课程，保障学员的权益，以期达到良好的学习效果，甲乙双方本着相互信任的原则，在自愿、平等基础上，经双方友好协商达成如下协议，以供双方共同遵守，请乙方务必仔细阅读。</p>

      <div class="curriculum">
        <!-- 第一条、合作内容及服务期限 -->
        <p class="content_title content_p">第一条、合作内容及服务期限</p>
        <p>1、甲方向乙方提供相应辅导服务，帮助乙方备考<u class="hr_line" th:if="${userAgreement.projectName} == null"></u><u
            th:if="${userAgreement.projectName} != null" th:text="${userAgreement.projectName}"></u>考试。</p>
        <p>2、本合同自生效之日起至乙方取得相应资格证书为止。</p>
        <!-- 第二条、费用收取及相关政策 -->
        <div class="period">
          <p class="content_title content_p">第二条、费用收取及相关政策</p>
          <p>1、乙方在提供真实有效的个人资料后，配合甲方前期的考试相关工作，学费及服务总费用：<u class="hr_line" th:if="${userAgreement.totalLowerCasePrice} == null"></u><u
            th:if="${userAgreement.totalLowerCasePrice} != null"
            th:text="${userAgreement.totalLowerCasePrice}"></u>元，（大写）<u class="hr_line"
            th:if="${userAgreement.totalCapitalPrice} == null"></u><u th:if="${userAgreement.totalCapitalPrice} != null"
            th:text="${userAgreement.totalCapitalPrice}"></u>。实付费用为：<u class="hr_line"
            th:if="${userAgreement.lowerCasePrice} == null"></u><u th:if="${userAgreement.lowerCasePrice} != null"
            th:text="${userAgreement.lowerCasePrice}"></u>元，（大写）<u class="hr_line"
            th:if="${userAgreement.capitalPrice} == null"></u><u th:if="${userAgreement.capitalPrice} != null"
            th:text="${userAgreement.capitalPrice}"></u>。</p>
          <p>2、若因甲方服务原因使乙方未取得相应资格证书，则退还乙方全部费用。</p>
        </div>
        <!-- 第三条、甲方的权利和义务 -->
        <div class="requirements">
          <p class="content_title content_p">第三条、甲方的权利和义务</p>
          <p>1、甲方帮助乙方取得资格证书。</p>
          <p>2、甲方负责乙方报名、考籍注册、考务管理、证书发放等工作。</p>
          <p>3、甲方一次性收取费用包括取得证书的所有费用，无故不得向乙方收取其他费用。</p>
          <p>4、甲方对于乙方提交的相关材料，不得从事与证书无关的活动，否则需承担给学员带来的损失。</p>
        </div>

        <!-- 第四条、乙方的权利和义务 -->
        <div class="a_interest">
          <p class="content_title content_p">第四条、乙方的权利和义务</p>
          <p>1、乙方按照甲方要求提供相关材料，并保证资料真实有效。</p>
          <p>2、乙方需配合甲方完成报名、考试等相关工作。</p>
          <p>3、乙方需配合完成考籍存续期间内的改革，诸如要求本人刷脸、学习考试等要求。</p>
          <p>4、乙方需按照协议缴清相关费用。</p>
          <p>5、乙方需要本人学习应试知识并参与考试。</p>
          <p>6、乙方通过考试取得证书后必须合理合法的使用该证书，该证书不能用于挂靠。</p>
          <p>7、乙方需维护学校良好的教学品牌，不得从事扰乱教学秩序以及有损学校名誉、声誉的行为。</p>
        </div>
        <!-- 第五条、保密事项 -->
        <div class="b_interest">
          <p class="content_title content_p">第五条、保密事项</p>
          <p>甲、乙双方有义务严守本协议所涉及的各种文件、协议本身、技术资料、学费标准、招生信息等一切机密信息，且不得向第三方泄密。在未经对方同意的前提下，任何一方有权拒绝和制止对方涉嫌泄露机密的要求和行为。一经发现，将追究其法律责任，并要求赔偿违约金为合同总金额的30%。</p>
        </div>
        <!-- 第六条、违约责任 -->
        <div class="refund">
          <p class="content_title content_p">第六条、违约责任</p>
          <p>一、合作期间不论发生何种情况与分歧，在双方未能达到一致意见期间，双方均应从维护双方利益出发，首先按本协议规定继续履行各自职责，否则将负责赔偿有关损失和承担相关的连带责任。</p>
          <p>二、乙方在学习等重要环节出现重大违纪、违规、失职和失误行为时，甲方有权责令乙方限期整改；限期不予整改或造成损失严重的，甲方有权终止本协议。</p>
          <p>三、乙方在学习过程中无故停止学习，如中途换项目等等。甲方有权终止本协议或终止学习的一切事宜。</p>
          <p>四、若乙方持不符合条件的学历证书或者假毕业证书报名者，在学习期间，一旦查出，则取消学籍，由此造成的费用损失等，均由乙方本人自负！</p>
        </div>
        <!-- 第七条、法律适用及争议解决 -->
        <div class="other_matters">
          <p class="content_title content_p">第七条、法律适用及争议解决</p>
          <p>1、本合同的成立、生效、解释及争议的解决均应适用中华人民共和国法律。</p>
              <p>2、甲乙双方在履行本协议过程中，如发生任何争议，首先应通过友好协商解决。协商未能解决的，双方同意因本合同引起的或与本合同有关的任何争议选用以下方式处理，同意签署则视为选取了以下争议解决途径。</p>
          <div class="b_interest">
            <p class="content_p">2.1争议解决选择：</p>
            <p class="content_p"><input  type="checkbox" checked="checked" readonly="readonly" disabled="disabled" /><label>依法向郑州市仲裁委员会按照郑州市仲裁规则进行仲裁；</label></p>
        <p class="content_p"><input  type="checkbox" readonly="readonly" disabled="disabled" /><label>依法向甲方注册地人民法院诉讼解决；</label></p>
            <p class="content_p">如乙方未进行选择争议处理机构，将视为乙方同意依法向郑州市仲裁委员会按照郑州市仲裁规则进行仲裁处理争议。</p>
          </div>
        </div>
        <!-- 九、其他 -->
        <div class="other_matters">
          <p>本协议一式两份，双方各执壹份，签字盖章生效。</p>
          <p class="content_p">注意：乙方通过考试取得证书后必须合理合法的使用该证书，该证书不能用于挂靠！</p>
        </div>
      </div>
      <div class="course_radio" th:if="${userAgreement.sign} != null or ${userAgreement.blankAgreement}">
        <input type="checkbox" checked="checked" readonly="readonly" /><label>我已经阅读此协议</label>
      </div>
      <div th:if="${userAgreement.sign} != null or ${userAgreement.blankAgreement}" class="content_boot">
        <div class="content_boot_pt">
          <p>甲方：众律智学教育咨询（北京）有限公司</p>
          <p th:text="${userAgreement.signTime}"></p>
          <p>（以下无文字内容）</p>
          <img class="content_boot_img"
            src="https://api.zhonglvzhixue.com/v1/file/download?url=/2023/08/14/10/25/40155199.png" width="50%" />
        </div>
        <div class="content_boot_bt">
          <p>乙方：<img th:src="${userAgreement.sign}" width="100" /></p>
          <p th:text="${userAgreement.signTime}"></p>
        </div>
        <div class="clear"></div>
      </div>
    </div>
  </div>
</body>

</html>