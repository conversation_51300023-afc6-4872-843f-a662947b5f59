package com.api.bean;

import java.util.List;

/**
 * 用户信息备注明细表请求对象
 *
 * @date 2025-08-11 17:24:47
 */
public class UserInformationCommentFlowRequest extends PageRequest {
	private static final long serialVersionUID = 1L;

	/*
	 * 主键
	 */
	private Integer id;

	/*
	 * 主键集合
	 */
	private List<Integer> ids;

	/*
	 * 用户编号
	 */
	private Integer userId;

	/*
	 * 用户编号集合
	 */
	private List<Integer> userIds;

	/*
	 * 备注
	 */
	private String comment;

	/*
	 * 图片地址
	 */
	private String imgUrl;

	/*
	 * 操作人编号
	 */
	private Integer sysUserId;

	/*
	 * 操作人编号集合
	 */
	private List<Integer> sysUserIds;

	/*
	 * 数据类型(1000:舆情记录 1001:推班记录 1002:群服务情况记录 1003:备注)
	 */
	private String catalog;

	/*
	 * 状态(0:正常 1:无效)
	 */
	private String status;

	/*
	 * 修改时间
	 */
	private String modifyTime;

	/*
	 * 最小修改时间
	 */
	private String minModifyTime;

	/*
	 * 最大修改时间
	 */
	private String maxModifyTime;

	/*
	 * 创建时间
	 */
	private String createTime;

	/*
	 * 最小创建时间
	 */
	private String minCreateTime;

	/*
	 * 最大创建时间
	 */
	private String maxCreateTime;


	public Integer getId (){
		return id;
	}

	public void setId (Integer id) {
		this.id = id;
	}

	public List<Integer> getIds (){
		return ids;
	}

	public void setIds (List<Integer> ids) {
		this.ids = ids;
	}

	public Integer getUserId (){
		return userId;
	}

	public void setUserId (Integer userId) {
		this.userId = userId;
	}

	public List<Integer> getUserIds (){
		return userIds;
	}

	public void setUserIds (List<Integer> userIds) {
		this.userIds = userIds;
	}

	public String getComment (){
		return comment;
	}

	public void setComment (String comment) {
		this.comment = comment;
	}

	public String getImgUrl (){
		return imgUrl;
	}

	public void setImgUrl (String imgUrl) {
		this.imgUrl = imgUrl;
	}

	public Integer getSysUserId (){
		return sysUserId;
	}

	public void setSysUserId (Integer sysUserId) {
		this.sysUserId = sysUserId;
	}

	public List<Integer> getSysUserIds (){
		return sysUserIds;
	}

	public void setSysUserIds (List<Integer> sysUserIds) {
		this.sysUserIds = sysUserIds;
	}

	public String getCatalog (){
		return catalog;
	}

	public void setCatalog (String catalog) {
		this.catalog = catalog;
	}

	public String getStatus (){
		return status;
	}

	public void setStatus (String status) {
		this.status = status;
	}

	public String getModifyTime (){
		return modifyTime;
	}

	public void setModifyTime (String modifyTime) {
		this.modifyTime = modifyTime;
	}

	public String getMinModifyTime (){
		return minModifyTime;
	}

	public void setMinModifyTime (String minModifyTime) {
		this.minModifyTime = minModifyTime;
	}

	public String getMaxModifyTime (){
		return maxModifyTime;
	}

	public void setMaxModifyTime (String maxModifyTime) {
		this.maxModifyTime = maxModifyTime;
	}

	public String getCreateTime (){
		return createTime;
	}

	public void setCreateTime (String createTime) {
		this.createTime = createTime;
	}

	public String getMinCreateTime (){
		return minCreateTime;
	}

	public void setMinCreateTime (String minCreateTime) {
		this.minCreateTime = minCreateTime;
	}

	public String getMaxCreateTime (){
		return maxCreateTime;
	}

	public void setMaxCreateTime (String maxCreateTime) {
		this.maxCreateTime = maxCreateTime;
	}

}
