package com.api.bean;

import java.math.BigDecimal;
import java.util.List;

import com.common.bean.Bean;

/**
 * 用户考卷记录详情表
 *
 * @date 2023-10-09 17:50:33
 */
public class UserExamPaperRecordDetailResponse extends Bean {
	private static final long serialVersionUID = 1L;

	/**
	 * 采分点
	 **/
	private List<UserExamPaperRecordScoreResponse> userExamPaperRecordScoreResponseList;
	/**
	 * 主键
	 */
	private Integer id;

	/**
	 * 考卷记录编号
	 */
	private Integer userExamPaperRecordId;
	/**
	 * 考题编号
	 */
	private Integer questionId;
	/**
	 * 考题父编号
	 */
	private Integer parentQuestionId;
	/**
	 * 题干类型
	 */
	private String questionType;
	/**
	 * 题干类型名称
	 */
	private String questionTypeName;
	/**
	 * 学员上传图片
	 */
	private List<String> customerImgUrls;
	private String customerImgUrl;
	/**
	 * 答案(以逗号分割)
	 */
	private String answer;
	/**
	 * 分数
	 */
	private BigDecimal score;
	/**
	 * 用户得分
	 */
	private BigDecimal userScore;
	/**
	 * 状态(0:正常 1:无效)
	 */
	private String status;
	/**
	 * 修改时间
	 */
	private String modifyTime;
	/**
	 * 创建时间
	 */
	private String createTime;
	/**
	 * 老师评语
	 */
	private String teacherComment;
	/**
	 * 评语图片数组
	 */
	private List<String> commentImgUrls;
	/**
	 * 对错结果(0:对 1:错)
	 */
	private String questionResult;
	/**
	 * 对错结果名称
	 */
	private String questionResultName;
	/**
	 * 题干分组总分
	 */
	private BigDecimal questionGroupSumScore;
	/**
	 * 题干分组正确数量
	 */
	private Integer questionGroupRightNum;
	/**
	 * 题干分组错误数量
	 */
	private Integer questionGroupErrorNum;
	/**
	 * 题干信息
	 */
	private QuestionResponse questionResponse;
	/**
	 *标记状态(0:未标记 1:已标记)
	 */
	private String mark;

	/**
	 * 自定义提醒模板编号
	 */
	private Integer questionTemplateItemId;
	/**
	 * 自定义题型名称
	 */
	private String questionTemplateItemName;
	/**
	 * 材料题做题记录子集
	 */
	private List<UserExamPaperRecordDetailResponse> children;

	/**
	 * 自定义题型排序
	 */
	private Integer questionTemplateItemSort;

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Integer getUserExamPaperRecordId() {
		return userExamPaperRecordId;
	}

	public void setUserExamPaperRecordId(Integer userExamPaperRecordId) {
		this.userExamPaperRecordId = userExamPaperRecordId;
	}

	public Integer getQuestionId() {
		return questionId;
	}

	public void setQuestionId(Integer questionId) {
		this.questionId = questionId;
	}

	public Integer getParentQuestionId() {
		return parentQuestionId;
	}

	public void setParentQuestionId(Integer parentQuestionId) {
		this.parentQuestionId = parentQuestionId;
	}

	public String getQuestionType() {
		return questionType;
	}

	public void setQuestionType(String questionType) {
		this.questionType = questionType;
	}

	public String getQuestionTypeName() {
		return questionTypeName;
	}

	public void setQuestionTypeName(String questionTypeName) {
		this.questionTypeName = questionTypeName;
	}

	public String getAnswer() {
		return answer;
	}

	public void setAnswer(String answer) {
		this.answer = answer;
	}

	public BigDecimal getScore() {
		return score;
	}

	public void setScore(BigDecimal score) {
		this.score = score;
	}

	public BigDecimal getUserScore() {
		return userScore;
	}

	public void setUserScore(BigDecimal userScore) {
		this.userScore = userScore;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getModifyTime() {
		return modifyTime;
	}

	public void setModifyTime(String modifyTime) {
		this.modifyTime = modifyTime;
	}

	public String getCreateTime() {
		return createTime;
	}

	public void setCreateTime(String createTime) {
		this.createTime = createTime;
	}

	public String getTeacherComment() {
		return teacherComment;
	}

	public void setTeacherComment(String teacherComment) {
		this.teacherComment = teacherComment;
	}

	public List<String> getCustomerImgUrls() {
		return customerImgUrls;
	}

	public void setCustomerImgUrls(List<String> customerImgUrls) {
		this.customerImgUrls = customerImgUrls;
	}

	public List<String> getCommentImgUrls() {
		return commentImgUrls;
	}

	public void setCommentImgUrls(List<String> commentImgUrls) {
		this.commentImgUrls = commentImgUrls;
	}

	public String getQuestionResult() {
		return questionResult;
	}

	public void setQuestionResult(String questionResult) {
		this.questionResult = questionResult;
	}

	public String getQuestionResultName() {
		return questionResultName;
	}

	public void setQuestionResultName(String questionResultName) {
		this.questionResultName = questionResultName;
	}

	public BigDecimal getQuestionGroupSumScore() {
		return questionGroupSumScore;
	}

	public void setQuestionGroupSumScore(BigDecimal questionGroupSumScore) {
		this.questionGroupSumScore = questionGroupSumScore;
	}

	public Integer getQuestionGroupRightNum() {
		return questionGroupRightNum;
	}

	public void setQuestionGroupRightNum(Integer questionGroupRightNum) {
		this.questionGroupRightNum = questionGroupRightNum;
	}

	public Integer getQuestionGroupErrorNum() {
		return questionGroupErrorNum;
	}

	public void setQuestionGroupErrorNum(Integer questionGroupErrorNum) {
		this.questionGroupErrorNum = questionGroupErrorNum;
	}

	public QuestionResponse getQuestionResponse() {
		return questionResponse;
	}

	public void setQuestionResponse(QuestionResponse questionResponse) {
		this.questionResponse = questionResponse;
	}

	public String getMark() {
		return mark;
	}

	public void setMark(String mark) {
		this.mark = mark;
	}

	public Integer getQuestionTemplateItemId() {
		return questionTemplateItemId;
	}

	public void setQuestionTemplateItemId(Integer questionTemplateItemId) {
		this.questionTemplateItemId = questionTemplateItemId;
	}

	public List<UserExamPaperRecordDetailResponse> getChildren() {
		return children;
	}

	public void setChildren(List<UserExamPaperRecordDetailResponse> children) {
		this.children = children;
	}

	public String getQuestionTemplateItemName() {
		return questionTemplateItemName;
	}

	public void setQuestionTemplateItemName(String questionTemplateItemName) {
		this.questionTemplateItemName = questionTemplateItemName;
	}

	public Integer getQuestionTemplateItemSort() {
		return questionTemplateItemSort;
	}

	public void setQuestionTemplateItemSort(Integer questionTemplateItemSort) {
		this.questionTemplateItemSort = questionTemplateItemSort;
	}

	public List<UserExamPaperRecordScoreResponse> getUserExamPaperRecordScoreResponseList() {
		return userExamPaperRecordScoreResponseList;
	}

	public void setUserExamPaperRecordScoreResponseList(List<UserExamPaperRecordScoreResponse> userExamPaperRecordScoreResponseList) {
		this.userExamPaperRecordScoreResponseList = userExamPaperRecordScoreResponseList;
	}

	public String getCustomerImgUrl() {
		return customerImgUrl;
	}

	public void setCustomerImgUrl(String customerImgUrl) {
		this.customerImgUrl = customerImgUrl;
	}
}
