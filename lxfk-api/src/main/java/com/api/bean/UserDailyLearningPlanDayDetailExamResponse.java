package com.api.bean;

import java.util.List;
import java.util.Objects;

import com.common.bean.Bean;

/**
 * 用户每日学习计划详情-试卷
 */
public class UserDailyLearningPlanDayDetailExamResponse extends Bean {
	private static final long serialVersionUID = 1L;

	/**
	 * 科目
	 */
	private Integer projectItemId;
	private String projectItemName;
	private String projectItemColor;
	/**
	 * 试卷类型
	 */
	private Integer examTypeId;
	private String examTypeName;
	/**
	 * 试卷类型-子级
	 */
	private Integer examTypeChildrenId;
	private String examTypeChildrenName;
	/**
	 * 试卷标签
	 */
	private Integer questionLabelId;
	private String questionLabelName;
	/**
	 * 试卷数量
	 */
	private Integer examPaperCount;

	/**
	 * 试卷列表
	 */
	List<ExamPaperResponse> examPaperResponseList;

	@Override
	public boolean equals(Object o) {
		if (!(o instanceof UserDailyLearningPlanDayDetailExamResponse)){
			return false;
		}
		UserDailyLearningPlanDayDetailExamResponse that = (UserDailyLearningPlanDayDetailExamResponse) o;
		return Objects.equals(projectItemId, that.projectItemId) && Objects.equals(examTypeId, that.examTypeId) && Objects.equals(questionLabelId, that.questionLabelId) && Objects.equals(examTypeChildrenId, that.examTypeChildrenId);
	}

	@Override
	public int hashCode() {
		return Objects.hash(projectItemId, examTypeId, questionLabelId, examTypeChildrenId);
	}

	public Integer getProjectItemId() {
		return projectItemId;
	}

	public void setProjectItemId(Integer projectItemId) {
		this.projectItemId = projectItemId;
	}

	public String getProjectItemName() {
		return projectItemName;
	}

	public void setProjectItemName(String projectItemName) {
		this.projectItemName = projectItemName;
	}

	public Integer getExamTypeId() {
		return examTypeId;
	}

	public void setExamTypeId(Integer examTypeId) {
		this.examTypeId = examTypeId;
	}

	public String getExamTypeName() {
		return examTypeName;
	}

	public void setExamTypeName(String examTypeName) {
		this.examTypeName = examTypeName;
	}

	public Integer getQuestionLabelId() {
		return questionLabelId;
	}

	public void setQuestionLabelId(Integer questionLabelId) {
		this.questionLabelId = questionLabelId;
	}

	public String getQuestionLabelName() {
		return questionLabelName;
	}

	public void setQuestionLabelName(String questionLabelName) {
		this.questionLabelName = questionLabelName;
	}

	public Integer getExamPaperCount() {
		return examPaperCount;
	}

	public void setExamPaperCount(Integer examPaperCount) {
		this.examPaperCount = examPaperCount;
	}

	public List<ExamPaperResponse> getExamPaperResponseList() {
		return examPaperResponseList;
	}

	public void setExamPaperResponseList(List<ExamPaperResponse> examPaperResponseList) {
		this.examPaperResponseList = examPaperResponseList;
	}

	public String getExamTypeChildrenName() {
		return examTypeChildrenName;
	}

	public void setExamTypeChildrenName(String examTypeChildrenName) {
		this.examTypeChildrenName = examTypeChildrenName;
	}

	public Integer getExamTypeChildrenId() {
		return examTypeChildrenId;
	}

	public void setExamTypeChildrenId(Integer examTypeChildrenId) {
		this.examTypeChildrenId = examTypeChildrenId;
	}

	public String getProjectItemColor() {
		return projectItemColor;
	}

	public void setProjectItemColor(String projectItemColor) {
		this.projectItemColor = projectItemColor;
	}
}
