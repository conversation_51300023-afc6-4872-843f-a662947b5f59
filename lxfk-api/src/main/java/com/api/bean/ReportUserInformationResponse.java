package com.api.bean;

import java.math.BigDecimal;

import com.api.constant.App;
import com.common.bean.Bean;
import com.common.util.DateUtil;
import com.domain.ReportUserInformation;

/**
 * 用户信息统计表返回值对象
 *
 * @date 2025-08-12 11:17:25
 */
public class ReportUserInformationResponse extends Bean {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	private Integer id;

	/**
	 * 用户编号
	 */
	private Integer userId;

	/**
	 * 产品编号
	 */
	private String productIds;

	/**
	 * 实付总金额
	 */
	private BigDecimal sumAmount;

	/**
	 * 实际退费金额
	 */
	private BigDecimal sumRefundAmount;

	/**
	 * 挽单金额
	 */
	private BigDecimal sumRetrieveAmount;

	/**
	 * 回访是否有异议(0:是 1:否 2:空 3:无需回访 4:未接)
	 */
	private String callbackObjection;

	/**
	 * 客诉数量
	 */
	private Integer complaintCount;

	/**
	 * 售后工单状态
	 */
	private String afterSalesWorkOrderStage;

	/**
	 * 学员投诉内容
	 */
	private String complaintContent;

	/**
	 * 状态(0:正常 1:无效)
	 */
	private String status;

	/**
	 * 修改时间
	 */
	private String modifyTime;

	/**
	 * 创建时间
	 */
	private String createTime;

	public ReportUserInformationResponse(){}

	public ReportUserInformationResponse(ReportUserInformation reportUserInformation){
		this.id = reportUserInformation.getId();
		this.userId = reportUserInformation.getUserId();
		this.productIds = reportUserInformation.getProductIds();
		this.sumAmount = reportUserInformation.getSumAmount();
		this.sumRefundAmount = reportUserInformation.getSumRefundAmount();
		this.sumRetrieveAmount = reportUserInformation.getSumRetrieveAmount();
		this.callbackObjection = reportUserInformation.getCallbackObjection();
		this.complaintCount = reportUserInformation.getComplaintCount();
		this.afterSalesWorkOrderStage = reportUserInformation.getAfterSalesWorkOrderStage();
		this.complaintContent = reportUserInformation.getComplaintContent();
		this.status = reportUserInformation.getStatus();
		if (reportUserInformation.getModifyTime() != null){
			this.modifyTime = DateUtil.format(reportUserInformation.getModifyTime(), App.DATETIME_FORMAT);
		}
		if (reportUserInformation.getCreateTime() != null){
			this.createTime = DateUtil.format(reportUserInformation.getCreateTime(), App.DATETIME_FORMAT);
		}
	}

	public Integer getId (){
		return id;
	}

	public void setId (Integer id) {
		this.id = id;
	}

	public Integer getUserId (){
		return userId;
	}

	public void setUserId (Integer userId) {
		this.userId = userId;
	}

	public String getProductIds (){
		return productIds;
	}

	public void setProductIds (String productIds) {
		this.productIds = productIds;
	}

	public BigDecimal getSumAmount (){
		return sumAmount;
	}

	public void setSumAmount (BigDecimal sumAmount) {
		this.sumAmount = sumAmount;
	}

	public BigDecimal getSumRefundAmount (){
		return sumRefundAmount;
	}

	public void setSumRefundAmount (BigDecimal sumRefundAmount) {
		this.sumRefundAmount = sumRefundAmount;
	}

	public BigDecimal getSumRetrieveAmount (){
		return sumRetrieveAmount;
	}

	public void setSumRetrieveAmount (BigDecimal sumRetrieveAmount) {
		this.sumRetrieveAmount = sumRetrieveAmount;
	}

	public String getCallbackObjection (){
		return callbackObjection;
	}

	public void setCallbackObjection (String callbackObjection) {
		this.callbackObjection = callbackObjection;
	}

	public Integer getComplaintCount (){
		return complaintCount;
	}

	public void setComplaintCount (Integer complaintCount) {
		this.complaintCount = complaintCount;
	}

	public String getAfterSalesWorkOrderStage (){
		return afterSalesWorkOrderStage;
	}

	public void setAfterSalesWorkOrderStage (String afterSalesWorkOrderStage) {
		this.afterSalesWorkOrderStage = afterSalesWorkOrderStage;
	}

	public String getComplaintContent (){
		return complaintContent;
	}

	public void setComplaintContent (String complaintContent) {
		this.complaintContent = complaintContent;
	}

	public String getStatus (){
		return status;
	}

	public void setStatus (String status) {
		this.status = status;
	}

	public String getModifyTime (){
		return modifyTime;
	}

	public void setModifyTime (String modifyTime) {
		this.modifyTime = modifyTime;
	}

	public String getCreateTime (){
		return createTime;
	}

	public void setCreateTime (String createTime) {
		this.createTime = createTime;
	}

}
