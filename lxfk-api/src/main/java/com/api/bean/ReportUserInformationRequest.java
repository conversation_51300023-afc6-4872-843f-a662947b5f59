package com.api.bean;

import java.math.BigDecimal;
import java.util.List;

/**
 * 用户信息统计表请求对象
 *
 * @date 2025-08-12 11:17:25
 */
public class ReportUserInformationRequest extends PageRequest {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	private Integer id;

	/**
	 * 主键集合
	 */
	private List<Integer> ids;

	/**
	 * 用户编号
	 */
	private Integer userId;

	/**
	 * 用户编号集合
	 */
	private List<Integer> userIds;

	/**
	 * 产品编号
	 */
	private String productIds;

	/**
	 * 产品编号集合
	 */
	private List<String> productIdss;

	/**
	 * 实付总金额
	 */
	private BigDecimal sumAmount;

	/**
	 * 实际退费金额
	 */
	private BigDecimal sumRefundAmount;

	/**
	 * 挽单金额
	 */
	private BigDecimal sumRetrieveAmount;

	/**
	 * 回访是否有异议(0:是 1:否 2:空 3:无需回访 4:未接)
	 */
	private String callbackObjection;

	/**
	 * 客诉数量
	 */
	private Integer complaintCount;

	/**
	 * 售后工单状态
	 */
	private String afterSalesWorkOrderStage;

	/**
	 * 学员投诉内容
	 */
	private String complaintContent;

	/**
	 * 状态(0:正常 1:无效)
	 */
	private String status;

	/**
	 * 修改时间
	 */
	private String modifyTime;

	/**
	 * 最小修改时间
	 */
	private String minModifyTime;

	/**
	 * 最大修改时间
	 */
	private String maxModifyTime;

	/**
	 * 创建时间
	 */
	private String createTime;

	/**
	 * 最小创建时间
	 */
	private String minCreateTime;

	/**
	 * 最大创建时间
	 */
	private String maxCreateTime;


	public Integer getId (){
		return id;
	}

	public void setId (Integer id) {
		this.id = id;
	}

	public List<Integer> getIds (){
		return ids;
	}

	public void setIds (List<Integer> ids) {
		this.ids = ids;
	}

	public Integer getUserId (){
		return userId;
	}

	public void setUserId (Integer userId) {
		this.userId = userId;
	}

	public List<Integer> getUserIds (){
		return userIds;
	}

	public void setUserIds (List<Integer> userIds) {
		this.userIds = userIds;
	}

	public String getProductIds (){
		return productIds;
	}

	public void setProductIds (String productIds) {
		this.productIds = productIds;
	}

	public List<String> getProductIdss (){
		return productIdss;
	}

	public void setProductIdss (List<String> productIdss) {
		this.productIdss = productIdss;
	}

	public BigDecimal getSumAmount (){
		return sumAmount;
	}

	public void setSumAmount (BigDecimal sumAmount) {
		this.sumAmount = sumAmount;
	}

	public BigDecimal getSumRefundAmount (){
		return sumRefundAmount;
	}

	public void setSumRefundAmount (BigDecimal sumRefundAmount) {
		this.sumRefundAmount = sumRefundAmount;
	}

	public BigDecimal getSumRetrieveAmount (){
		return sumRetrieveAmount;
	}

	public void setSumRetrieveAmount (BigDecimal sumRetrieveAmount) {
		this.sumRetrieveAmount = sumRetrieveAmount;
	}

	public String getCallbackObjection (){
		return callbackObjection;
	}

	public void setCallbackObjection (String callbackObjection) {
		this.callbackObjection = callbackObjection;
	}

	public Integer getComplaintCount (){
		return complaintCount;
	}

	public void setComplaintCount (Integer complaintCount) {
		this.complaintCount = complaintCount;
	}

	public String getAfterSalesWorkOrderStage (){
		return afterSalesWorkOrderStage;
	}

	public void setAfterSalesWorkOrderStage (String afterSalesWorkOrderStage) {
		this.afterSalesWorkOrderStage = afterSalesWorkOrderStage;
	}

	public String getComplaintContent (){
		return complaintContent;
	}

	public void setComplaintContent (String complaintContent) {
		this.complaintContent = complaintContent;
	}

	public String getStatus (){
		return status;
	}

	public void setStatus (String status) {
		this.status = status;
	}

	public String getModifyTime (){
		return modifyTime;
	}

	public void setModifyTime (String modifyTime) {
		this.modifyTime = modifyTime;
	}

	public String getMinModifyTime (){
		return minModifyTime;
	}

	public void setMinModifyTime (String minModifyTime) {
		this.minModifyTime = minModifyTime;
	}

	public String getMaxModifyTime (){
		return maxModifyTime;
	}

	public void setMaxModifyTime (String maxModifyTime) {
		this.maxModifyTime = maxModifyTime;
	}

	public String getCreateTime (){
		return createTime;
	}

	public void setCreateTime (String createTime) {
		this.createTime = createTime;
	}

	public String getMinCreateTime (){
		return minCreateTime;
	}

	public void setMinCreateTime (String minCreateTime) {
		this.minCreateTime = minCreateTime;
	}

	public String getMaxCreateTime (){
		return maxCreateTime;
	}

	public void setMaxCreateTime (String maxCreateTime) {
		this.maxCreateTime = maxCreateTime;
	}

}
