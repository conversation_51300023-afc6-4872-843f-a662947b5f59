package com.api.bean;

import com.api.constant.App;
import com.common.bean.Bean;
import com.common.util.DateUtil;
import com.domain.UserInformationCommentFlow;

/**
 * 用户信息备注明细表返回值对象
 *
 * @date 2025-08-11 17:24:47
 */
public class UserInformationCommentFlowResponse extends Bean {
	private static final long serialVersionUID = 1L;

	/*
	 * 主键
	 */
	private Integer id;

	/*
	 * 用户编号
	 */
	private Integer userId;

	/*
	 * 备注
	 */
	private String comment;

	/*
	 * 图片地址
	 */
	private String imgUrl;

	/*
	 * 操作人编号
	 */
	private Integer sysUserId;

	/*
	 * 数据类型(1000:舆情记录 1001:推班记录 1002:群服务情况记录 1003:备注)
	 */
	private String catalog;

	/*
	 * 状态(0:正常 1:无效)
	 */
	private String status;

	/*
	 * 修改时间
	 */
	private String modifyTime;

	/*
	 * 创建时间
	 */
	private String createTime;

	public UserInformationCommentFlowResponse(){}

	public UserInformationCommentFlowResponse(UserInformationCommentFlow userInformationCommentFlow){
		this.id = userInformationCommentFlow.getId();
		this.userId = userInformationCommentFlow.getUserId();
		this.comment = userInformationCommentFlow.getComment();
		this.imgUrl = userInformationCommentFlow.getImgUrl();
		this.sysUserId = userInformationCommentFlow.getSysUserId();
		this.catalog = userInformationCommentFlow.getCatalog();
		this.status = userInformationCommentFlow.getStatus();
		if (userInformationCommentFlow.getModifyTime() != null){
			this.modifyTime = DateUtil.format(userInformationCommentFlow.getModifyTime(), App.DATETIME_FORMAT);
		}
		if (userInformationCommentFlow.getCreateTime() != null){
			this.createTime = DateUtil.format(userInformationCommentFlow.getCreateTime(), App.DATETIME_FORMAT);
		}
	}

	public Integer getId (){
		return id;
	}

	public void setId (Integer id) {
		this.id = id;
	}

	public Integer getUserId (){
		return userId;
	}

	public void setUserId (Integer userId) {
		this.userId = userId;
	}

	public String getComment (){
		return comment;
	}

	public void setComment (String comment) {
		this.comment = comment;
	}

	public String getImgUrl (){
		return imgUrl;
	}

	public void setImgUrl (String imgUrl) {
		this.imgUrl = imgUrl;
	}

	public Integer getSysUserId (){
		return sysUserId;
	}

	public void setSysUserId (Integer sysUserId) {
		this.sysUserId = sysUserId;
	}

	public String getCatalog (){
		return catalog;
	}

	public void setCatalog (String catalog) {
		this.catalog = catalog;
	}

	public String getStatus (){
		return status;
	}

	public void setStatus (String status) {
		this.status = status;
	}

	public String getModifyTime (){
		return modifyTime;
	}

	public void setModifyTime (String modifyTime) {
		this.modifyTime = modifyTime;
	}

	public String getCreateTime (){
		return createTime;
	}

	public void setCreateTime (String createTime) {
		this.createTime = createTime;
	}

}
