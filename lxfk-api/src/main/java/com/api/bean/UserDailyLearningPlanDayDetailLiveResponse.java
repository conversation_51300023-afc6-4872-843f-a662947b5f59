package com.api.bean;

import com.common.bean.Bean;

/**
 * 直播信息
 *
 * @date 2025-08-04 16:56:21
 */
public class UserDailyLearningPlanDayDetailLiveResponse extends Bean {
	private static final long serialVersionUID = 1L;


	/**
	 * 直播id
	 */
	private Integer id;
	/**
	 * 直播编号
	 */
	private String liveId;
	/**
	 * 直播间编号
	 */
	private String roomId;

	/**
	 * 科目编号
	 */
	private Integer projectItemId;

	/**
	 * 开始时间
	 */
	private String startTime;

	/**
	 * 结束时间
	 */
	private String endTime;

	/**
	 * 直播间名称
	 */
	private String liveName;

	/**
	 * 讲师名称
	 */
	private String authorName;


	public String getLiveId() {
		return liveId;
	}

	public void setLiveId(String liveId) {
		this.liveId = liveId;
	}

	public String getRoomId() {
		return roomId;
	}

	public void setRoomId(String roomId) {
		this.roomId = roomId;
	}

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Integer getProjectItemId() {
		return projectItemId;
	}

	public void setProjectItemId(Integer projectItemId) {
		this.projectItemId = projectItemId;
	}

	public String getStartTime() {
		return startTime;
	}

	public void setStartTime(String startTime) {
		this.startTime = startTime;
	}

	public String getEndTime() {
		return endTime;
	}

	public void setEndTime(String endTime) {
		this.endTime = endTime;
	}

	public String getLiveName() {
		return liveName;
	}

	public void setLiveName(String liveName) {
		this.liveName = liveName;
	}

	public String getAuthorName() {
		return authorName;
	}

	public void setAuthorName(String authorName) {
		this.authorName = authorName;
	}
}
