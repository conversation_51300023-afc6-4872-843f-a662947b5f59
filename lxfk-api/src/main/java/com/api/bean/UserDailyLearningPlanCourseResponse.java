package com.api.bean;

import com.common.bean.Bean;

/**
 * 用户每月学习计划课程表
 *
 * @date 2025-07-28 14:15:56
 */
public class UserDailyLearningPlanCourseResponse extends Bean {
	private static final long serialVersionUID = 1L;


	/**
	 * 视频名称
	 */
	private String courseName;
	/**
	 * 长度
	 */
	private String lengthStr;
	/**
	 * 科目名称
	 */
	private String projectItemName;
	/**
	 * 课程分类
	 */
	private String courseCatalogName;

	private Integer id;
	/**
	 * 用户每月学习计划Id
	 */
	private Integer userDailyLearningPlanId;
	/**
	 * 视频编号
	 */
	private Integer courseId;
	/**
	 * 分配状态(0:未分配 1:已分配)
	 */
	private String allocationStage;
	private String allocationStageName;
	/**
	 * 排序
	 */
	private Integer sort;

	private String status;

	private String modifyTime;

	private String createTime;
	/**
	 * 课程分类编号
	 */
	private Integer courseCatalogId;


	public Integer getUserDailyLearningPlanId (){
		return userDailyLearningPlanId;
	}

	public void setUserDailyLearningPlanId (Integer userDailyLearningPlanId) {
		this.userDailyLearningPlanId = userDailyLearningPlanId;
	}

	public Integer getCourseId (){
		return courseId;
	}

	public void setCourseId (Integer courseId) {
		this.courseId = courseId;
	}

	public String getAllocationStage (){
		return allocationStage;
	}

	public void setAllocationStage (String allocationStage) {
		this.allocationStage = allocationStage;
	}

	public Integer getSort() {
		return sort;
	}

	public void setSort(Integer sort) {
		this.sort = sort;
	}

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getModifyTime() {
		return modifyTime;
	}

	public void setModifyTime(String modifyTime) {
		this.modifyTime = modifyTime;
	}

	public String getCreateTime() {
		return createTime;
	}

	public void setCreateTime(String createTime) {
		this.createTime = createTime;
	}

	public Integer getCourseCatalogId() {
		return courseCatalogId;
	}

	public void setCourseCatalogId(Integer courseCatalogId) {
		this.courseCatalogId = courseCatalogId;
	}

	public String getProjectItemName() {
		return projectItemName;
	}

	public void setProjectItemName(String projectItemName) {
		this.projectItemName = projectItemName;
	}

	public String getCourseCatalogName() {
		return courseCatalogName;
	}

	public void setCourseCatalogName(String courseCatalogName) {
		this.courseCatalogName = courseCatalogName;
	}

	public String getLengthStr() {
		return lengthStr;
	}

	public void setLengthStr(String lengthStr) {
		this.lengthStr = lengthStr;
	}

	public String getCourseName() {
		return courseName;
	}

	public void setCourseName(String courseName) {
		this.courseName = courseName;
	}

	public String getAllocationStageName() {
		return allocationStageName;
	}

	public void setAllocationStageName(String allocationStageName) {
		this.allocationStageName = allocationStageName;
	}
}
