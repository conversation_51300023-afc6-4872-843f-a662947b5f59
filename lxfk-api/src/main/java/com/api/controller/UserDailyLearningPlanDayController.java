package com.api.controller;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.api.bean.CourseCatalogResponse;
import com.api.bean.CourseResponse;
import com.api.bean.ExamPaperResponse;
import com.api.bean.LiveResponse;
import com.api.bean.NewPageResponse;
import com.api.bean.ProjectItemResponse;
import com.api.bean.Response;
import com.api.bean.UserDailyLearningPlanDayDetailExamResponse;
import com.api.bean.UserDailyLearningPlanDayDetailLiveResponse;
import com.api.bean.UserDailyLearningPlanDayRequest;
import com.api.bean.UserDailyLearningPlanDayResponse;
import com.api.config.Token;
import com.api.constant.App;
import com.api.constant.DataStatus;
import com.api.validator.UserDailyLearningPlanDayValidator;
import com.common.bean.Pager;
import com.common.constant.LiveSubscribe;
import com.common.constant.PublicOver;
import com.common.constant.PublicStage;
import com.common.constant.UserDailyLearningPlanCourseAllocationStage;
import com.common.constant.UserDailyLearningPlanDayDayType;
import com.common.constant.UserDailyLearningPlanDayDetailTaskType;
import com.common.constant.UserDailyLearningPlanProjectItemColor;
import com.common.constant.UserDailyLearningPlanRestStudyTime;
import com.common.constant.UserDailyLearningPlanSeeLive;
import com.common.constant.UserDailyLearningPlanStage;
import com.common.constant.UserDailyLearningPlanWeekdayStudyTime;
import com.common.constant.UserExamPaperRecordCatalog;
import com.common.constant.UserExamPaperRecordOver;
import com.common.constant.UserExamPaperRecordStatus;
import com.common.util.DateUtil;
import com.common.util.NumberUtil;
import com.domain.Base;
import com.domain.Course;
import com.domain.CourseCatalog;
import com.domain.ExamPaper;
import com.domain.ExamPaperQuestion;
import com.domain.ExamType;
import com.domain.LiveSubscription;
import com.domain.ProjectItem;
import com.domain.QuestionLabel;
import com.domain.UserCourseRecord;
import com.domain.UserDailyLearningPlan;
import com.domain.UserDailyLearningPlanCourse;
import com.domain.UserDailyLearningPlanDay;
import com.domain.UserDailyLearningPlanDayDetail;
import com.domain.UserExamPaperRecord;
import com.domain.UserLiveLogs;
import com.domain.complex.CourseCatalogQuery;
import com.domain.complex.CourseQuery;
import com.domain.complex.ExamPaperQuery;
import com.domain.complex.ExamPaperQuestionQuery;
import com.domain.complex.ExamTypeQuery;
import com.domain.complex.LiveSubscriptionQuery;
import com.domain.complex.ProjectItemQuery;
import com.domain.complex.UserCourseRecordQuery;
import com.domain.complex.QuestionLabelQuery;
import com.domain.complex.UserDailyLearningPlanCourseQuery;
import com.domain.complex.UserDailyLearningPlanDayDetailQuery;
import com.domain.complex.UserDailyLearningPlanDayQuery;
import com.domain.complex.UserDailyLearningPlanQuery;
import com.domain.complex.UserExamPaperRecordQuery;
import com.domain.complex.UserLiveLogsQuery;
import com.service.CourseCatalogService;
import com.service.CourseService;
import com.service.ExamPaperQuestionService;
import com.service.ExamPaperService;
import com.service.ExamTypeService;
import com.service.LiveSubscriptionService;
import com.service.ProjectItemService;
import com.service.UserCourseRecordService;
import com.service.QuestionLabelService;
import com.service.UserDailyLearningPlanCourseServiceImpl;
import com.service.UserDailyLearningPlanDayDetailService;
import com.service.UserDailyLearningPlanDayService;
import com.service.UserDailyLearningPlanService;
import com.service.UserExamPaperRecordDetailService;
import com.service.UserExamPaperRecordService;
import com.service.UserLiveLogsService;


/**
 * 用户每日学习计划表
 */
@RestController
public class UserDailyLearningPlanDayController extends BaseController {
    @Autowired
    private UserDailyLearningPlanDayService userDailyLearningPlanDayService;
    @Autowired
    private UserDailyLearningPlanCourseServiceImpl userDailyLearningPlanCourseService;
    @Autowired
    private UserDailyLearningPlanService userDailyLearningPlanService;
    @Autowired
    private ProjectItemService projectItemService;
    @Autowired
    private UserDailyLearningPlanDayDetailService userDailyLearningPlanDayDetailService;
    @Autowired
    private CourseService courseService;
    @Autowired
    private ExamPaperService examPaperService;
    @Autowired
    private LiveSubscriptionService liveSubscriptionService;
    @Autowired
    private UserLiveLogsService userLiveLogsService;
    @Autowired
    private ExamPaperQuestionService examPaperQuestionService;
    @Autowired
    private UserExamPaperRecordService userExamPaperRecordService;
    @Autowired
    private UserExamPaperRecordDetailService userExamPaperRecordDetailService;
    @Autowired
    private CourseCatalogService courseCatalogService;
    @Autowired
    private ExamTypeService examTypeService;
    @Autowired
    private QuestionLabelService questionLabelService;
    @Autowired
    private UserCourseRecordService userCourseRecordService;

    /**
     * 设置日期类型(只能修改明天及以后的),更新绑定的每日学习计划,重新分配视频
     *
     * <AUTHOR>
     * @date 2025-07-29
     */
    @RequestMapping("/v1/user/daily/learning/plain/day/type/modify")
    public Response<?> modify(@RequestBody UserDailyLearningPlanDayRequest request) {
        try {
            Integer id = request.getId();
            String dayType = request.getDayType();
            UserDailyLearningPlanDayValidator validator = new UserDailyLearningPlanDayValidator();
            if (!validator.onId(id).onDayType(dayType).result()) {
                return new Response<>(ERROR, validator.getErrorMessage());
            }

            Date planDay;
            UserDailyLearningPlanDay userDailyLearningPlanDay = userDailyLearningPlanDayService.findById(id);
            if (isEmpty(userDailyLearningPlanDay) || !DataStatus.Y.getCode().equals(userDailyLearningPlanDay.getStatus()) || (planDay = userDailyLearningPlanDay.getPlanDay()) == null) {
                return new Response<>(ERROR, "用户日计划数据有误");
            }

            //1.校验: 只能修改明天及以后的任务量
            Date serverTime = getServerTime();
            if (planDay.getTime() < serverTime.getTime()) {
                return new Response<>(ERROR, "只能修改明天及以后的数据");
            }

            //2.查询月度计划
            UserDailyLearningPlanQuery userDailyLearningPlanQuery = new UserDailyLearningPlanQuery();
            userDailyLearningPlanQuery.setStatus(DataStatus.Y.getCode());
            userDailyLearningPlanQuery.setId(userDailyLearningPlanDay.getUserDailyLearningPlanId());
            List<UserDailyLearningPlan> userDailyLearningPlanList = userDailyLearningPlanService.findAll(userDailyLearningPlanQuery);
            if (isEmpty(userDailyLearningPlanList) || userDailyLearningPlanList.isEmpty()) {
                return new Response<>(ERROR, "用户月计划数据有误");
            }
            UserDailyLearningPlan userDailyLearningPlan = userDailyLearningPlanList.get(0);

            //3.设置当日计划(更新对象)
            UserDailyLearningPlanDay userDailyLearningPlanDayModify = new UserDailyLearningPlanDay();
            userDailyLearningPlanDayModify.setId(id);
            //日期类型
            userDailyLearningPlanDayModify.setDayType(dayType);
            //任务时长
            Integer taskLength = 0;
            if (UserDailyLearningPlanDayDayType.WEEKDAY.getCode().equals(dayType)) {
                UserDailyLearningPlanWeekdayStudyTime userDailyLearningPlanWeekdayStudyTime = UserDailyLearningPlanWeekdayStudyTime.get(userDailyLearningPlan.getWeekdayStudyTime());
                if (userDailyLearningPlanWeekdayStudyTime != null) {
                    taskLength = userDailyLearningPlanWeekdayStudyTime.getMin();
                }
            } else if (UserDailyLearningPlanDayDayType.RESET.getCode().equals(dayType)) {
                UserDailyLearningPlanRestStudyTime userDailyLearningPlanRestStudyTime = UserDailyLearningPlanRestStudyTime.get(userDailyLearningPlan.getRestStudyTime());
                if (userDailyLearningPlanRestStudyTime != null) {
                    taskLength = userDailyLearningPlanRestStudyTime.getMin();
                }
            }
            userDailyLearningPlanDayModify.setTaskLength(taskLength);
            userDailyLearningPlanDayModify.setModifyTime(serverTime);

            //4.持久化(并重新分配视频)
            userDailyLearningPlanDayService.modifyById(userDailyLearningPlanDayModify, true);
            return new Response<>(OK, SUCCESS);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }


    /**
     * 查询时间段内用户的每日学习计划完成情况
     *
     * <AUTHOR>
     */
    @RequestMapping(value = "/v1/user/daily/learning/plan/day/time/slot/query")
    @Token
    public Response<?> queryTimeSlot(@RequestBody UserDailyLearningPlanDayRequest request) {
        try {
            Date datetime = this.getServerTime();
            List<UserDailyLearningPlanDayResponse> responses = new ArrayList<>();
            Integer customerId = this.getUserToken().getCustomerId();
            if (!this.isEmpty(request.getCustomerId())){
                customerId = request.getCustomerId();
            }
            UserDailyLearningPlanDayValidator validator = new UserDailyLearningPlanDayValidator();
            if (!validator.onMinPlanDay(request.getMinPlanDay()).onMaxPlanDay(request.getMaxPlanDay()).result()) {
                return new Response<>(ERROR, validator.getErrorMessage());
            }
            if (DateUtil.isSameDate(DateUtil.parse(request.getMinPlanDay(), DATE_FORMAT), DateUtil.parse(request.getMaxPlanDay(), DATE_FORMAT))) {
                return new Response<>(ERROR, "时间区间不能是同一天");
            }
            List<Date> moths = new ArrayList<>();
            moths.add(DateUtil.getMonthStartTime(DateUtil.parse(request.getMinPlanDay(), DATE_FORMAT)));
            moths.add(DateUtil.getMonthStartTime(DateUtil.parse(request.getMaxPlanDay(), DATE_FORMAT)));
            //查询用户对应项目的 每日学习计划
            UserDailyLearningPlanQuery userDailyLearningPlanQuery = new UserDailyLearningPlanQuery();
            userDailyLearningPlanQuery.setMonths(moths);
            userDailyLearningPlanQuery.setProjectId(request.getProjectId());
            userDailyLearningPlanQuery.setCustomerId(customerId);
            userDailyLearningPlanQuery.setStatus(DataStatus.Y.getCode());
            List<UserDailyLearningPlan> userDailyLearningPlanList = this.userDailyLearningPlanService.findAll(userDailyLearningPlanQuery);
            if (userDailyLearningPlanList == null || userDailyLearningPlanList.isEmpty()) {
                return new Response<>(ERROR, "未找到用户的学习计划日历");
            }
            //获取这两个时间段内的所有日期
            List<String> calendar = DateUtil.getCalendar(request.getMinPlanDay(), request.getMaxPlanDay(), DATE_FORMAT);
            List<Integer> userDailyLearningPlanIds = userDailyLearningPlanList.stream().map(Base::getId).collect(Collectors.toList());
            //构建查询参数
            UserDailyLearningPlanDayQuery userDailyLearningPlanDayQuery = new UserDailyLearningPlanDayQuery();
            userDailyLearningPlanDayQuery.setUserDailyLearningPlanIds(userDailyLearningPlanIds);
            userDailyLearningPlanDayQuery.setCustomerId(customerId);
            userDailyLearningPlanDayQuery.setMinPlanDay(DateUtil.parse(request.getMinPlanDay(), DATE_FORMAT));
            userDailyLearningPlanDayQuery.setMaxPlanDay(DateUtil.getFutureDay(DateUtil.parse(request.getMaxPlanDay(), DATE_FORMAT), 1));
            userDailyLearningPlanDayQuery.setStatus(DataStatus.Y.getCode());
            List<UserDailyLearningPlanDay> userDailyLearningPlanDays = this.userDailyLearningPlanDayService.findAll(userDailyLearningPlanDayQuery);
            //按日期分组
            Map<Date, List<UserDailyLearningPlanDay>> userDailyLearningPlanDayMap = userDailyLearningPlanDays
                    .stream().collect(Collectors.groupingBy(UserDailyLearningPlanDay::getPlanDay));
            //取出id
            List<Integer> userDailyLearningDayIds = userDailyLearningPlanDays.stream().map(UserDailyLearningPlanDay::getId).collect(Collectors.toList());
            //查询每日绑定的任务
            Map<Integer, List<UserDailyLearningPlanDayDetail>> UserDailyLearningPlanDayDetailMap = new HashMap<>();
            if (!this.isEmpty(userDailyLearningDayIds) && !userDailyLearningDayIds.isEmpty()) {
                UserDailyLearningPlanDayDetailQuery userDailyLearningPlanDayDetailQuery = new UserDailyLearningPlanDayDetailQuery();
                userDailyLearningPlanDayDetailQuery.setUserDailyLearningPlanDayIds(userDailyLearningDayIds);
                userDailyLearningPlanDayDetailQuery.setStatus(DataStatus.Y.getCode());
                List<UserDailyLearningPlanDayDetail> userDailyLearningPlanDayDetails =
                        this.userDailyLearningPlanDayDetailService.findAll(userDailyLearningPlanDayDetailQuery);
                //按日期明细表id分组
                UserDailyLearningPlanDayDetailMap = userDailyLearningPlanDayDetails
                        .stream().collect(Collectors.groupingBy(UserDailyLearningPlanDayDetail::getUserDailyLearningPlanDayId));
            }

            for (String date : calendar) {
                UserDailyLearningPlanDayResponse userDailyLearningPlanDayResponse = new UserDailyLearningPlanDayResponse();
                userDailyLearningPlanDayResponse.setPlanDay(date);
                if (userDailyLearningPlanDayMap.containsKey(DateUtil.parse(date, DATE_FORMAT))) {
                    List<UserDailyLearningPlanDay> userDailyLearningPlanDayList = userDailyLearningPlanDayMap.get(DateUtil.parse(date, DATE_FORMAT));
                    if (userDailyLearningPlanDayList == null || userDailyLearningPlanDayList.isEmpty()) {
                        userDailyLearningPlanDayResponse.setOver(UserDailyLearningPlanStage.C3.getCode());
                        userDailyLearningPlanDayResponse.setOverName(UserDailyLearningPlanStage.C3.getName());
                    } else {
                        List<UserDailyLearningPlanDayDetail> userDailyLearningPlanDayDetails =
                                UserDailyLearningPlanDayDetailMap.get(userDailyLearningPlanDayList.get(0).getId());
                        if (userDailyLearningPlanDayDetails == null || userDailyLearningPlanDayDetails.isEmpty()) {
                            userDailyLearningPlanDayResponse.setOver(UserDailyLearningPlanStage.C3.getCode());
                            userDailyLearningPlanDayResponse.setOverName(UserDailyLearningPlanStage.C3.getName());
                        } else {
                            if (PublicOver.Y.getCode().equals(userDailyLearningPlanDayList.get(0).getOver())) {
                                userDailyLearningPlanDayResponse.setOver(UserDailyLearningPlanStage.C1.getCode());
                                userDailyLearningPlanDayResponse.setOverName(UserDailyLearningPlanStage.C1.getName());
                            } else {
                                //比较时间 早于和等于当前时间统一是未完成学习任务
                                if (DateUtil.parse(date, DATE_FORMAT).compareTo(datetime) <= 0) {
                                    userDailyLearningPlanDayResponse.setOver(UserDailyLearningPlanStage.C2.getCode());
                                    userDailyLearningPlanDayResponse.setOverName(UserDailyLearningPlanStage.C2.getName());
                                } else {
                                    userDailyLearningPlanDayResponse.setOver(UserDailyLearningPlanStage.C0.getCode());
                                    userDailyLearningPlanDayResponse.setOverName(UserDailyLearningPlanStage.C0.getName());
                                }
                            }
                        }
                    }
                }
                responses.add(userDailyLearningPlanDayResponse);
            }
            return new Response<>(OK, SUCCESS, responses);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }


    /**
     * 查询某天用户的学习计划(0:视频,直播 , 1:试卷)
     *
     * <AUTHOR>
     */
    @RequestMapping(value = "/v1/user/daily/learning/plan/day/date/query")
    @Token
    public Response<?> queryDate(@RequestBody UserDailyLearningPlanDayRequest request) {
        if (this.isEmpty(request.getCustomerId())){
            Integer customerId = this.getUserToken().getCustomerId();
            request.setCustomerId(customerId);
        }
        return queryUserSingleDayLearningPlan(request);
    }


    /**
     * 用户单日学习计划明细
     */
    public Response<?> queryUserSingleDayLearningPlan(UserDailyLearningPlanDayRequest request) {
        try {
            Date serverTime = this.getServerTime();
            Integer customerId = request.getCustomerId();
            UserDailyLearningPlanDayResponse response = new UserDailyLearningPlanDayResponse();
            response.setProjectItemResponses(new ArrayList<>());
            response.setCourseCatalogResponses(new ArrayList<>());
            response.setLiveResponses(new ArrayList<>());
            response.setExamPaperResponses(new ArrayList<>());

            UserDailyLearningPlanDayValidator validator = new UserDailyLearningPlanDayValidator();
            if (!validator.onPlanDay(request.getPlanDay()).onCustomerId(customerId).result()) {
                return new Response<>(ERROR, validator.getErrorMessage());
            }
            //将年月拼接
            String nowYearMonth = DateUtil.format(serverTime, DATE_MONTH_FORMAT);
            //查询用户本月学习科目信息
            UserDailyLearningPlanQuery userDailyLearningPlanQuery = new UserDailyLearningPlanQuery();
            userDailyLearningPlanQuery.setCustomerId(customerId);
            userDailyLearningPlanQuery.setMonthLike(nowYearMonth);
            userDailyLearningPlanQuery.setStatus(DataStatus.Y.getCode());
            List<UserDailyLearningPlan> userDailyLearningPlans = this.userDailyLearningPlanService.findAll(userDailyLearningPlanQuery);
            List<Integer> projectItemIdList = new ArrayList<>();
            if (!this.isEmpty(userDailyLearningPlans) && !userDailyLearningPlans.isEmpty()) {
                //取出科目
                String projectItemIds = userDailyLearningPlans.get(0).getProjectItemIds();
                if (!this.isEmpty(projectItemIds)) {
                    String[] split = projectItemIds.split(App.COMMA);
                    projectItemIdList = new ArrayList<>();
                    for (String s : split) {
                        projectItemIdList.add(Integer.parseInt(s));
                    }
                    //查询科目信息
                    ProjectItemQuery projectItemQueryV2 = new ProjectItemQuery();
                    projectItemQueryV2.setIds(projectItemIdList);
                    projectItemQueryV2.setStatus(DataStatus.Y.getCode());
                    List<ProjectItem> projectItemList = this.projectItemService.findAll(projectItemQueryV2);
                    for (ProjectItem projectItem : projectItemList) {
                        ProjectItemResponse projectItemResponse = new ProjectItemResponse();
                        projectItemResponse.setId(projectItem.getId());
                        projectItemResponse.setName(projectItem.getName());
                        if (!this.isEmpty(UserDailyLearningPlanProjectItemColor.getByName(projectItem.getName()))){
                            projectItemResponse.setColor(UserDailyLearningPlanProjectItemColor.getByName(projectItem.getName()).getAppColor());
                            projectItemResponse.setGradientColor(UserDailyLearningPlanProjectItemColor.getByName(projectItem.getName()).getAppGradientColor());
                        }
                        response.getProjectItemResponses().add(projectItemResponse);
                    }
                }
            }

            //查询全部科目并组装map
            ProjectItemQuery projectItemQuery = new ProjectItemQuery();
            projectItemQuery.setStatus(DataStatus.Y.getCode());
            List<ProjectItem> projectItems = this.projectItemService.findAll(projectItemQuery);
            Map<Integer, ProjectItem> projectItemMap = projectItems.stream().collect(Collectors.toMap(ProjectItem::getId, Function.identity()));

            Date planDay = DateUtil.parse(request.getPlanDay(), DATE_FORMAT);

            //查询用户对应项目的 每日学习计划
            UserDailyLearningPlanQuery dailyLearningPlanQuery = new UserDailyLearningPlanQuery();
            dailyLearningPlanQuery.setMonth(DateUtil.getMonthStartTime(planDay));
            dailyLearningPlanQuery.setProjectId(request.getProjectId());
            dailyLearningPlanQuery.setCustomerId(customerId);
            dailyLearningPlanQuery.setStatus(DataStatus.Y.getCode());
            List<UserDailyLearningPlan> dailyLearningPlans = this.userDailyLearningPlanService.findAll(dailyLearningPlanQuery);
            if (dailyLearningPlans == null || dailyLearningPlans.isEmpty()) {
                return new Response<>(ERROR, "未找到用户的学习计划日历");
            }
            UserDailyLearningPlanDayQuery userDailyLearningPlanDayQuery = new UserDailyLearningPlanDayQuery();
            userDailyLearningPlanDayQuery.setUserDailyLearningPlanId(dailyLearningPlans.get(0).getId());
            userDailyLearningPlanDayQuery.setCustomerId(customerId);
            userDailyLearningPlanDayQuery.setPlanDay(planDay);
            userDailyLearningPlanDayQuery.setStatus(DataStatus.Y.getCode());
            List<UserDailyLearningPlanDay> userDailyLearningPlanDays = this.userDailyLearningPlanDayService.findAll(userDailyLearningPlanDayQuery);
            if (!this.isEmpty(userDailyLearningPlanDays) && !userDailyLearningPlanDays.isEmpty()) {
                //查询日计划明细
                UserDailyLearningPlanDayDetailQuery userDailyLearningPlanDayDetailQuery = new UserDailyLearningPlanDayDetailQuery();
                userDailyLearningPlanDayDetailQuery.setUserDailyLearningPlanDayId(userDailyLearningPlanDays.get(0).getId());
                userDailyLearningPlanDayDetailQuery.setStatus(DataStatus.Y.getCode());
                List<UserDailyLearningPlanDayDetail> userDailyLearningPlanDayDetails =
                        this.userDailyLearningPlanDayDetailService.findAll(userDailyLearningPlanDayDetailQuery);
                //按照任务类型进行分组
                Map<String, List<UserDailyLearningPlanDayDetail>> userDailyLearningPlanDayDetailTaskTypeMap = userDailyLearningPlanDayDetails
                        .stream().collect(Collectors.groupingBy(UserDailyLearningPlanDayDetail::getTaskType));
                /**
                 * 获取课程信息
                 */
                List<UserDailyLearningPlanDayDetail> userDailyLearningPlanDayDetailsCourse =
                        userDailyLearningPlanDayDetailTaskTypeMap.get(UserDailyLearningPlanDayDetailTaskType.COURSE.getCode());
                if(!this.isEmpty(userDailyLearningPlanDayDetailsCourse) && !userDailyLearningPlanDayDetailsCourse.isEmpty()){

                    Map<Integer, UserDailyLearningPlanDayDetail> userDailyLearningPlanDayDetailMap = userDailyLearningPlanDayDetailsCourse
                            .stream().collect(Collectors.toMap(UserDailyLearningPlanDayDetail::getExternalId, Function.identity()));
                    List<Integer> userDailyLearningPlanCourseIds = userDailyLearningPlanDayDetailsCourse
                            .stream().map(UserDailyLearningPlanDayDetail::getExternalId).collect(Collectors.toList());
                    UserDailyLearningPlanCourseQuery dailyLearningPlanCourseQuery = new UserDailyLearningPlanCourseQuery();
                    dailyLearningPlanCourseQuery.setIds(userDailyLearningPlanCourseIds);
                    dailyLearningPlanCourseQuery.setStatus(DataStatus.Y.getCode());
                    List<UserDailyLearningPlanCourse> dailyLearningPlanCourses = this.userDailyLearningPlanCourseService.findAll(dailyLearningPlanCourseQuery);
                    //按课程分类id分组
                    Map<Integer, List<UserDailyLearningPlanCourse>> userDailyLearningPlanCourseMap = dailyLearningPlanCourses
                            .stream().collect(Collectors.groupingBy(UserDailyLearningPlanCourse::getCourseCatalogId));
                    //取出课程分类id
                    List<Integer> courseCatalogIds = dailyLearningPlanCourses.stream().map(UserDailyLearningPlanCourse::getCourseCatalogId).collect(Collectors.toList());
                    //查询课程分类信息
                    Map<Integer, CourseCatalog> courseCatalogMap = new HashMap<>();
                    if (!this.isEmpty(courseCatalogIds) && !courseCatalogIds.isEmpty()) {
                        CourseCatalogQuery courseCatalogQuery = new CourseCatalogQuery();
                        courseCatalogQuery.setIds(courseCatalogIds);
                        courseCatalogQuery.setStatus(DataStatus.Y.getCode());
                        List<CourseCatalog> courseCatalogList = courseCatalogService.findAll(courseCatalogQuery);
                        courseCatalogMap = courseCatalogList.stream().collect(Collectors.toMap(CourseCatalog::getId, Function.identity()));
                    }
                    Map<Integer, Course> courseMap = new HashMap<>();
                    Map<Integer, List<UserCourseRecord>> userCourseRecordMap = new HashMap<>();
                    //取出视频id
                    List<Integer> courseIds = dailyLearningPlanCourses.stream().map(UserDailyLearningPlanCourse::getCourseId).collect(Collectors.toList());
                    if (!this.isEmpty(courseIds) && !courseIds.isEmpty()) {
                        //查询视频信息
                        CourseQuery courseQuery = new CourseQuery();
                        courseQuery.setIds(courseIds);
                        courseQuery.setStatus(DataStatus.Y.getCode());
                        List<Course> courseList = courseService.findAll(courseQuery);
                        //视频按id组装map
                        courseMap = courseList.stream().collect(Collectors.toMap(Course::getId, Function.identity()));
                        //查询视频进度信息
                        UserCourseRecordQuery userCourseRecordQuery = new UserCourseRecordQuery();
                        userCourseRecordQuery.setCustomerId(customerId);
                        userCourseRecordQuery.setVideoIds(courseIds);
                        userCourseRecordQuery.setStatus(DataStatus.Y.getCode());
                        List<UserCourseRecord> userCourseRecords = this.userCourseRecordService.findAll(userCourseRecordQuery);
                        //按视频id分组
                        userCourseRecordMap = userCourseRecords.stream().collect(Collectors.groupingBy(UserCourseRecord::getVideoId));

                    }

                    //组装视频信息
                    for (Integer courseCatalogId : courseCatalogMap.keySet()) {
                        if (userDailyLearningPlanCourseMap.containsKey(courseCatalogId)) {
                            List<UserDailyLearningPlanCourse> userDailyLearningPlanCourseList = userDailyLearningPlanCourseMap.get(courseCatalogId);
                            if (courseCatalogMap.containsKey(courseCatalogId)) {
                                CourseCatalog courseCatalog = courseCatalogMap.get(courseCatalogId);
                                CourseCatalogResponse courseCatalogResponse = new CourseCatalogResponse();
                                courseCatalogResponse.setId(courseCatalog.getId());
                                courseCatalogResponse.setProjectItemId(courseCatalog.getProjectItemId());
                                if (projectItemMap.containsKey(courseCatalog.getProjectItemId())){
                                    String projectItemName = projectItemMap.get(courseCatalog.getProjectItemId()).getName();
                                    if (!this.isEmpty(UserDailyLearningPlanProjectItemColor.getByName(projectItemName))){
                                        courseCatalogResponse.setColor(UserDailyLearningPlanProjectItemColor.getByName(projectItemName).getAppColor());
                                        courseCatalogResponse.setGradientColor(UserDailyLearningPlanProjectItemColor.getByName(projectItemName).getAppGradientColor());
                                    }
                                }
                                courseCatalogResponse.setName(courseCatalog.getName());
                                courseCatalogResponse.setCourseResponseList(new ArrayList<>());
                                //视频信息
                                for (UserDailyLearningPlanCourse userDailyLearningPlanCourse : userDailyLearningPlanCourseList) {
                                    if (courseMap.containsKey(userDailyLearningPlanCourse.getCourseId())) {
                                        Course course = courseMap.get(userDailyLearningPlanCourse.getCourseId());
                                        CourseResponse courseResponse = new CourseResponse();
                                        courseResponse.setId(course.getId());
                                        courseResponse.setName(course.getName());
                                        courseResponse.setVid(course.getVid());
                                        courseResponse.setProjectId(course.getProjectId());
                                        courseResponse.setProjectItemId(course.getProjectItemId());
                                        courseResponse.setCourseCatalogId(courseCatalogId);
                                        if (projectItemMap.containsKey(course.getProjectItemId())){
                                            String projectItemName = projectItemMap.get(course.getProjectItemId()).getName();
                                            courseResponse.setProjectItemName(projectItemName);
                                            if (!this.isEmpty(UserDailyLearningPlanProjectItemColor.getByName(projectItemName))){
                                                courseResponse.setColor(UserDailyLearningPlanProjectItemColor.getByName(projectItemName).getAppColor());
                                                courseResponse.setGradientColor(UserDailyLearningPlanProjectItemColor.getByName(projectItemName).getAppGradientColor());
                                            }
                                        }
                                        courseResponse.setLength(course.getLength());
                                        courseResponse.setLengthStr(DateUtil.turnSecondsToTimestring(Integer.parseInt(course.getLength())));
                                        if (userCourseRecordMap.containsKey(course.getId())){
                                            UserCourseRecord userCourseRecord = userCourseRecordMap.get(course.getId()).get(0);
                                            courseResponse.setLastLength(this.isEmpty(userCourseRecord.getLength()) ? 0 : userCourseRecord.getLength());
                                            courseResponse.setLastLengthStr(DateUtil.turnSecondsToTimestring(this.isEmpty(userCourseRecord.getLength()) ? 0 : userCourseRecord.getLength()));
                                        }else {
                                            courseResponse.setLastLength(0);
                                            courseResponse.setLastLengthStr(DateUtil.turnSecondsToTimestring(0));
                                        }
                                        if (userDailyLearningPlanDayDetailMap.containsKey(userDailyLearningPlanCourse.getId())) {
                                            Integer completedNumber = userDailyLearningPlanDayDetailMap.get(userDailyLearningPlanCourse.getId()).getCompletedNumber();
                                            courseResponse.setTotalLength(this.isEmpty(completedNumber) ? 0 : completedNumber);
                                            courseResponse.setTotalLengthStr(DateUtil.turnSecondsToTimestring(this.isEmpty(completedNumber) ? 0 : completedNumber));
                                            //是否完成
                                            courseResponse.setCourseFinish(userDailyLearningPlanDayDetailMap.get(userDailyLearningPlanCourse.getId()).getOver());
                                            courseCatalogResponse.getCourseResponseList().add(courseResponse);
                                        }else {
                                            courseResponse.setTotalLength(0);
                                            courseResponse.setTotalLengthStr(DateUtil.turnSecondsToTimestring(0));
                                            //是否完成
                                            courseResponse.setCourseFinish(PublicOver.N.getCode());
                                        }
                                    }
                                }
                                response.getCourseCatalogResponses().add(courseCatalogResponse);
                            }
                        }
                    }
                }
                /**
                 * 跟直播的话在查
                 */
                if (dailyLearningPlans.get(0).getSeeLive().equals(UserDailyLearningPlanSeeLive.Y.getCode())) {
                    List<UserDailyLearningPlanDayDetail> userDailyLearningPlanDayDetailLive =
                            userDailyLearningPlanDayDetailTaskTypeMap.get(UserDailyLearningPlanDayDetailTaskType.LIVE.getCode());

                    if (userDailyLearningPlanDayDetailLive != null && !userDailyLearningPlanDayDetailLive.isEmpty()) {
                        //json转对象
                        List<UserDailyLearningPlanDayDetailLiveResponse> lives = new ArrayList<>();
                        List<Integer> liveIds = new ArrayList<>();
                        for (UserDailyLearningPlanDayDetail liveDetail : userDailyLearningPlanDayDetailLive) {
                            if (!this.isEmpty(liveDetail.getTaskNumber())) {
                                UserDailyLearningPlanDayDetailLiveResponse live = (UserDailyLearningPlanDayDetailLiveResponse) this.getObject(liveDetail.getTaskNumber(), UserDailyLearningPlanDayDetailLiveResponse.class);
                                lives.add(live);
                                liveIds.add(liveDetail.getExternalId());
                            }
                        }
                        //查询用户直播间预约状态
                        LiveSubscriptionQuery liveSubscriptionQuery = new LiveSubscriptionQuery();
                        liveSubscriptionQuery.setCustomerId(customerId);
                        liveSubscriptionQuery.setLiveIds(liveIds);
                        liveSubscriptionQuery.setStatus(DataStatus.Y.getCode());
                        List<LiveSubscription> liveSubscriptions = liveSubscriptionService.findAll(liveSubscriptionQuery);
                        //取出已预约的直播间id
                        List<Integer> liveSubscriptionIds = liveSubscriptions.stream().map(LiveSubscription::getLiveId).collect(Collectors.toList());
                        //查询用户直播间进入状态
                        UserLiveLogsQuery userLiveLogsQuery = new UserLiveLogsQuery();
                        userLiveLogsQuery.setCustomerId(customerId);
                        userLiveLogsQuery.setLiveIds(liveIds);
                        userLiveLogsQuery.setStatus(DataStatus.Y.getCode());
                        List<UserLiveLogs> userLiveLogs = userLiveLogsService.findAll(userLiveLogsQuery);
                        //取出进入过的直播间id
                        List<Integer> liveLogsIds = userLiveLogs.stream().map(UserLiveLogs::getLiveId).collect(Collectors.toList());

                        for (UserDailyLearningPlanDayDetailLiveResponse live : lives) {
                            LiveResponse liveResponse = new LiveResponse();
                            liveResponse.setId(live.getId());
                            liveResponse.setLiveId(live.getLiveId());
                            liveResponse.setName(live.getLiveName());
                            liveResponse.setProjectItemId(live.getProjectItemId());
                            if (!this.isEmpty(live.getStartTime())) {
                                liveResponse.setStartTimeStr(live.getStartTime());
                            }
                            if (projectItemMap.containsKey(live.getProjectItemId())) {
                                String projectItemName = projectItemMap.get(live.getProjectItemId()).getName();
                                liveResponse.setProjectItemName(projectItemName);
                                if (!this.isEmpty(UserDailyLearningPlanProjectItemColor.getByName(projectItemName))) {
                                    liveResponse.setColor(UserDailyLearningPlanProjectItemColor.getByName(projectItemName).getAppColor());
                                    liveResponse.setGradientColor(UserDailyLearningPlanProjectItemColor.getByName(projectItemName).getAppGradientColor());
                                }
                            }
                            //讲师
                            liveResponse.setAuthorName(live.getAuthorName());
                            //用户名(官网app使用)
                            if (this.isEmpty(request.getCustomerId())){
                                liveResponse.setUserName(this.getMobile(getUserToken().getUsername()));
                            }
                            //预约状态
                            if (liveSubscriptionIds.contains(live.getId())) {
                                liveResponse.setLiveStage(LiveSubscribe.Y.getCode());
                                liveResponse.setLiveStageName(LiveSubscribe.Y.getName());
                            } else {
                                liveResponse.setLiveStage(LiveSubscribe.N.getCode());
                                liveResponse.setLiveStageName(LiveSubscribe.N.getName());
                            }
                            //直播开始就变成进入按钮
                            if (!this.isEmpty(live.getStartTime()) && serverTime.compareTo(DateUtil.parse(live.getStartTime(), DATETIME_FORMAT)) > 0) {
                                liveResponse.setLiveStage(LiveSubscribe.ENTER.getCode());
                                liveResponse.setLiveStageName(LiveSubscribe.ENTER.getName());
                            }
                            //直播结束变为结束按钮
                            if (!this.isEmpty(live.getEndTime()) && serverTime.compareTo(DateUtil.parse(live.getEndTime(), DATETIME_FORMAT)) > 0) {
                                liveResponse.setLiveStage(LiveSubscribe.END.getCode());
                                liveResponse.setLiveStageName(LiveSubscribe.END.getName());
                            }
                            //参加状态
                            if (liveLogsIds.contains(live.getId())) {
                                liveResponse.setLiveAttendStage(DataStatus.Y.getCode());
                                liveResponse.setLiveAttendStageName("已参加");
                            } else {
                                liveResponse.setLiveAttendStage(DataStatus.N.getCode());
                                liveResponse.setLiveAttendStageName("未参加");
                            }
                            response.getLiveResponses().add(liveResponse);
                        }
                    }
                }
                /**
                 * 查询试卷信息
                 */
                List<UserDailyLearningPlanDayDetail> userDailyLearningPlanDayDetailExam =
                        userDailyLearningPlanDayDetailTaskTypeMap.get(UserDailyLearningPlanDayDetailTaskType.EXAM.getCode());
                if (userDailyLearningPlanDayDetailExam != null && !userDailyLearningPlanDayDetailExam.isEmpty()) {
                    List<Integer> examPaperIds = userDailyLearningPlanDayDetailExam.stream().map(UserDailyLearningPlanDayDetail::getExternalId).collect(Collectors.toList());
                    //分页查询结果
                    ExamPaperQuery examPaperQuery = new ExamPaperQuery();
                    examPaperQuery.setStatus(com.common.constant.DataStatus.Y.getCode());
                    examPaperQuery.setStage(PublicStage.Y.getCode());
                    examPaperQuery.setIds(examPaperIds);
                    Integer count = examPaperService.count(examPaperQuery);
                    if (count != 0){
                        Pager pager = new Pager(count, 1, count);
                        examPaperQuery.setStart(pager.getOffset());
                        examPaperQuery.setLimit(pager.getLimit());
                        List<String> userExamPaperRecordCatalogCodes = new ArrayList<>();
                        userExamPaperRecordCatalogCodes.add(UserExamPaperRecordCatalog.C2.getCode());
                        userExamPaperRecordCatalogCodes.add(UserExamPaperRecordCatalog.C3.getCode());
                        userExamPaperRecordCatalogCodes.add(UserExamPaperRecordCatalog.C4.getCode());
                        List<ExamPaperResponse> examPaperResponseList = this.getExamPaperResponseListForQuery(examPaperQuery, userExamPaperRecordCatalogCodes, getUserToken().getCustomerId(),projectItemMap);
                        response.setExamPaperResponses(examPaperResponseList);
                    }
                }
            }
            //按绑定月计划时的科目顺序排序
            response.setProjectItemResponses(this.sortById(response.getProjectItemResponses(), projectItemIdList, projectItemResponse -> projectItemResponse.getId()));
            response.setCourseCatalogResponses(this.sortById(response.getCourseCatalogResponses(), projectItemIdList, courseCatalogResponse -> courseCatalogResponse.getProjectItemId()));
            response.setLiveResponses(this.sortById(response.getLiveResponses(), projectItemIdList, liveResponse -> liveResponse.getProjectItemId()));
            response.setExamPaperResponses(this.sortById(response.getExamPaperResponses(), projectItemIdList, examPaperResponse -> examPaperResponse.getProjectItemId()));

            return new Response<>(OK, SUCCESS, response);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }




    /**
     * 查询用户日学习计划课程详情
     * <AUTHOR>
     * @date 2025-08-05
     */
    @RequestMapping("/v1/user/daily/learning/plan/day/course/detail/query")
    public Response<?> queryUserDailyLearningPlanDayCourseDetail(@RequestBody UserDailyLearningPlanDayRequest request) {
        try {
            List<CourseCatalogResponse> responses = new ArrayList<>();
            if (isEmpty(request.getId())) {
                return new Response<>(ERROR,"当日学习计划编号不能为空");
            }

            UserDailyLearningPlanDay userDailyLearningPlanDay = userDailyLearningPlanDayService.findById(request.getId());
            if (isEmpty(userDailyLearningPlanDay) || DataStatus.N.getCode().equals(userDailyLearningPlanDay.getStatus())) {
                return new Response<>(ERROR,"当日无学习计划");
            }
            // 查询每日规划下的课程详细信息
            UserDailyLearningPlanDayDetailQuery userDailyLearningPlanDayDetailQuery = new UserDailyLearningPlanDayDetailQuery();
            userDailyLearningPlanDayDetailQuery.setUserDailyLearningPlanDayId(userDailyLearningPlanDay.getId());
            userDailyLearningPlanDayDetailQuery.setTaskType(UserDailyLearningPlanDayDetailTaskType.COURSE.getCode());
            userDailyLearningPlanDayDetailQuery.setStatus(DataStatus.Y.getCode());
            List<UserDailyLearningPlanDayDetail> userDailyLearningPlanDayDetailList = userDailyLearningPlanDayDetailService.findAll(userDailyLearningPlanDayDetailQuery);
            if (isEmpty(userDailyLearningPlanDayDetailList) || userDailyLearningPlanDayDetailList.isEmpty()) {
                return new Response<>(OK,SUCCESS,responses);
            }

            // 课程的详细信息中 取出外部id集合 此时的外部id 是月规划关联课程视频编号
            List<Integer> externalIds = userDailyLearningPlanDayDetailList.stream().map(UserDailyLearningPlanDayDetail::getExternalId).collect(Collectors.toList());
            if (externalIds.isEmpty()) {
                return new Response<>(OK,SUCCESS,responses);
            }
            // 查询外部编号详细信息
            UserDailyLearningPlanCourseQuery userDailyLearningPlanCourseQuery = new UserDailyLearningPlanCourseQuery();
            userDailyLearningPlanCourseQuery.setIds(externalIds);
            userDailyLearningPlanCourseQuery.setAllocationStage(UserDailyLearningPlanCourseAllocationStage.YES.getCode());
            userDailyLearningPlanCourseQuery.setStatus(DataStatus.Y.getCode());
            List<UserDailyLearningPlanCourse> userDailyLearningPlanCourseList = userDailyLearningPlanCourseService.findAll(userDailyLearningPlanCourseQuery);
            if (isEmpty(userDailyLearningPlanCourseList) || userDailyLearningPlanCourseList.isEmpty()) {
                return new Response<>(OK,SUCCESS,responses);
            }
            // 获取 课程分类编号集合, 课程视频编号集合, map<月规划绑定视频编号,月规划绑定视频信息>
            List<Integer> courseCatalogIds = new ArrayList<>();
            List<Integer> courseIds = new ArrayList<>();
            Map<Integer,UserDailyLearningPlanCourse> userDailyLearningPlanCourseMap = new HashMap<>();
            for (UserDailyLearningPlanCourse planCourse : userDailyLearningPlanCourseList) {
                courseCatalogIds.add(planCourse.getCourseCatalogId());
                courseIds.add(planCourse.getCourseId());
                userDailyLearningPlanCourseMap.put(planCourse.getId(),planCourse);
            }

            // 查询课程分类信息 组装map<课程分类编号,课程分类名称>
            Map<Integer,CourseCatalog> courseCatalogMap = new HashMap<>();
            if (!courseCatalogIds.isEmpty()) {
                CourseCatalogQuery courseCatalogQuery = new CourseCatalogQuery();
                courseCatalogQuery.setIds(courseCatalogIds);
                courseCatalogQuery.setStage(PublicStage.Y.getCode());
                courseCatalogQuery.setStatus(DataStatus.Y.getCode());
                List<CourseCatalog> courseCatalogList = courseCatalogService.findAll(courseCatalogQuery);
                courseCatalogMap = courseCatalogList.stream().collect(Collectors.toMap(CourseCatalog::getId,Function.identity()));
            }
            // 查询课程信息 组装map<课程编号,课程名称>
            Map<Integer,Course> courseMap = new HashMap<>();
            // 科目map<科目编号,科目>
            Map<Integer, ProjectItem> projectItemMap = new HashMap<>();
            if (!courseIds.isEmpty()) {
                CourseQuery courseQuery = new CourseQuery();
                courseQuery.setIds(courseIds);
                courseQuery.setStage(PublicStage.Y.getCode());
                courseQuery.setStatus(DataStatus.Y.getCode());
                List<Course> courseList = courseService.findAll(courseQuery);
                courseMap = courseList.stream().collect(Collectors.toMap(Course::getId,Function.identity()));
                // 获取科目编号集合，并查询科目信息
                List<Integer> projectItemIds = courseList.stream().map(Course::getProjectItemId).collect(Collectors.toList());
                if (!projectItemIds.isEmpty()) {
                    List<ProjectItem> projectItemList = projectItemService.findByIds(projectItemIds);
                    projectItemMap = projectItemList.stream().collect(Collectors.toMap(ProjectItem::getId,Function.identity()));
                }
            }

            // 循环组装返回数据
            Map<Integer,List<CourseResponse>> catalogIdCourseResponseMap = new HashMap<>();
            for (UserDailyLearningPlanDayDetail userDailyLearningPlanDayDetail : userDailyLearningPlanDayDetailList) {
                if (userDailyLearningPlanCourseMap.containsKey(userDailyLearningPlanDayDetail.getExternalId())) {
                    UserDailyLearningPlanCourse userDailyLearningPlanCourse = userDailyLearningPlanCourseMap.get(userDailyLearningPlanDayDetail.getExternalId());
                    // 校验课程分类 课程视频
                    if (courseCatalogMap.containsKey(userDailyLearningPlanCourse.getCourseCatalogId()) && courseMap.containsKey(userDailyLearningPlanCourse.getCourseId())) {
                        Course course = courseMap.get(userDailyLearningPlanCourse.getCourseId());
                        CourseResponse courseResponse = new CourseResponse();
                        courseResponse.setId(course.getId());
                        courseResponse.setName(course.getName());
                        courseResponse.setLengthStr(DateUtil.turnSecondsToTimestring(Integer.parseInt(course.getLength())));
                        courseResponse.setUserDailyLearningPlanCourseId(userDailyLearningPlanCourse.getId());
                        courseResponse.setUserDailyLearningPlanDayDetailId(userDailyLearningPlanDayDetail.getId());
                        courseResponse.setProjectItemId(course.getProjectItemId());
                        if (catalogIdCourseResponseMap.containsKey(userDailyLearningPlanCourse.getCourseCatalogId())) {
                            catalogIdCourseResponseMap.get(userDailyLearningPlanCourse.getCourseCatalogId()).add(courseResponse);
                        } else {
                            List<CourseResponse> courseResponses = new ArrayList<>();
                            courseResponses.add(courseResponse);
                            catalogIdCourseResponseMap.put(userDailyLearningPlanCourse.getCourseCatalogId(),courseResponses);
                        }
                    }
                }
            }

            for (Integer courseCatalogId : catalogIdCourseResponseMap.keySet()) {
                CourseCatalogResponse courseCatalogResponse = new CourseCatalogResponse();
                CourseCatalog courseCatalog = courseCatalogMap.get(courseCatalogId);
                courseCatalogResponse.setId(courseCatalogId);
                courseCatalogResponse.setName(courseCatalog.getName());
                courseCatalogResponse.setCourseResponseList(catalogIdCourseResponseMap.get(courseCatalogId));
                if (projectItemMap.containsKey(courseCatalog.getProjectItemId())){
                    String projectItemName = projectItemMap.get(courseCatalog.getProjectItemId()).getName();
                    if (!this.isEmpty(UserDailyLearningPlanProjectItemColor.getByName(projectItemName))){
                        courseCatalogResponse.setColor(UserDailyLearningPlanProjectItemColor.getByName(projectItemName).getAppColor());
                        courseCatalogResponse.setGradientColor(UserDailyLearningPlanProjectItemColor.getByName(projectItemName).getAppGradientColor());
                    }
                }
                responses.add(courseCatalogResponse);
            }
            return new Response<>(OK, SUCCESS,responses);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }

    /**
     * 查询用户日学习计划试卷详情(分组:试卷分类-试卷标签)
     * <AUTHOR>
     * @date 2025-08-08
     */
    @RequestMapping("/v1/user/daily/learning/plan/day/exam/detail/query")
    public Response<?> queryUserDailyLearningPlanDayExamDetail(@RequestBody UserDailyLearningPlanDayRequest request) {
        try {
            Integer id = request.getId();
            UserDailyLearningPlanDayValidator validator = new UserDailyLearningPlanDayValidator();
            if (!validator.onId(id).result()){
                return new Response<>(ERROR, validator.getErrorMessage());
            }
            UserDailyLearningPlanDay userDailyLearningPlanDay = userDailyLearningPlanDayService.findById(id);
            if (isEmpty(userDailyLearningPlanDay) || !DataStatus.Y.getCode().equals(userDailyLearningPlanDay.getStatus()) || isEmpty(userDailyLearningPlanDay.getUserDailyLearningPlanId())) {
                return new Response<>(ERROR,"当日无学习计划");
            }
            UserDailyLearningPlan userDailyLearningPlan = userDailyLearningPlanService.findById(userDailyLearningPlanDay.getUserDailyLearningPlanId());
            if (isEmpty(userDailyLearningPlan) || !DataStatus.Y.getCode().equals(userDailyLearningPlan.getStatus()) || isEmpty(userDailyLearningPlan.getProjectId())){
                return new Response<>(ERROR, "月学习计划无效");
            }
            List<UserDailyLearningPlanDayDetailExamResponse> responses = new ArrayList<>();

            // 查询每日规划下的试卷列表
            UserDailyLearningPlanDayDetailQuery userDailyLearningPlanDayDetailQuery = new UserDailyLearningPlanDayDetailQuery();
            userDailyLearningPlanDayDetailQuery.setStatus(DataStatus.Y.getCode());
            userDailyLearningPlanDayDetailQuery.setTaskType(UserDailyLearningPlanDayDetailTaskType.EXAM.getCode());
            userDailyLearningPlanDayDetailQuery.setUserDailyLearningPlanDayId(id);
            List<UserDailyLearningPlanDayDetail> userDailyLearningPlanDayDetailList = userDailyLearningPlanDayDetailService.findAll(userDailyLearningPlanDayDetailQuery);
            if (isEmpty(userDailyLearningPlanDayDetailList) || userDailyLearningPlanDayDetailList.isEmpty()) {
                return new Response<>(OK, SUCCESS, responses);
            }
            Map<Integer, UserDailyLearningPlanDayDetail> examPaperIdMappingUserDailyLearningPlanDayDetailMap = userDailyLearningPlanDayDetailList.stream().filter(a -> !isEmpty(a.getExternalId())).collect(Collectors.toMap(UserDailyLearningPlanDayDetail::getExternalId, Function.identity()));

            if (examPaperIdMappingUserDailyLearningPlanDayDetailMap.isEmpty()) {
                return new Response<>(OK,SUCCESS,responses);
            }

            // 查询试卷列表
            ExamPaperQuery examPaperQuery = new ExamPaperQuery();
            examPaperQuery.setStatus(DataStatus.Y.getCode());
            examPaperQuery.setIds(new ArrayList<>(examPaperIdMappingUserDailyLearningPlanDayDetailMap.keySet()));
            List<ExamPaper> examPaperList = examPaperService.findAll(examPaperQuery);
            if (isEmpty(examPaperList) || examPaperList.isEmpty()){
                return new Response<>(OK, SUCCESS, responses);
            }
            Map<Integer, ExamPaper> examPaperMap = examPaperList.stream().collect(Collectors.toMap(ExamPaper::getId, Function.identity()));

            //科目
            Map<Integer, String> projectItemNameMap = new HashMap<>();
            List<Integer> projectItemIdList = examPaperList.stream().map(ExamPaper::getProjectItemId).filter(projectItemId -> !isEmpty(projectItemId)).distinct().collect(Collectors.toList());
            if (!projectItemIdList.isEmpty()){
                ProjectItemQuery projectItemQuery = new ProjectItemQuery();
                projectItemQuery.setStatus(DataStatus.Y.getCode());
                projectItemQuery.setIds(projectItemIdList);
                List<ProjectItem> projectItemList = projectItemService.findAll(projectItemQuery);
                if (!isEmpty(projectItemList) && !projectItemList.isEmpty()){
                    projectItemNameMap = projectItemList.stream().collect(Collectors.toMap(ProjectItem::getId, ProjectItem::getName));
                }
            }

            //试卷分类(项目下所有试卷分类)
            Map<Integer, String> examTypeFullNameMap = new HashMap<>();
            ExamTypeQuery examTypeQuery = new ExamTypeQuery();
            examTypeQuery.setStatus(DataStatus.Y.getCode());
            examTypeQuery.setProjectId(userDailyLearningPlan.getProjectId());
            List<ExamType> examTypeList = examTypeService.findAll(examTypeQuery);
            if (!isEmpty(examTypeList) && !examTypeList.isEmpty()){
                for (ExamType examType : examTypeList) {
                    List<ExamType> parentList = new ArrayList<>();
                    this.getExamTypeParentList(examType, examTypeList, parentList);

                    StringBuilder examTypeFullName = new StringBuilder();
                    for (int index = parentList.size() - 1; index >= 0; index--){
                        examTypeFullName.append(parentList.get(index).getName()).append("-");
                    }
                    examTypeFullName.append(examType.getName());
                    examTypeFullNameMap.put(examType.getId(), examTypeFullName.toString());
                }
            }

            //试卷标签
            Map<Integer, String> questionLabelFullNameMap = new HashMap<>();
            QuestionLabelQuery questionLabelQuery = new QuestionLabelQuery();
            questionLabelQuery.setStatus(DataStatus.Y.getCode());
            questionLabelQuery.setProjectId(userDailyLearningPlan.getProjectId());
            List<QuestionLabel> questionLabelList = questionLabelService.findAll(questionLabelQuery);
            if (!isEmpty(questionLabelList) && !questionLabelList.isEmpty()){
                for (QuestionLabel questionLabel : questionLabelList) {
                    List<QuestionLabel> parentList = new ArrayList<>();
                    this.getQuestionLabelParentList(questionLabel, questionLabelList, parentList);
                    StringBuilder questionLabelFullName = new StringBuilder();
                    for (int index = parentList.size() - 1; index >= 0; index--){
                        questionLabelFullName.append(parentList.get(index).getName()).append("-");
                    }
                    questionLabelFullName.append(questionLabel.getName());
                    questionLabelFullNameMap.put(questionLabel.getId(),  questionLabelFullName.toString());
                }
            }

            //试卷分组
            Map<UserDailyLearningPlanDayDetailExamResponse, List<ExamPaper>> map = new HashMap<>();
            for (UserDailyLearningPlanDayDetail userDailyLearningPlanDayDetail : userDailyLearningPlanDayDetailList) {
                Integer examPaperId = userDailyLearningPlanDayDetail.getExternalId();
                if (isEmpty(examPaperId) || !examPaperMap.containsKey(examPaperId)){
                    continue;
                }
                ExamPaper examPaper = examPaperMap.get(examPaperId);
                UserDailyLearningPlanDayDetailExamResponse userDailyLearningPlanDayDetailExamResponse = new UserDailyLearningPlanDayDetailExamResponse();
                userDailyLearningPlanDayDetailExamResponse.setProjectItemId(examPaper.getProjectItemId());
                userDailyLearningPlanDayDetailExamResponse.setExamTypeId(examPaper.getExamTypeId());
                userDailyLearningPlanDayDetailExamResponse.setExamTypeChildrenId(examPaper.getExamTypeChildrenId());
                userDailyLearningPlanDayDetailExamResponse.setQuestionLabelId(examPaper.getQuestionLabelId());
                if (!map.containsKey(userDailyLearningPlanDayDetailExamResponse)){
                    map.put(userDailyLearningPlanDayDetailExamResponse, new ArrayList<>());
                }
                map.get(userDailyLearningPlanDayDetailExamResponse).add(examPaper);
            }

            for (Map.Entry<UserDailyLearningPlanDayDetailExamResponse, List<ExamPaper>> kv : map.entrySet()) {
                UserDailyLearningPlanDayDetailExamResponse userDailyLearningPlanDayDetailExamResponse = kv.getKey();
                //科目
                Integer projectItemId = userDailyLearningPlanDayDetailExamResponse.getProjectItemId();
                if (!isEmpty(projectItemId) && projectItemNameMap.containsKey(projectItemId)){
                    String projectItemName = projectItemNameMap.get(projectItemId);
                    userDailyLearningPlanDayDetailExamResponse.setProjectItemName(projectItemName);
                    UserDailyLearningPlanProjectItemColor color = UserDailyLearningPlanProjectItemColor.getByName(projectItemName);
                    if (!isEmpty(color)){
                        userDailyLearningPlanDayDetailExamResponse.setProjectItemColor(color.getSysColor());
                    }
                }

                //试卷分类
                Integer examTypeId = userDailyLearningPlanDayDetailExamResponse.getExamTypeId();
                Integer examTypeChildrenId = userDailyLearningPlanDayDetailExamResponse.getExamTypeChildrenId();
                String examTypeFullName = examTypeFullNameMap.get(!isEmpty(examTypeChildrenId) ? examTypeChildrenId : examTypeId);
                userDailyLearningPlanDayDetailExamResponse.setExamTypeChildrenName(examTypeFullName);

                //标签
                Integer questionLabelId = userDailyLearningPlanDayDetailExamResponse.getQuestionLabelId();
                String questionLabelFullName = questionLabelFullNameMap.get(questionLabelId);
                userDailyLearningPlanDayDetailExamResponse.setQuestionLabelName(questionLabelFullName);

                //试卷列表
                List<ExamPaperResponse> examPaperResponseList = new ArrayList<>();
                for (ExamPaper examPaper : kv.getValue()) {
                    ExamPaperResponse examPaperResponse = new ExamPaperResponse();
                    examPaperResponse.setId(examPaper.getId());
                    examPaperResponse.setName(examPaper.getName());
                    UserDailyLearningPlanDayDetail userDailyLearningPlanDayDetail = examPaperIdMappingUserDailyLearningPlanDayDetailMap.get(examPaper.getId());
                    String taskNumber = userDailyLearningPlanDayDetail.getTaskNumber();
                    if (!isEmpty(taskNumber) && NumberUtil.isNumeric(taskNumber)){
                        //试题数量
                        examPaperResponse.setAllQuestionNumber(Integer.parseInt(taskNumber));
                    }
                    //日计划详情ID
                    examPaperResponse.setUserDailyLearningPlanDayDetailId(userDailyLearningPlanDayDetail.getId());
                    examPaperResponseList.add(examPaperResponse);
                }
                userDailyLearningPlanDayDetailExamResponse.setExamPaperResponseList(examPaperResponseList);

                responses.add(userDailyLearningPlanDayDetailExamResponse);
            }

            return new Response<>(OK, SUCCESS,responses);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }

    /**
     * 递归获取父级列表
     */
    private void getQuestionLabelParentList(QuestionLabel questionLabel, List<QuestionLabel> questionLabelList, List<QuestionLabel> parentList) {
        if (questionLabel.getParentId() != 0 && questionLabelList != null && !questionLabelList.isEmpty()) {
            for (QuestionLabel parent : questionLabelList) {
                if (questionLabel.getParentId() != null && parent.getId().equals(questionLabel.getParentId())) {
                    parentList.add(parent);
                    this.getQuestionLabelParentList(parent, questionLabelList, parentList);
                }
            }
        }
    }

    /**
     * 递归获取父级列表
     */
    private void getExamTypeParentList(ExamType examType, List<ExamType> examTypeList, List<ExamType> parentList){
        if (examType.getParentId() != 0 && examTypeList != null && !examTypeList.isEmpty()){
            for (ExamType parent : examTypeList) {
                if (parent.getId().equals(examType.getParentId())){
                    parentList.add(parent);
                    this.getExamTypeParentList(parent, examTypeList, parentList);
                }
            }
        }
    }


    /**
     * 查询用户日学习计划关联-未选择课程视频(分页)
     * <AUTHOR>
     * @date 2025-08-07
     */
    @RequestMapping("/v1/user/daily/learning/plan/day/course/query")
    public Response<?> queryUserDailyLearningPlanDayCourse(@RequestBody UserDailyLearningPlanDayRequest request) {
        try {
            NewPageResponse<CourseResponse> responses = new NewPageResponse<>();
            responses.setTotal(0);
            responses.setItems(new ArrayList<>());
            if (isEmpty(request.getId())) {
                return new Response<>(ERROR,"当日学习计划编号不能为空");
            }

            // 查询日学习规划
            UserDailyLearningPlanDay userDailyLearningPlanDay = userDailyLearningPlanDayService.findById(request.getId());
            if (isEmpty(userDailyLearningPlanDay) || DataStatus.N.getCode().equals(userDailyLearningPlanDay.getStatus())) {
                return new Response<>(ERROR,"当日无学习计划");
            }
            // 查询月学习规划
            UserDailyLearningPlan userDailyLearningPlan = userDailyLearningPlanService.findById(userDailyLearningPlanDay.getUserDailyLearningPlanId());
            if (isEmpty(userDailyLearningPlan) || DataStatus.N.getCode().equals(userDailyLearningPlan.getStatus())) {
                return new Response<>(ERROR,"当月无学习规划");
            }
            // 查询每日规划下的课程详细信息
            UserDailyLearningPlanDayDetailQuery userDailyLearningPlanDayDetailQuery = new UserDailyLearningPlanDayDetailQuery();
            userDailyLearningPlanDayDetailQuery.setUserDailyLearningPlanDayId(userDailyLearningPlanDay.getId());
            userDailyLearningPlanDayDetailQuery.setTaskType(UserDailyLearningPlanDayDetailTaskType.COURSE.getCode());
            userDailyLearningPlanDayDetailQuery.setStatus(DataStatus.Y.getCode());
            List<UserDailyLearningPlanDayDetail> userDailyLearningPlanDayDetailList = userDailyLearningPlanDayDetailService.findAll(userDailyLearningPlanDayDetailQuery);
            List<Integer> externalIds = new ArrayList<>();
            if (!isEmpty(userDailyLearningPlanDayDetailList) && !userDailyLearningPlanDayDetailList.isEmpty()) {
                // 课程的详细信息中 取出外部id集合 此时的外部id 是月规划关联课程视频编号
                externalIds = userDailyLearningPlanDayDetailList.stream().map(UserDailyLearningPlanDayDetail::getExternalId).collect(Collectors.toList());
            }
            // 以关联表为主体查询数据
            UserDailyLearningPlanCourseQuery planCourseQuery = new UserDailyLearningPlanCourseQuery();
            planCourseQuery.setUserDailyLearningPlanId(userDailyLearningPlan.getId());

            // 视频map<视频编号,视频>
            Map<Integer,Course> courseMap = new HashMap<>();
            // 科目map<科目编号,科目>
            Map<Integer, ProjectItem> projectItemMap = new HashMap<>();
            // 课程分类map<课程分类编号,课程分类>
            Map<Integer,CourseCatalog> courseCatalogMap = new HashMap<>();
            if (!externalIds.isEmpty()) {
                planCourseQuery.setBoundIds(externalIds);
            }
            // 查询每月学习计划已绑定课程数据
            UserDailyLearningPlanCourseQuery userDailyLearningPlanCourseQuery = new UserDailyLearningPlanCourseQuery();
            userDailyLearningPlanCourseQuery.setUserDailyLearningPlanId(userDailyLearningPlanDay.getUserDailyLearningPlanId());
            userDailyLearningPlanCourseQuery.setStatus(DataStatus.Y.getCode());
            List<UserDailyLearningPlanCourse> userDailyLearningPlanCourseList = userDailyLearningPlanCourseService.findAll(userDailyLearningPlanCourseQuery);
            // 获取每月学习计划绑定的课程视频ids
            List<Integer> courseIds = new ArrayList<>();
            if (!isEmpty(userDailyLearningPlanCourseList) && !userDailyLearningPlanCourseList.isEmpty()) {
                courseIds = userDailyLearningPlanCourseList.stream().map(UserDailyLearningPlanCourse::getCourseId).collect(Collectors.toList());
            }
            if (courseIds.isEmpty()) {
                return new Response<>(OK,SUCCESS,responses);
            }
            // 1.查询已关联课程视频信息，获得符合条件的课程视频编号集合 并组装map<视频编号,视频>
            CourseQuery courseQuery = new CourseQuery();
            courseQuery.setCourseIds(courseIds);
            courseQuery.setProjectId(userDailyLearningPlan.getProjectId());
            if (!isEmpty(request.getProjectItemId())) {
                courseQuery.setProjectItemId(request.getProjectItemId());
            }
            if (!isEmpty(request.getCourseNameLike())) {
                courseQuery.setNameLike(request.getCourseNameLike());
            }
            List<Course> courseList = courseService.findAll(courseQuery);
            // 判断查询数据为空时 返回
            if (isEmpty(courseList) || courseList.isEmpty()) {
                return new Response<>(OK,SUCCESS,responses);
            }
            // 获取视频编号集合,组装map<视频编号,视频>
            courseIds = courseList.stream().map(Course::getId).collect(Collectors.toList());
            // 视频map<视频编号,视频>
            courseMap = courseList.stream().collect(Collectors.toMap(Course::getId,Function.identity()));
            // 获取科目编号集合，并查询科目信息
            List<Integer> projectItemIds = courseList.stream().map(Course::getProjectItemId).collect(Collectors.toList());
            if (!projectItemIds.isEmpty()) {
                List<ProjectItem> projectItemList = projectItemService.findByIds(projectItemIds);
                projectItemMap = projectItemList.stream().collect(Collectors.toMap(ProjectItem::getId,Function.identity()));
            }

            // 2.查询已关联课程分类信息，获得符合条件的课程分类编号集合 并组装map<课程分类编号,课程分类>
            // 获取课程分类编号
            List<Integer> courseCatalogIds = userDailyLearningPlanCourseList.stream().map(UserDailyLearningPlanCourse::getCourseCatalogId).collect(Collectors.toList());
            if (!courseCatalogIds.isEmpty()) {
                CourseCatalogQuery courseCatalogQuery = new CourseCatalogQuery();
                courseCatalogQuery.setIds(courseCatalogIds);
                if (!isEmpty(request.getCourseCatalogId())) {
                    courseCatalogQuery.setId(request.getCourseCatalogId());
                }
                courseCatalogQuery.setProjectId(userDailyLearningPlan.getProjectId());
                if (!isEmpty(request.getProjectItemId())) {
                    courseCatalogQuery.setProjectItemId(request.getProjectItemId());
                }
                List<CourseCatalog> courseCatalogList = courseCatalogService.findAll(courseCatalogQuery);
                // 判断查询数据为空时 返回
                if (isEmpty(courseCatalogList) || courseCatalogList.isEmpty()) {
                    return new Response<>(OK,SUCCESS,responses);
                }
                courseCatalogIds = courseCatalogList.stream().map(CourseCatalog::getId).collect(Collectors.toList());
                courseCatalogMap = courseCatalogList.stream().collect(Collectors.toMap(CourseCatalog::getId,Function.identity()));
            }
            planCourseQuery.setCourseCatalogIds(courseCatalogIds);
            planCourseQuery.setCourseIds(courseIds);

            planCourseQuery.setStatus(DataStatus.Y.getCode());
            // 查询数量
            Integer total = userDailyLearningPlanCourseService.count(planCourseQuery);
            if (total == 0){
                return new Response<>(OK,SUCCESS,responses);
            }
            responses.setTotal(total);
            Pager pager = new Pager(total,request.getPage(),request.getLimit());
            planCourseQuery.setStart(pager.getOffset());
            planCourseQuery.setLimit(pager.getLimit());
            // 判断查询结果是否有数据
            List<UserDailyLearningPlanCourse> userDailyLearningPlanCourses = userDailyLearningPlanCourseService.find(planCourseQuery);
            if (!isEmpty(userDailyLearningPlanCourses) && !userDailyLearningPlanCourses.isEmpty()) {
                for (UserDailyLearningPlanCourse userDailyLearningPlanCourse : userDailyLearningPlanCourses) {
                    CourseResponse courseResponse = new CourseResponse();
                    // 用户月计划绑定视频编号
                    courseResponse.setUserDailyLearningPlanCourseId(userDailyLearningPlanCourse.getId());
                    // 视频编号
                    courseResponse.setId(userDailyLearningPlanCourse.getCourseId());
                    if (courseMap.containsKey(userDailyLearningPlanCourse.getCourseId())) {
                        Course course = courseMap.get(userDailyLearningPlanCourse.getCourseId());
                        // 视频时长 转换格式 HH:mm:ss
                        courseResponse.setLengthStr(DateUtil.turnSecondsToTimestring(Integer.parseInt(course.getLength())));
                        // 视频名称
                        courseResponse.setName(course.getName());
                        // 科目信息
                        if (projectItemMap.containsKey(course.getProjectItemId())) {
                            ProjectItem projectItem = projectItemMap.get(course.getProjectItemId());
                            // 科目编号
                            courseResponse.setProjectItemId(projectItem.getId());
                            // 科目名称
                            courseResponse.setProjectItemName(projectItem.getName());
                        }
                    }
                    // 课程分类编号
                    courseResponse.setCourseCatalogId(userDailyLearningPlanCourse.getCourseCatalogId());
                    if (courseCatalogMap.containsKey(userDailyLearningPlanCourse.getCourseCatalogId())) {
                        CourseCatalog courseCatalog = courseCatalogMap.get(userDailyLearningPlanCourse.getCourseCatalogId());
                        // 课程分类名称
                        courseResponse.setCourseCatalogName(courseCatalog.getName());
                    }
                    responses.getItems().add(courseResponse);
                }
            }
            return new Response<>(OK, SUCCESS,responses);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }



    /**
     * 查询试卷列表,包括用户做题统计信息,一般用于科目练习,模拟考试,历年真题(全部)的试卷列表查询
     */
    private List<ExamPaperResponse> getExamPaperResponseListForQuery(ExamPaperQuery examPaperQuery, List<String> userExamPaperRecordCatalogCodes, Integer customerId,Map<Integer,ProjectItem> projectItemMap){
        List<ExamPaperResponse> result = new ArrayList<>();

        List<ExamPaper> examPaperList = examPaperService.findPage(examPaperQuery);
        if (isEmpty(examPaperList) || examPaperList.isEmpty()){
            return result;
        }

        List<Integer> examPaperIdList = examPaperList.stream().map(ExamPaper::getId).collect(Collectors.toList());
        //查询这些试卷下所有大题列表
        ExamPaperQuestionQuery examPaperQuestionQuery = new ExamPaperQuestionQuery();
        examPaperQuestionQuery.setStatus(com.common.constant.DataStatus.Y.getCode());
        examPaperQuestionQuery.setParentId(0);
        examPaperQuestionQuery.setExamPaperIds(examPaperIdList);
        List<ExamPaperQuestion> examPaperQuestionList = examPaperQuestionService.findAll(examPaperQuestionQuery);

        //K:试卷ID V:试卷试题关联关系列表 (可用于求试卷中一共有多少个试题)
        Map<Integer, List<ExamPaperQuestion>> examPaperIdMappingExamPaperQuestionListMap = new HashMap<>();
        if (!isEmpty(examPaperQuestionList) && !examPaperQuestionList.isEmpty()){
            examPaperIdMappingExamPaperQuestionListMap = examPaperQuestionList.stream().filter(a -> !isEmpty(a.getQuestionId())).collect(Collectors.groupingBy(ExamPaperQuestion::getExamPaperId));
        }

        //查询这些试卷的做卷记录
        UserExamPaperRecordQuery userExamPaperRecordQuery = new UserExamPaperRecordQuery();
        userExamPaperRecordQuery.setStatus(com.common.constant.DataStatus.Y.getCode());
        userExamPaperRecordQuery.setCustomerId(customerId);
        userExamPaperRecordQuery.setExamPaperIds(examPaperIdList);
        //做题记录分类 在交卷记录表中仅通过试卷ID没办法区分做的是哪个表中的卷,所以配合此字段进行唯一区分, 如果不加此字段,会同时查询到用户组卷和系统试卷
        userExamPaperRecordQuery.setCatalogs(userExamPaperRecordCatalogCodes);
        List<UserExamPaperRecord> userExamPaperRecordList = userExamPaperRecordService.findAll(userExamPaperRecordQuery);

        //做卷 记录(用户最后一次做卷记录,可能是交卷了,可能是保存退出了,可能是从来没做过) K:试卷ID V:交卷(做卷未提交)记录(可能为多次,只要最后一次)
        Map<Integer, UserExamPaperRecord> examPaperIdMappingUserExamPaperRecordMap = new HashMap<>();
        if (!isEmpty(userExamPaperRecordList) && !userExamPaperRecordList.isEmpty()){
            examPaperIdMappingUserExamPaperRecordMap = userExamPaperRecordList.stream().collect(Collectors.toMap(UserExamPaperRecord::getExamPaperId, Function.identity(), (a, b) -> a.getCreateTime().after(b.getCreateTime()) ? a : b));
        }
        //分组组装map
        Map<Integer, List<UserExamPaperRecord>> userExamPaperRecordMap = userExamPaperRecordList.stream().collect(Collectors.groupingBy(UserExamPaperRecord::getExamPaperId));

        //取出试卷类型id
        List<Integer> examTypeIdList = examPaperList.stream().map(ExamPaper::getExamTypeId).collect(Collectors.toList());

        Map<Integer, ExamType> examTypeMap = new HashMap<>();

        if (!this.isEmpty(examTypeIdList) && !examTypeIdList.isEmpty()){
            ExamTypeQuery examTypeQuery = new ExamTypeQuery();
            examTypeQuery.setIds(examTypeIdList);
            examTypeQuery.setStatus(DataStatus.Y.getCode());
            List<ExamType> examTypeList = examTypeService.findAll(examTypeQuery);
            //按id组装map
            examTypeMap = examTypeList.stream().collect(Collectors.toMap(ExamType::getId, Function.identity()));
        }

        //封装数据
        for (ExamPaper examPaper : examPaperList) {
            //-- 做卷记录
            UserExamPaperRecord userExamPaperRecord = null;
            if (examPaperIdMappingUserExamPaperRecordMap.containsKey(examPaper.getId())){
                userExamPaperRecord = examPaperIdMappingUserExamPaperRecordMap.get(examPaper.getId());
            }

            ExamPaperResponse examPaperResponse = new ExamPaperResponse();

            //* 试卷编号
            examPaperResponse.setId(examPaper.getId());
            //* 试卷名称
            examPaperResponse.setName(examPaper.getName());
            examPaperResponse.setLastExerciseQuestion((userExamPaperRecord == null || userExamPaperRecord.getLastExerciseQuestion() == null ) ? 0 : userExamPaperRecord.getLastExerciseQuestion());
            //* 做卷记录编号
            if (userExamPaperRecord != null){
                examPaperResponse.setUserExamPaperRecordId(userExamPaperRecord.getId());
            }

            //* 试题数量
            if (examPaperIdMappingExamPaperQuestionListMap.containsKey(examPaper.getId())){
                List<ExamPaperQuestion> examPaperQuestions = examPaperIdMappingExamPaperQuestionListMap.get(examPaper.getId());
                examPaperResponse.setAllQuestionNumber(examPaperQuestions.size());
            } else {
                examPaperResponse.setAllQuestionNumber(0);
            }

            //* 做题进度(做题数量)
            examPaperResponse.setDoQuestionNumber(0);
            if (userExamPaperRecord != null){

                //* 做题进度(做题数量) 未交卷的进度是做题数量 交卷的进度是卷中包含的试题数量
                if (UserExamPaperRecordOver.N.getCode().equals(userExamPaperRecord.getOver())){
                    examPaperResponse.setDoQuestionNumber(Math.min(examPaperResponse.getAllQuestionNumber(), userExamPaperRecord.getPlayQuestionNumber()));
                } else if (UserExamPaperRecordOver.Y.getCode().equals(userExamPaperRecord.getOver())){
                    examPaperResponse.setDoQuestionNumber(Math.min(examPaperResponse.getAllQuestionNumber(), userExamPaperRecord.getQuestionNumber()));
                }

                //* 正确率(我觉得是 只有在重新做题的时候才会展示上次做题的正确率,但其实设计的是任何试卷状态下都有这个正确率)  这里目前设置的是在交卷(退出保存)的时候计算的正确率,规则为:在所有可判断对错的题目中(包含材料题小题),做对的数量除以所有题目数量
                if (!isEmpty(userExamPaperRecord.getRightRate())){
                    examPaperResponse.setYesQuestionRatio(userExamPaperRecord.getRightRate().stripTrailingZeros().toPlainString());
                }
            }

            //* 试卷状态: 开始做题[从来没做过]  继续做题[保存了,没做完]  重新做题[做完交卷了]
            UserExamPaperRecordStatus userExamPaperRecordStatus = null;
            if (userExamPaperRecord != null){
                if (UserExamPaperRecordOver.N.getCode().equals(userExamPaperRecord.getOver())){
                    userExamPaperRecordStatus = UserExamPaperRecordStatus.CONTINUE;
                } else if (UserExamPaperRecordOver.Y.getCode().equals(userExamPaperRecord.getOver())){
                    userExamPaperRecordStatus = UserExamPaperRecordStatus.AGAIN;
                }
            } else {
                userExamPaperRecordStatus = UserExamPaperRecordStatus.BEGIN;
            }
            if (userExamPaperRecordStatus != null){
                examPaperResponse.setUserExamPaperRecordStatus(userExamPaperRecordStatus.getCode());
                //试卷状态名称(可以作为前端展示的按钮名称 如果是[重新做题]状态 前端可以自己加上[查看解析]按钮)
                examPaperResponse.setUserExamPaperRecordStatusName(userExamPaperRecordStatus.getName());
            }
            //完成或未完成。如果交过卷就算完成
            if (userExamPaperRecordMap.containsKey(examPaper.getId())){
                List<UserExamPaperRecord> userExamPaperRecords = userExamPaperRecordMap.get(examPaper.getId());
                examPaperResponse.setOver(PublicOver.N.getCode());
                for (UserExamPaperRecord examPaperRecord : userExamPaperRecords) {
                    if (PublicOver.Y.getCode().equals(examPaperRecord.getOver())){
                        examPaperResponse.setOver(PublicOver.Y.getCode());
                        break;
                    }
                }
            }else {
                examPaperResponse.setOver(PublicOver.N.getCode());
            }

            //颜色
            if (projectItemMap.containsKey(examPaper.getProjectItemId())) {
                String projectItemName = projectItemMap.get(examPaper.getProjectItemId()).getName();
                examPaperResponse.setProjectItemName(projectItemName);
                if (!this.isEmpty(UserDailyLearningPlanProjectItemColor.getByName(projectItemName))) {
                    examPaperResponse.setColor(UserDailyLearningPlanProjectItemColor.getByName(projectItemName).getAppColor());
                    examPaperResponse.setGradientColor(UserDailyLearningPlanProjectItemColor.getByName(projectItemName).getAppGradientColor());
                }
            }
            //分类
            if (examTypeMap.containsKey(examPaper.getExamTypeId())){
                examPaperResponse.setUserExamPaperRecordCatalog(UserExamPaperRecordCatalog.getByName(examTypeMap.get(examPaper.getExamTypeId()).getName()).getCode());
            }

            result.add(examPaperResponse);
        }
        return result;
    }


    /**
     * 按照指定ID顺序排序对象集合
     */
    private <T,ID> List<T> sortById(
            List<T> list,
            List<ID> idOrder,
            Function<T, ID> idExtractor) {

        // 创建ID到索引位置的映射
        Map<ID, Integer> orderMap = new HashMap<>();
        for (int i = 0; i < idOrder.size(); i++) {
            orderMap.put(idOrder.get(i), i);
        }

        return list.stream()
                .sorted(Comparator.comparing(
                        obj -> orderMap.getOrDefault(idExtractor.apply(obj), Integer.MAX_VALUE)
                ))
                .collect(Collectors.toList());
    }


}
