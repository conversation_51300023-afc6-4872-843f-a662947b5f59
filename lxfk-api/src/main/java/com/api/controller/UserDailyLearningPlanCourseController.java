package com.api.controller;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.api.bean.CourseResponse;
import com.api.bean.NewPageResponse;
import com.api.bean.UserDailyLearningPlanCourseRequest;
import com.api.bean.UserDailyLearningPlanCourseResponse;
import com.common.bean.Pager;
import com.common.bean.Response;
import com.common.constant.DataStatus;
import com.common.constant.PublicAssociationCatalog;
import com.common.constant.PublicStage;
import com.common.constant.UserDailyLearningPlanCourseAllocationStage;
import com.common.util.DateUtil;
import com.common.util.NumberUtil;
import com.domain.Course;
import com.domain.CourseCatalog;
import com.domain.CourseCatalogChapter;
import com.domain.CourseCatalogChapterVideo;
import com.domain.ProjectItem;
import com.domain.UserDailyLearningPlan;
import com.domain.UserDailyLearningPlanCourse;
import com.domain.complex.CourseCatalogChapterQuery;
import com.domain.complex.CourseCatalogChapterVideoQuery;
import com.domain.complex.CourseCatalogQuery;
import com.domain.complex.CourseQuery;
import com.domain.complex.UserDailyLearningPlanCourseQuery;
import com.service.CourseCatalogChapterService;
import com.service.CourseCatalogChapterVideoService;
import com.service.CourseCatalogService;
import com.service.ProjectItemService;
import com.service.UserDailyLearningPlanCourseService;
import com.service.UserDailyLearningPlanService;

/**
 * 用户每月学习计划课程表
 *
 * @date 2025-07-28 14:15:56
 */
@RestController
public class UserDailyLearningPlanCourseController extends VideoBaseControllerV3 {
    @Autowired
    private UserDailyLearningPlanCourseService userDailyLearningPlanCourseService;
    @Autowired
    private UserDailyLearningPlanService userDailyLearningPlanService;
    @Autowired
    private CourseCatalogService courseCatalogService;
    @Autowired
    private ProjectItemService projectItemService;
    @Autowired
    private CourseCatalogChapterService courseCatalogChapterService;
    @Autowired
    private CourseCatalogChapterVideoService courseCatalogChapterVideoService;




    /**
     * 查询用户每月学习计划关联，未关联课程视频信息（分页）
     * @param request
     * @return
     */
    @RequestMapping(value = "/v1/user/daily/learning/plan/course/bound/query")
    public Response<?> queryUserDailyLearningPlanCourseBound(@RequestBody UserDailyLearningPlanCourseRequest request) {
        try {
            NewPageResponse<CourseResponse> response = new NewPageResponse<>();
            response.setTotal(0);
            response.setItems(new ArrayList<>());

            if (isEmpty(request.getUserDailyLearningPlanId())) {
                return new Response<>(ERROR,"每月学习计划编号不能为空");
            }
            if (isEmpty(request.getAssociationCatalog())) {
                return new Response<>(ERROR,"关联状态参数不能为空");
            }
            // 查询每月学习计划数据状态
            UserDailyLearningPlan userDailyLearningPlan = userDailyLearningPlanService.findById(request.getUserDailyLearningPlanId());
            if (isEmpty(userDailyLearningPlan)) {
                return new Response<>(ERROR,"每月学习计划不存在");
            } else if (DataStatus.N.getCode().equals(userDailyLearningPlan.getStatus())){
                return new Response<>(ERROR,"每月学习计划已删除");
            }

            // 查询每月学习计划已绑定课程数据
            UserDailyLearningPlanCourseQuery userDailyLearningPlanCourseQuery = new UserDailyLearningPlanCourseQuery();
            userDailyLearningPlanCourseQuery.setUserDailyLearningPlanId(userDailyLearningPlan.getId());
            userDailyLearningPlanCourseQuery.setStatus(DataStatus.Y.getCode());
            List<UserDailyLearningPlanCourse> userDailyLearningPlanCourseList = userDailyLearningPlanCourseService.findAll(userDailyLearningPlanCourseQuery);
            // 获取每月学习计划绑定的课程视频ids
            List<Integer> courseIds = new ArrayList<>();
            if (!isEmpty(userDailyLearningPlanCourseList) && !userDailyLearningPlanCourseList.isEmpty()) {
                courseIds = userDailyLearningPlanCourseList.stream().map(UserDailyLearningPlanCourse::getCourseId).collect(Collectors.toList());
            }
            // 已关联
            if (PublicAssociationCatalog.C0.getCode().equals(request.getAssociationCatalog())) {
                if (courseIds.isEmpty()) {
                    return new Response<>(OK,SUCCESS,response);
                }
                // 1.查询已关联课程视频信息，获得符合条件的课程视频编号集合 并组装map<视频编号,视频>
                CourseQuery courseQuery = new CourseQuery();
                courseQuery.setCourseIds(courseIds);
                courseQuery.setProjectId(userDailyLearningPlan.getProjectId());
                if (!isEmpty(request.getProjectItemId())) {
                    courseQuery.setProjectItemId(request.getProjectItemId());
                }
                if (!isEmpty(request.getCourseNameLike())) {
                    courseQuery.setNameLike(request.getCourseNameLike());
                }
                courseQuery.setStatus(DataStatus.Y.getCode());
                List<Course> courseList = courseService.findAll(courseQuery);
                // 判断查询数据为空时 返回
                if (isEmpty(courseList) || courseList.isEmpty()) {
                    return new Response<>(OK,SUCCESS,response);
                }
                // 获取视频编号集合,组装map<视频编号,视频>
                courseIds = courseList.stream().map(Course::getId).collect(Collectors.toList());
                // 视频map<视频编号,视频>
                Map<Integer,Course> courseMap = courseList.stream().collect(Collectors.toMap(Course::getId,Function.identity()));
                // 科目map<科目编号,科目>
                Map<Integer, ProjectItem> projectItemMap = new HashMap<>();
                // 获取科目编号集合，并查询科目信息
                List<Integer> projectItemIds = courseList.stream().map(Course::getProjectItemId).collect(Collectors.toList());
                if (!projectItemIds.isEmpty()) {
                    List<ProjectItem> projectItemList = projectItemService.findByIds(projectItemIds);
                    projectItemMap = projectItemList.stream().collect(Collectors.toMap(ProjectItem::getId,Function.identity()));
                }

                // 2.查询已关联课程分类信息，获得符合条件的课程分类编号集合 并组装map<课程分类编号,课程分类>
                // 课程分类map<课程分类编号,课程分类>
                Map<Integer,CourseCatalog> courseCatalogMap = new HashMap<>();
                // 获取课程分类编号
                List<Integer> courseCatalogIds = userDailyLearningPlanCourseList.stream().map(UserDailyLearningPlanCourse::getCourseCatalogId).collect(Collectors.toList());
                if (!courseCatalogIds.isEmpty()) {
                    CourseCatalogQuery courseCatalogQuery = new CourseCatalogQuery();
                    courseCatalogQuery.setIds(courseCatalogIds);
                    if (!isEmpty(request.getCourseCatalogId())) {
                        courseCatalogQuery.setId(request.getCourseCatalogId());
                    }
                    courseCatalogQuery.setProjectId(userDailyLearningPlan.getProjectId());
                    if (!isEmpty(request.getProjectItemId())) {
                        courseCatalogQuery.setProjectItemId(request.getProjectItemId());
                    }
                    courseCatalogQuery.setStage(PublicStage.Y.getCode());
                    courseCatalogQuery.setStatus(DataStatus.Y.getCode());
                    List<CourseCatalog> courseCatalogList = courseCatalogService.findAll(courseCatalogQuery);
                    // 判断查询数据为空时 返回
                    if (isEmpty(courseCatalogList) || courseCatalogList.isEmpty()) {
                        return new Response<>(OK,SUCCESS,response);
                    }
                    courseCatalogIds = courseCatalogList.stream().map(CourseCatalog::getId).collect(Collectors.toList());
                    courseCatalogMap = courseCatalogList.stream().collect(Collectors.toMap(CourseCatalog::getId,Function.identity()));
                }

                // 3.以关联表为主体重新查询数据
                UserDailyLearningPlanCourseQuery planCourseQuery = new UserDailyLearningPlanCourseQuery();
                planCourseQuery.setUserDailyLearningPlanId(userDailyLearningPlan.getId());
                planCourseQuery.setCourseCatalogIds(courseCatalogIds);
                planCourseQuery.setCourseIds(courseIds);
                planCourseQuery.setStatus(DataStatus.Y.getCode());
                // 查询数量
                Integer total = userDailyLearningPlanCourseService.count(planCourseQuery);
                if (total == 0){
                    return new Response<>(OK,SUCCESS,response);
                }
                response.setTotal(total);
                Pager pager = new Pager(total,request.getPage(),request.getLimit());
                planCourseQuery.setStart(pager.getOffset());
                planCourseQuery.setLimit(pager.getLimit());
                // 判断查询结果是否有数据
                List<UserDailyLearningPlanCourse> userDailyLearningPlanCourses = userDailyLearningPlanCourseService.find(planCourseQuery);
                if (!isEmpty(userDailyLearningPlanCourses) && !userDailyLearningPlanCourses.isEmpty()) {
                    for (UserDailyLearningPlanCourse userDailyLearningPlanCourse : userDailyLearningPlanCourses) {
                        CourseResponse courseResponse = new CourseResponse();
                        // 用户月计划绑定视频编号
                        courseResponse.setUserDailyLearningPlanCourseId(userDailyLearningPlanCourse.getId());
                        // 视频编号
                        courseResponse.setId(userDailyLearningPlanCourse.getCourseId());
                        if (courseMap.containsKey(userDailyLearningPlanCourse.getCourseId())) {
                            Course course = courseMap.get(userDailyLearningPlanCourse.getCourseId());
                            // 视频时长 转换格式 HH:mm:ss
                            courseResponse.setLengthStr(DateUtil.turnSecondsToTimestring(Integer.parseInt(course.getLength())));
                            // 视频名称
                            courseResponse.setName(course.getName());
                            // 科目信息
                            if (projectItemMap.containsKey(course.getProjectItemId())) {
                                ProjectItem projectItem = projectItemMap.get(course.getProjectItemId());
                                // 科目编号
                                courseResponse.setProjectItemId(projectItem.getId());
                                // 科目名称
                                courseResponse.setProjectItemName(projectItem.getName());
                            }
                        }
                        // 课程分类编号
                        courseResponse.setCourseCatalogId(userDailyLearningPlanCourse.getCourseCatalogId());
                        if (courseCatalogMap.containsKey(userDailyLearningPlanCourse.getCourseCatalogId())) {
                            CourseCatalog courseCatalog = courseCatalogMap.get(userDailyLearningPlanCourse.getCourseCatalogId());
                            // 课程分类名称
                            courseResponse.setCourseCatalogName(courseCatalog.getName());
                        }
                        response.getItems().add(courseResponse);
                    }
                }
            } else if (PublicAssociationCatalog.C1.getCode().equals(request.getAssociationCatalog())) {
                // 1.排除已关联的课程视频
                // 查询条件
                CourseQuery courseQuery = new CourseQuery();
                if (!courseIds.isEmpty()) {
                    courseQuery.setNoAssociationIds(courseIds);
                }

                // 2.查询对应项目下学员购买的所有课程视频
                // 2.1查询对应项目下用户购买课程的课程分类
                List<CourseCatalog> courseCatalogList = queryUserCourseCatalogs(userDailyLearningPlan.getProjectId() , userDailyLearningPlan.getCustomerId());
                if (isEmpty(courseCatalogList) || courseCatalogList.isEmpty()) {
                    return new Response<>(OK,SUCCESS,response);
                }
                // 2.2课程分类map<课程分类编号,课程分类>
                Map<Integer,CourseCatalog> courseCatalogMap = new HashMap<>();
                // 获取课程分类ids
                List<Integer> courseCatalogIds = courseCatalogList.stream().map(CourseCatalog::getId).collect(Collectors.toList());
                if (!courseCatalogIds.isEmpty()) {
                    CourseCatalogQuery courseCatalogQuery = new CourseCatalogQuery();
                    courseCatalogQuery.setProjectId(userDailyLearningPlan.getProjectId());
                    if (!isEmpty(request.getProjectItemId())) {
                        courseCatalogQuery.setProjectItemId(request.getProjectItemId());
                    }
                    if (!isEmpty(request.getCourseCatalogId())) {
                        courseCatalogQuery.setId(request.getCourseCatalogId());
                    }
                    courseCatalogQuery.setIds(courseCatalogIds);
                    // 上架状态
                    courseCatalogQuery.setStage(PublicStage.Y.getCode());
                    courseCatalogQuery.setStatus(DataStatus.Y.getCode());
                    courseCatalogList = courseCatalogService.findAll(courseCatalogQuery);
                    if (isEmpty(courseCatalogList) || courseCatalogList.isEmpty()) {
                        return new Response<>(OK,SUCCESS,response);
                    }
                    courseCatalogIds = courseCatalogList.stream().map(CourseCatalog::getId).collect(Collectors.toList());
                    courseCatalogMap = courseCatalogList.stream().collect(Collectors.toMap(CourseCatalog::getId,Function.identity()));
                }
                if (courseCatalogIds.isEmpty()) {
                    return new Response<>(OK,SUCCESS,response);
                }

                // 2.3查询课程分类关联的章节信息，并获取map<章节id,课程分类id>
                CourseCatalogChapterQuery courseCatalogChapterQuery = new CourseCatalogChapterQuery();
                courseCatalogChapterQuery.setCourseCatalogIds(courseCatalogIds);
                courseCatalogChapterQuery.setStatus(DataStatus.Y.getCode());
                List<CourseCatalogChapter> courseCatalogChapterList = courseCatalogChapterService.findAll(courseCatalogChapterQuery);
                // map<章节id,课程分类id>
                Map<Integer,Integer> courseCatalogChapterMap = courseCatalogChapterList.stream().collect(Collectors.toMap(CourseCatalogChapter::getId,CourseCatalogChapter::getCourseCatalogId));
                List<Integer> chapterIds = courseCatalogChapterList.stream().map(CourseCatalogChapter::getId).collect(Collectors.toList());
                if (isEmpty(chapterIds) || chapterIds.isEmpty()) {
                    return new Response<>(OK,SUCCESS,response);
                }

                // 2.4查询章节关联的视频
                CourseCatalogChapterVideoQuery courseCatalogChapterVideoQuery = new CourseCatalogChapterVideoQuery();
                courseCatalogChapterVideoQuery.setChapterIds(chapterIds);
                courseCatalogChapterVideoQuery.setStatus(DataStatus.Y.getCode());
                List<CourseCatalogChapterVideo> courseCatalogChapterVideoList = courseCatalogChapterVideoService.findAll(courseCatalogChapterVideoQuery);
                List<Integer> videoIds = courseCatalogChapterVideoList.stream().map(CourseCatalogChapterVideo::getVideoId).collect(Collectors.toList());
                if (videoIds.isEmpty()) {
                    return new Response<>(OK,SUCCESS,response);
                }
                // 2.5查询符合条件的视频信息
                courseQuery.setCourseIds(videoIds);
                if (!isEmpty(userDailyLearningPlan.getProjectId())) {
                    courseQuery.setProjectId(userDailyLearningPlan.getProjectId());
                }
                if (!isEmpty(request.getProjectItemId())) {
                    courseQuery.setProjectItemId(request.getProjectItemId());
                }
                if (!isEmpty(request.getCourseNameLike())) {
                    courseQuery.setNameLike(request.getCourseNameLike());
                }
                courseQuery.setStage(PublicStage.Y.getCode());
                courseQuery.setStatus(DataStatus.Y.getCode());
                List<Course> courseList = courseService.findAll(courseQuery);
                // 获取map<视频编号,视频信息>
                Map<Integer,Course> courseMap = courseList.stream().collect(Collectors.toMap(Course::getId,Function.identity()));
                // 科目map<科目编号,科目>
                Map<Integer, ProjectItem> projectItemMap = new HashMap<>();
                // 获取科目编号集合，并查询科目信息
                List<Integer> projectItemIds = courseList.stream().map(Course::getProjectItemId).collect(Collectors.toList());
                if (!projectItemIds.isEmpty()) {
                    List<ProjectItem> projectItemList = projectItemService.findByIds(projectItemIds);
                    projectItemMap = projectItemList.stream().collect(Collectors.toMap(ProjectItem::getId,Function.identity()));
                }
                videoIds = courseList.stream().map(Course::getId).collect(Collectors.toList());
                if (videoIds.isEmpty()) {
                    return new Response<>(OK,SUCCESS,response);
                }

                // 3.再次查询章节关联的视频数据
                courseCatalogChapterVideoQuery.setVideoIds(videoIds);
                // 查询数量
                Integer total = courseCatalogChapterVideoService.count(courseCatalogChapterVideoQuery);
                if (total == 0){
                    return new Response<>(OK,SUCCESS,response);
                }
                response.setTotal(total);
                Pager pager = new Pager(total,request.getPage(),request.getLimit());
                courseCatalogChapterVideoQuery.setStart(pager.getOffset());
                courseCatalogChapterVideoQuery.setLimit(pager.getLimit());
                // 判断查询结果是否有数据 组装返回数据
                List<CourseCatalogChapterVideo> courseCatalogChapterVideos = courseCatalogChapterVideoService.find(courseCatalogChapterVideoQuery);
                if (!isEmpty(courseCatalogChapterVideos) && !courseCatalogChapterVideos.isEmpty()) {
                    for (CourseCatalogChapterVideo courseCatalogChapterVideo : courseCatalogChapterVideos) {
                        CourseResponse courseResponse = new CourseResponse();
                        // 只在这里返回前端当key特别使用
                        courseResponse.setCourseCatalogChapterVideoId(courseCatalogChapterVideo.getId());
                        // 视频编号 名称 时长
                        courseResponse.setId(courseCatalogChapterVideo.getVideoId());
                        if (courseMap.containsKey(courseCatalogChapterVideo.getVideoId())) {
                            Course course = courseMap.get(courseCatalogChapterVideo.getVideoId());
                            courseResponse.setName(course.getName());
                            courseResponse.setLengthStr(DateUtil.turnSecondsToTimestring(Integer.parseInt(course.getLength())));
                            // 视频所属科目编号 名称
                            if (projectItemMap.containsKey(course.getProjectItemId())) {
                                ProjectItem projectItem = projectItemMap.get(course.getProjectItemId());
                                // 科目编号
                                courseResponse.setProjectItemId(projectItem.getId());
                                // 科目名称
                                courseResponse.setProjectItemName(projectItem.getName());
                            }
                        }
                        // 视频所属课程分类编号 名称
                        if (courseCatalogChapterMap.containsKey(courseCatalogChapterVideo.getChapterId())) {
                            Integer courseCatalogId = courseCatalogChapterMap.get(courseCatalogChapterVideo.getChapterId());
                            if (courseCatalogMap.containsKey(courseCatalogId)) {
                                CourseCatalog courseCatalog = courseCatalogMap.get(courseCatalogId);
                                courseResponse.setCourseCatalogId(courseCatalog.getId());
                                courseResponse.setCourseCatalogName(courseCatalog.getName());
                            }
                        }
                        response.getItems().add(courseResponse);
                    }
                }
            }
            return new Response<>(OK,SUCCESS,response);
        } catch (Exception e){
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR,FAILURE);
        }
    }


    /**
     * 用户每月学习计划关联,取消关联课程视频（批量）
     * @param request
     * @return
     * <AUTHOR>
     */
    @RequestMapping(value = "/v1/user/daily/learning/plan/bound/course/batch/modify")
    public Response<?> modifyBatchUserDailyLearningPlanBoundCourse(@RequestBody UserDailyLearningPlanCourseRequest request) {
        try {
            Date datetime = this.getServerTime();
            if (isEmpty(request.getUserDailyLearningPlanId())) {
                return new Response<>(ERROR,"请选择月学习计划");
            }
            if (isEmpty(request.getAssociationCatalog())) {
                return new Response<>(ERROR,"关联状态参数不能为空");
            }
            if (PublicAssociationCatalog.C0.getCode().equals(request.getAssociationCatalog())) {
                if (isEmpty(request.getCourseList()) || request.getCourseList().isEmpty()) {
                    return new Response<>(ERROR,"请选择需要关联的视频");
                }
            } else if (PublicAssociationCatalog.C1.getCode().equals(request.getAssociationCatalog())){
                if (isEmpty(request.getIds()) || request.getIds().isEmpty()) {
                    return new Response<>(ERROR,"请选择需要取消关联的数据");
                }
            } else {
                return new Response<>(ERROR,"关联状态参数错误");
            }
            // 查询每月学习计划数据状态
            UserDailyLearningPlan userDailyLearningPlan = userDailyLearningPlanService.findById(request.getUserDailyLearningPlanId());
            if (isEmpty(userDailyLearningPlan)) {
                return new Response<>(ERROR,"每月学习计划不存在");
            } else if (DataStatus.N.getCode().equals(userDailyLearningPlan.getStatus())){
                return new Response<>(ERROR,"每月学习计划已删除");
            }

            // 状态：已关联(关联)
            if (PublicAssociationCatalog.C0.getCode().equals(request.getAssociationCatalog())) {
                // 查询计划关联视频排序最大的数据
                UserDailyLearningPlanCourseQuery userDailyLearningPlanCourseQuery = new UserDailyLearningPlanCourseQuery();
                userDailyLearningPlanCourseQuery.setUserDailyLearningPlanId(request.getUserDailyLearningPlanId());
                userDailyLearningPlanCourseQuery.setStatus(DataStatus.Y.getCode());
                Integer sort = userDailyLearningPlanCourseService.sortMax(userDailyLearningPlanCourseQuery);

                // 组装每月学习计划关联视频数据
                List<UserDailyLearningPlanCourse> createUserDailyLearningPlanCourseList = new ArrayList<>();
                for (Course course : request.getCourseList()) {
                    if (isEmpty(course.getCatalogId())) {
                        return new Response<>(ERROR,"选择的视频中包含课程分类编号为空的视频");
                    }
                    if (isEmpty(course.getId())) {
                        return new Response<>(ERROR,"选择的视频中包含视频编号为空的视频");
                    }
                    sort = sort + 1;
                    UserDailyLearningPlanCourse userDailyLearningPlanCourseCreate = new UserDailyLearningPlanCourse();
                    userDailyLearningPlanCourseCreate.setUserDailyLearningPlanId(userDailyLearningPlan.getId());
                    userDailyLearningPlanCourseCreate.setCourseCatalogId(course.getCatalogId());
                    userDailyLearningPlanCourseCreate.setCourseId(course.getId());
                    userDailyLearningPlanCourseCreate.setAllocationStage(UserDailyLearningPlanCourseAllocationStage.NO.getCode());
                    userDailyLearningPlanCourseCreate.setSort(sort);
                    userDailyLearningPlanCourseCreate.setStatus(DataStatus.Y.getCode());
                    userDailyLearningPlanCourseCreate.setModifyTime(datetime);
                    userDailyLearningPlanCourseCreate.setCreateTime(datetime);
                    createUserDailyLearningPlanCourseList.add(userDailyLearningPlanCourseCreate);
                }
                // 执行新增关联操作
                userDailyLearningPlanCourseService.createBatch(createUserDailyLearningPlanCourseList);

                // 状态：未关联(取消关联)
            } else if (PublicAssociationCatalog.C1.getCode().equals(request.getAssociationCatalog())) {
                // 查询月计划绑定数据信息
                UserDailyLearningPlanCourseQuery userDailyLearningPlanCourseQuery = new UserDailyLearningPlanCourseQuery();
                userDailyLearningPlanCourseQuery.setUserDailyLearningPlanId(request.getUserDailyLearningPlanId());
                userDailyLearningPlanCourseQuery.setIds(request.getIds());
                userDailyLearningPlanCourseQuery.setStatus(DataStatus.Y.getCode());
                List<UserDailyLearningPlanCourse> userDailyLearningPlanCourseList = userDailyLearningPlanCourseService.findAll(userDailyLearningPlanCourseQuery);
                if (isEmpty(userDailyLearningPlanCourseList) || userDailyLearningPlanCourseList.isEmpty()) {
                    return new Response<>(ERROR,"该学习计划未关联选择的视频，无需取消关联");
                }
                // 组装公开课分类取消关联课程数据
                List<UserDailyLearningPlanCourse> removeUserDailyLearningPlanCourseList = new ArrayList<>();
                for (UserDailyLearningPlanCourse userDailyLearningPlanCourse : userDailyLearningPlanCourseList) {
                    if (UserDailyLearningPlanCourseAllocationStage.YES.getCode().equals(userDailyLearningPlanCourse.getAllocationStage())) {
                        return new Response<>(ERROR,"选择包含已分配数据,无法取消关联");
                    }
                    UserDailyLearningPlanCourse userDailyLearningPlanCourseRemove = new UserDailyLearningPlanCourse();
                    userDailyLearningPlanCourseRemove.setId(userDailyLearningPlanCourse.getId());
                    userDailyLearningPlanCourseRemove.setStatus(DataStatus.N.getCode());
                    userDailyLearningPlanCourseRemove.setModifyTime(datetime);
                    removeUserDailyLearningPlanCourseList.add(userDailyLearningPlanCourseRemove);
                }
                // 执行取消关联操作
                userDailyLearningPlanCourseService.removeBatch(removeUserDailyLearningPlanCourseList);
            }
            return new Response<>(OK,SUCCESS);
        } catch (Exception e){
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR,FAILURE);
        }
    }

    /**
     * 查询已经绑定到本月下的视频(分页)
     * <AUTHOR>
     * @date 2025-08-05
     */
    @RequestMapping("/v1/user/daily/learning/plain/course/query")
    public Response<?> queryPage(@RequestBody UserDailyLearningPlanCourseRequest request){
        try {
            Integer userDailyLearningPlanId = request.getUserDailyLearningPlanId();
            if (isEmpty(userDailyLearningPlanId)){
                return new Response<>(ERROR, "月计划编号不能为空");
            }
            UserDailyLearningPlan userDailyLearningPlan = userDailyLearningPlanService.findById(userDailyLearningPlanId);
            if (isEmpty(userDailyLearningPlan) || !DataStatus.Y.getCode().equals(userDailyLearningPlan.getStatus())){
                return new Response<>(ERROR, "月计划编号无效");
            }

            NewPageResponse<UserDailyLearningPlanCourseResponse> response = new NewPageResponse<>();
            response.setTotal(0);
            response.setItems(new ArrayList<>());
            UserDailyLearningPlanCourseQuery userDailyLearningPlanCourseQuery = new UserDailyLearningPlanCourseQuery();
            userDailyLearningPlanCourseQuery.setStatus(DataStatus.Y.getCode());
            userDailyLearningPlanCourseQuery.setUserDailyLearningPlanId(userDailyLearningPlanId);
            Integer count = userDailyLearningPlanCourseService.count(userDailyLearningPlanCourseQuery);
            if (count == 0){
                return new Response<>(OK, SUCCESS, response);
            }
            response.setTotal(count);
            Pager pager = new Pager(count, request.getPage(), request.getLimit());
            userDailyLearningPlanCourseQuery.setStart(pager.getOffset());
            userDailyLearningPlanCourseQuery.setLimit(pager.getLimit());
            List<UserDailyLearningPlanCourse> userDailyLearningPlanCourseList = userDailyLearningPlanCourseService.find(userDailyLearningPlanCourseQuery);
            if (isEmpty(userDailyLearningPlanCourseList) || userDailyLearningPlanCourseList.isEmpty()){
                return new Response<>(OK, SUCCESS, response);
            }
            List<Integer> courseIdList = userDailyLearningPlanCourseList.stream().map(UserDailyLearningPlanCourse::getCourseId).filter(a -> !isEmpty(a)).distinct().collect(Collectors.toList());
            Map<Integer, Course> courseMap = new HashMap<>();
            Map<Integer, ProjectItem> projectItemMap = new HashMap<>();
            Map<Integer, CourseCatalog> courseCatalogMap = new HashMap<>();

            if (!courseIdList.isEmpty()){
                CourseQuery courseQuery = new CourseQuery();
                courseQuery.setStatus(DataStatus.Y.getCode());
                courseQuery.setIds(courseIdList);
                List<Course> courseList = this.courseService.findAll(courseQuery);
                if (!isEmpty(courseList) && !courseList.isEmpty()){
                    courseMap = courseList.stream().collect(Collectors.toMap(Course::getId, Function.identity()));

                    //科目
                    List<Integer> projectItemIdList = courseList.stream().map(Course::getProjectItemId).filter(projectItemId -> !isEmpty(projectItemId)).distinct().collect(Collectors.toList());
                    if (!projectItemIdList.isEmpty()){
                        List<ProjectItem> projectItemList = projectItemService.findByIds(projectItemIdList);
                        if (!isEmpty(projectItemList) && !projectItemList.isEmpty()){
                            projectItemMap = projectItemList.stream().collect(Collectors.toMap(ProjectItem::getId, Function.identity()));
                        }
                    }

                    //课程分类
                    List<Integer> catalogIdList = courseList.stream().map(Course::getCatalogId).filter(catalogId -> !isEmpty(catalogId)).distinct().collect(Collectors.toList());
                    if (!catalogIdList.isEmpty()){
                        List<CourseCatalog> courseCatalogList = courseCatalogService.findByIds(catalogIdList);
                        if (!isEmpty(courseCatalogList) && !courseCatalogList.isEmpty()){
                            courseCatalogMap = courseCatalogList.stream().collect(Collectors.toMap(CourseCatalog::getId, Function.identity()));
                        }
                    }
                }
            }

            for (UserDailyLearningPlanCourse userDailyLearningPlanCourse : userDailyLearningPlanCourseList) {
                Integer courseId = userDailyLearningPlanCourse.getCourseId();
                UserDailyLearningPlanCourseResponse userDailyLearningPlanCourseResponse = new UserDailyLearningPlanCourseResponse();
                //ID
                userDailyLearningPlanCourseResponse.setId(userDailyLearningPlanCourse.getId());

                if (courseMap.containsKey(courseId)){
                    Course course = courseMap.get(courseId);

                    //科目
                    Integer projectItemId = course.getProjectItemId();
                    if (projectItemMap.containsKey(projectItemId)){
                        ProjectItem projectItem = projectItemMap.get(projectItemId);
                        userDailyLearningPlanCourseResponse.setProjectItemName(projectItem.getName());
                    }

                    //课程分类
                    Integer catalogId = course.getCatalogId();
                    if (courseCatalogMap.containsKey(catalogId)){
                        CourseCatalog courseCatalog = courseCatalogMap.get(catalogId);
                        userDailyLearningPlanCourseResponse.setCourseCatalogName(courseCatalog.getName());
                    }

                    //视频名称
                    userDailyLearningPlanCourseResponse.setCourseName(course.getName());

                    //时长
                    String length = course.getLength();
                    if (!isEmpty(length) && NumberUtil.isNumeric(length)){
                        userDailyLearningPlanCourseResponse.setLengthStr(DateUtil.turnSecondsToTimestring(Integer.parseInt(length)));
                    }

                }

                //是否分配 (0:未分配 1:已分配)
                userDailyLearningPlanCourseResponse.setAllocationStage(userDailyLearningPlanCourse.getAllocationStage());
                userDailyLearningPlanCourseResponse.setAllocationStageName(UserDailyLearningPlanCourseAllocationStage.getName(userDailyLearningPlanCourse.getAllocationStage()));
                response.getItems().add(userDailyLearningPlanCourseResponse);
            }
            return new Response<>(OK, SUCCESS, response);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }

}
