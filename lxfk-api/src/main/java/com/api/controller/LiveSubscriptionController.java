package com.api.controller;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.api.bean.LiveSubscriptionRequest;
import com.api.bean.LiveSubscriptionResponse;
import com.api.bean.NewPageResponse;
import com.api.bean.Response;
import com.api.bean.UserToken;
import com.api.config.Token;
import com.api.constant.DataStatus;
import com.api.validator.LiveSubscriptionValidator;
import com.common.bean.Pager;
import com.domain.CourseAuthor;
import com.domain.CourseCatalog;
import com.domain.Live;
import com.domain.LiveSubscription;
import com.domain.Project;
import com.domain.ProjectItem;
import com.domain.User;
import com.domain.complex.LiveSubscriptionQuery;
import com.service.CourseAuthorService;
import com.service.CourseCatalogService;
import com.service.LiveService;
import com.service.LiveSubscriptionService;
import com.service.ProjectItemService;
import com.service.ProjectService;
import com.service.UserService;

/**
 * @Description: 直播预约
 * @aouthor Geoffrey·Archie
 * @create 2020-08-27 14:24
 */
@RestController
public class LiveSubscriptionController extends BaseController {


    @Autowired
    private LiveSubscriptionService liveSubscriptionService;
    @Autowired
    private LiveService liveService;
    @Autowired
    private UserService userService;
    @Autowired
    private CourseAuthorService courseAuthorService;
    @Autowired
    private ProjectService projectService;
    @Autowired
    private ProjectItemService projectItemService;
    @Autowired
    private CourseCatalogService catalogService;


    /**
     * 直播预约列表查询
     *
     * @param request
     * @return
     * <AUTHOR> Archie
     */
    @RequestMapping("/v1/live/subscription/query")
    public Response<?> query(@RequestBody LiveSubscriptionRequest request) {
        NewPageResponse<LiveSubscriptionResponse> pageResponse = new NewPageResponse<>();
        try {
            LiveSubscriptionQuery query = new LiveSubscriptionQuery();
            query.setLiveId(request.getLiveId());
            if (Objects.nonNull(request.getUserName()) && !Objects.equals("",request.getUserName())) {
                User user = userService.findByUsername(request.getUserName());
                if (Objects.isNull(user)) {
                    return new Response<>(OK, SUCCESS, pageResponse);
                }
                query.setUserId(user.getId());
            }
            Integer total = liveSubscriptionService.count(query);
            Pager pager = new Pager(total, request.getPage(), request.getLimit());
            query.setStart(pager.getOffset());
            query.setLimit(pager.getLimit());
            List<LiveSubscription> liveSubscriptionList = liveSubscriptionService.find(query);
            pageResponse.setTotal(total);
            pageResponse.setItems(new ArrayList<>());
            if (!liveSubscriptionList.isEmpty() && liveSubscriptionList.size() > 0) {
                Set<Integer> liveIds = liveSubscriptionList.stream().map(LiveSubscription::getLiveId).collect(Collectors.toSet());
                Set<Integer> userIds = liveSubscriptionList.stream().map(LiveSubscription::getUserId).collect(Collectors.toSet());

                List<Live> liveList = liveService.findByIds(new ArrayList<Integer>() {{
                    addAll(liveIds);
                }});
                List<User> userList = userService.findByIds(new ArrayList<Integer>() {{
                    addAll(userIds);
                }});
                Map<Integer, Live> liveMap = liveList.stream().filter(item -> Objects.nonNull(item)).collect(Collectors.toMap(Live::getId, Function.identity()));
                Map<Integer, User> userMap = userList.stream().filter(item -> Objects.nonNull(item)).collect(Collectors.toMap(User::getId, Function.identity()));
                Map<Integer, Project> projectMap = null;
                Map<Integer, ProjectItem> projectItemMap = null;
                Map<Integer, CourseCatalog> courseCatalogMap = null;
                Map<Integer, CourseAuthor> authorMap = null;


                if (!liveList.isEmpty() && liveList.size() > 0) {
                    Set<Integer> projectIds = liveList.stream().map(Live::getProjectId).collect(Collectors.toSet());
                    Set<Integer> projectItemIds = liveList.stream().map(Live::getProjectItemId).collect(Collectors.toSet());
                    Set<Integer> catalogIds = liveList.stream().map(Live::getCatalogId).collect(Collectors.toSet());
                    Set<Integer> authorIds = liveList.stream().map(Live::getAuthorId).collect(Collectors.toSet());

                    List<Project> projectList = projectService.findByIds(new ArrayList<Integer>() {{
                        addAll(projectIds);
                    }});
                    List<ProjectItem> projectItemList = projectItemService.findByIds(new ArrayList<Integer>() {{
                        addAll(projectItemIds);
                    }});
                    List<CourseCatalog> catalogList = catalogService.findByIds(new ArrayList<Integer>() {{
                        addAll(catalogIds);
                    }});
                    List<CourseAuthor> authorList = courseAuthorService.findByIds(new ArrayList<Integer>() {{
                        addAll(authorIds);
                    }});

                    projectMap = projectList.stream().filter(item -> Objects.nonNull(item)).collect(Collectors.toMap(Project::getId, Function.identity()));
                    projectItemMap = projectItemList.stream().filter(item -> Objects.nonNull(item)).collect(Collectors.toMap(ProjectItem::getId, Function.identity()));
                    courseCatalogMap = catalogList.stream().filter(item -> Objects.nonNull(item)).collect(Collectors.toMap(CourseCatalog::getId, Function.identity()));
                    authorMap = authorList.stream().filter(item -> Objects.nonNull(item)).collect(Collectors.toMap(CourseAuthor::getId, Function.identity()));
                }

                Map<Integer, Project> finalProjectMap = projectMap;
                Map<Integer, ProjectItem> finalProjectItemMap = projectItemMap;
                Map<Integer, CourseCatalog> finalCourseCatalogMap = courseCatalogMap;
                Map<Integer, CourseAuthor> finalAuthorMap = authorMap;

                liveSubscriptionList.forEach(liveSubscription -> {
                    LiveSubscriptionResponse response = new LiveSubscriptionResponse();
                    if (!liveMap.isEmpty() && liveMap.containsKey(liveSubscription.getLiveId())) {
                        Live live = liveMap.get(liveSubscription.getLiveId());
                        if (Objects.nonNull(live)) {
                            if (!finalProjectMap.isEmpty() && finalProjectMap.containsKey(live.getProjectId())) {
                                response.setProjectName(finalProjectMap.get(live.getProjectId()).getName());
                            }
                            if (!finalProjectItemMap.isEmpty() && finalProjectItemMap.containsKey(live.getProjectItemId())) {
                                response.setProjectItemName(finalProjectItemMap.get(live.getProjectItemId()).getName());
                            }
                            if (!finalCourseCatalogMap.isEmpty() && finalCourseCatalogMap.containsKey(live.getCatalogId())) {
                                response.setCatalogName(finalCourseCatalogMap.get(live.getCatalogId()).getName());
                            }
                            if (!finalAuthorMap.isEmpty() && finalAuthorMap.containsKey(live.getAuthorId())) {
                                CourseAuthor author = finalAuthorMap.get(live.getAuthorId());
                                response.setAuthorName(author.getName());
                                response.setAuthorAvatar(Objects.nonNull(author.getAvatar()) ? author.getAvatar() : "");
                            }
                        }
                    }
//                    if (!userMap.isEmpty() && userMap.containsKey(liveSubscription.getUserId())) {
//                        response.setUserName(userMap.get(liveSubscription.getUserId()).getUsername());
//                    }
                    response.setId(liveSubscription.getId());
                    response.setCustomerId(liveSubscription.getCustomerId());
                    response.setUserId(liveSubscription.getUserId());
                    response.setLiveId(liveSubscription.getLiveId());
                    response.setCreateTime(liveSubscription.getCreateTime());
                    response.setModifyTime(liveSubscription.getModifyTime());
                    pageResponse.getItems().add(response);
                });
            }
            return new Response<>(OK, SUCCESS, pageResponse);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }


    /**
     * 预约直播
     *
     * @param request
     * @return
     * <AUTHOR> Archie
     */
    @Token
    @RequestMapping("/v1/live/subscription/create")
    public Response<?> create(@RequestBody LiveSubscriptionRequest request) {
        try {
            LiveSubscriptionValidator validator = new LiveSubscriptionValidator();
            UserToken userToken = getUserToken();
            if (validator.onLiveId(request.getLiveId())
                    .onToken(userToken.getId())
                    .result()) {
                Date date = getServerTime();
                LiveSubscription liveSubscription = new LiveSubscription();
                liveSubscription.setLiveId(request.getLiveId());
                liveSubscription.setCustomerId(getUserToken().getCustomerId());
                liveSubscription.setUserId(getUserToken().getId());
                liveSubscription.setStatus(DataStatus.Y.getCode());
                liveSubscription.setModifyTime(date);
                liveSubscription.setCreateTime(date);
                liveSubscriptionService.create(liveSubscription);
                return new Response<>(OK, SUCCESS);
            }
            return new Response<>(ERROR, validator.getErrorMessage());
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }

}