package com.api.controller;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.aliyun.dingtalkrobot_1_0.models.BatchSendOTORequest;
import com.api.bean.ChapterExamPaperRecordDetailResponse;
import com.api.bean.CustomerRankingListResponse;
import com.api.bean.DingMessageSampleTextParam;
import com.api.bean.ExamPaperQuestionAnalysisResponse;
import com.api.bean.ExamPaperRecordWrongReportResponse;
import com.api.bean.ExamPaperRequest;
import com.api.bean.ExamPaperTypeResponse;
import com.api.bean.MyQuestionItemRecordResponse;
import com.api.bean.MyQuestionRecordDateResponse;
import com.api.bean.MyQuestionRecordRequest;
import com.api.bean.MyQuestionRecordResponse;
import com.api.bean.NewPageResponse;
import com.api.bean.OrderProductResponseV3;
import com.api.bean.ProjectItemDataResponse;
import com.api.bean.QuestionAnalyticResponse;
import com.api.bean.QuestionChapterResponse;
import com.api.bean.QuestionRequest;
import com.api.bean.QuestionResponse;
import com.api.bean.QuestionTemplateItemResponse;
import com.api.bean.QuestionUserExamPaperRecordResponse;
import com.api.bean.UserExamPaperQuestionNoteResponse;
import com.api.bean.UserExamPaperRecordDetailRequest;
import com.api.bean.UserExamPaperRecordDetailResponse;
import com.api.bean.UserExamPaperRecordExport;
import com.api.bean.UserExamPaperRecordRequest;
import com.api.bean.UserExamPaperRecordResponse;
import com.api.bean.UserExamPaperRecordScoreResponse;
import com.api.bean.UserExamQuestionHistoryResponse;
import com.api.bean.UserToken;
import com.api.config.MQProducer;
import com.api.config.Token;
import com.api.sequence.ExportSequence;
import com.api.sequence.LogSequence;
import com.api.validator.UserExamPaperRecordDetailValidator;
import com.api.validator.UserExamPaperRecordValidator;
import com.common.bean.Pager;
import com.common.bean.QuestionOptionResponse;
import com.common.bean.Response;
import com.common.constant.App;
import com.common.constant.CacheKey;
import com.common.constant.DataStatus;
import com.common.constant.DingMessageKey;
import com.common.constant.ExamAnalysisStatus;
import com.common.constant.ExamPaperCorrectSwitch;
import com.common.constant.ExportAction;
import com.common.constant.ExportStage;
import com.common.constant.ExportType;
import com.common.constant.MyQuestionRecordShowType;
import com.common.constant.PublicShow;
import com.common.constant.PublicStage;
import com.common.constant.PublicSwitch;
import com.common.constant.QuestionArguments;
import com.common.constant.QuestionBankType;
import com.common.constant.QuestionBaseType;
import com.common.constant.QuestionCatalogV3;
import com.common.constant.QuestionChapterCatalog;
import com.common.constant.QuestionDifficultyLevel;
import com.common.constant.QuestionStarLevel;
import com.common.constant.UserExamOverStatus;
import com.common.constant.UserExamPaperRecordCatalog;
import com.common.constant.UserExamPaperRecordDetailAiScoreStatus;
import com.common.constant.UserExamPaperRecordDetailMark;
import com.common.constant.UserExamPaperRecordDetailQuestionResult;
import com.common.constant.UserExamPaperRecordOver;
import com.common.constant.UserExamPaperRecordPlayType;
import com.common.constant.UserExamRecordOver;
import com.common.constant.UserExamRecordReadOver;
import com.common.mq.MQTag;
import com.common.mq.MQTopic;
import com.common.util.DateUtil;
import com.domain.Customer;
import com.domain.ExamPaper;
import com.domain.ExamPaperQuestion;
import com.domain.ExamPaperQuestionScorePoint;
import com.domain.ExportLog;
import com.domain.Project;
import com.domain.ProjectItem;
import com.domain.Question;
import com.domain.QuestionChapter;
import com.domain.QuestionLabel;
import com.domain.QuestionLabelItem;
import com.domain.QuestionLibrary;
import com.domain.QuestionScorePoint;
import com.domain.QuestionTemplateItem;
import com.domain.SysUser;
import com.domain.SysUserMaster;
import com.domain.User;
import com.domain.UserExamPaper;
import com.domain.UserExamPaperQuestion;
import com.domain.UserExamPaperQuestionFavorite;
import com.domain.UserExamPaperQuestionNote;
import com.domain.UserExamPaperRecord;
import com.domain.UserExamPaperRecordDetail;
import com.domain.UserExamPaperRecordScore;
import com.domain.UserLearningDayStatistic;
import com.domain.UserTeacher;
import com.domain.complex.ExamPaperQuery;
import com.domain.complex.ExamPaperQuestionQuery;
import com.domain.complex.ExamPaperQuestionScorePointQuery;
import com.domain.complex.ProjectItemQuery;
import com.domain.complex.QuestionChapterQuery;
import com.domain.complex.QuestionLabelItemQuery;
import com.domain.complex.QuestionQuery;
import com.domain.complex.QuestionScorePointQuery;
import com.domain.complex.QuestionTemplateItemQuery;
import com.domain.complex.SysUserMasterQuery;
import com.domain.complex.UserExamPaperQuery;
import com.domain.complex.UserExamPaperQuestionFavoriteQuery;
import com.domain.complex.UserExamPaperQuestionNoteQuery;
import com.domain.complex.UserExamPaperQuestionQuery;
import com.domain.complex.UserExamPaperRecordDetailQuery;
import com.domain.complex.UserExamPaperRecordQuery;
import com.domain.complex.UserExamPaperRecordScoreQuery;
import com.domain.complex.UserLearningDayStatisticQuery;
import com.domain.complex.UserQuery;
import com.domain.complex.UserTeacherQuery;
import com.service.CustomerService;
import com.service.ExamPaperQuestionScorePointService;
import com.service.ExamPaperQuestionService;
import com.service.ExamPaperService;
import com.service.ExportLogService;
import com.service.ProjectItemService;
import com.service.ProjectService;
import com.service.QuestionChapterService;
import com.service.QuestionLabelItemService;
import com.service.QuestionLabelService;
import com.service.QuestionLibraryService;
import com.service.QuestionScorePointService;
import com.service.QuestionService;
import com.service.QuestionTemplateItemService;
import com.service.SysUserMasterService;
import com.service.SysUserService;
import com.service.UserExamPaperQuestionFavoriteService;
import com.service.UserExamPaperQuestionNoteService;
import com.service.UserExamPaperQuestionService;
import com.service.UserExamPaperRecordDetailService;
import com.service.UserExamPaperRecordScoreService;
import com.service.UserExamPaperRecordService;
import com.service.UserExamPaperService;
import com.service.UserLearningDayStatisticService;
import com.service.UserService;
import com.service.UserTeacherService;

/**
 * 用户考卷记录
 *
 * @date 2024-03-03 09:58:10
 */
@RestController
public class UserExamPaperRecordController extends ExamPaperBaseController {
    @Autowired
    private UserExamPaperRecordService userExamPaperRecordService;
    @Autowired
    private ExamPaperService examPaperService;
    @Autowired
    private ProjectService projectService;
    @Autowired
    private SysUserService sysUserService;
    @Autowired
    private UserExamPaperRecordDetailService userExamPaperRecordDetailService;
    @Autowired
    private QuestionService questionService;
    @Autowired
    private ProjectItemService projectItemService;
    @Autowired
    private QuestionChapterService questionChapterService;
    @Autowired
    private UserExamPaperQuestionFavoriteService userExamPaperQuestionFavoriteService;
    @Autowired
    private UserExamPaperQuestionNoteService userExamPaperQuestionNoteService;
    @Autowired
    private UserExamPaperService userExamPaperService;
    @Autowired
    private UserExamPaperQuestionService userExamPaperQuestionService;
    @Autowired
    private ExamPaperQuestionService examPaperQuestionService;
    @Autowired
    private QuestionTemplateItemService questionTemplateItemService;
    @Autowired
    private CustomerService customerService;
    @Autowired
    private ExportLogService exportLogService;
    @Autowired
    private RedisTemplate<String, String> redisTemplate;
    @Autowired
    private UserService userService;
    @Autowired
    private SysUserMasterService sysUserMasterService;
    @Autowired
    private UserTeacherService userTeacherService;
    @Autowired
    private QuestionLabelItemService questionLabelItemService;
    @Autowired
    private QuestionLibraryService questionLibraryService;
    @Autowired
    private UserLearningDayStatisticService userLearningDayStatisticService;
    @Autowired
    private UserExamPaperRecordScoreService userExamPaperRecordScoreService;
    @Autowired
    private QuestionScorePointService questionScorePointService;
    @Autowired
    private ExamPaperQuestionScorePointService examPaperQuestionScorePointService;
    @Autowired
    private QuestionLabelService questionLabelService;

    //错题报告展示知识点数量限制
    private static int EXAM_PAPER_RECORD_WRONG_REPORT_LIMIT = 8;
    /**
     * 做题报告
     * <AUTHOR>
     * @date 2024-03-29
     */
    @RequestMapping(value = "/v1/home/<USER>/paper/record/id/query")
    public Response<?> queryById(@RequestBody UserExamPaperRecordRequest request){
        try{
            UserExamPaperRecordValidator validator = new UserExamPaperRecordValidator();
            if (!validator.onId(request.getId()).result()){
                return new Response<>(ERROR,validator.getErrorMessage());
            }

            //查询 做卷记录
            UserExamPaperRecord userExamPaperRecord = userExamPaperRecordService.findById(request.getId());
            if (isEmpty(userExamPaperRecord) || !DataStatus.Y.getCode().equals(userExamPaperRecord.getStatus())){
                return new Response<>(ERROR, "做卷记录编号无效");
            }
            //查询当前做卷的科目信息 只有[科目练习]和[课后练习-试卷]的试卷 才有可能有科目信息
            ProjectItem projectItem = null;
            if (!isEmpty(userExamPaperRecord.getExamPaperId()) && !isEmpty(userExamPaperRecord.getCatalog()) && Arrays.asList(UserExamPaperRecordCatalog.C4.getCode(),UserExamPaperRecordCatalog.C5.getCode()).contains(userExamPaperRecord.getCatalog())){
                ExamPaper examPaper = examPaperService.findById(userExamPaperRecord.getExamPaperId());
                if (!isEmpty(examPaper) && !isEmpty(examPaper.getProjectItemId())){
                    projectItem = projectItemService.findById(examPaper.getProjectItemId());
                }
            }

            //试题详情  K:试题ID V:试题
            Map<Integer, Question> questionMap = new HashMap<>();
            Map<Integer, QuestionTemplateItem> questionTemplateItemMap = new HashMap<>();
            Set<Integer> questionTemplateItemIdSet = new HashSet<>();

            //查询 做卷详情
            UserExamPaperRecordDetailQuery userExamPaperRecordDetailQuery = new UserExamPaperRecordDetailQuery();
            userExamPaperRecordDetailQuery.setStatus(DataStatus.Y.getCode());
            userExamPaperRecordDetailQuery.setUserExamPaperRecordId(request.getId());
            List<UserExamPaperRecordDetail> userExamPaperRecordDetailList = userExamPaperRecordDetailService.findAll(userExamPaperRecordDetailQuery);
            if (!isEmpty(userExamPaperRecordDetailList) && !userExamPaperRecordDetailList.isEmpty()){
                List<Integer> questionIdList = userExamPaperRecordDetailList.stream().filter(userExamPaperRecordDetail -> !isEmpty(userExamPaperRecordDetail.getQuestionId())).map(UserExamPaperRecordDetail::getQuestionId).collect(Collectors.toList());
                if (!questionIdList.isEmpty()){
                    //查询 试题
                    List<Question> questionList = questionService.findByIds(questionIdList);
                    if (!isEmpty(questionList) && !questionList.isEmpty()){
                        for (Question question : questionList) {
                            questionMap.put(question.getId(), question);
                            if (!isEmpty(question.getQuestionTemplateItemId())){
                                questionTemplateItemIdSet.add(question.getQuestionTemplateItemId());
                            }
                        }
                        if (!questionTemplateItemIdSet.isEmpty()){
                            //查询 题型模板
                            List<QuestionTemplateItem> questionTemplateItemList = questionTemplateItemService.findByIds(new ArrayList<>(questionTemplateItemIdSet));
                            if (!isEmpty(questionTemplateItemList) && !questionTemplateItemList.isEmpty()){
                                questionTemplateItemMap = questionTemplateItemList.stream().collect(Collectors.toMap(QuestionTemplateItem::getId, Function.identity()));
                            }
                        }
                    }
                }
            }

            //构造相应数据
            UserExamPaperRecordResponse response = new UserExamPaperRecordResponse();
            response.setId(userExamPaperRecord.getId());
            //科目
            if (projectItem != null){
                response.setProjectItemId(projectItem.getId());
                response.setProjectItemName(projectItem.getName());
            }
            //得分
            response.setSumScore(userExamPaperRecord.getSumScore());
            //交卷时间(做卷结束时间)
            if (!isEmpty(userExamPaperRecord.getEndTime())){
                response.setEndTime(DateUtil.format(userExamPaperRecord.getEndTime(), DATETIME_FORMAT));
            }
            //耗时
            response.setDuration(userExamPaperRecord.getDuration());
            //试题数量
            response.setQuestionNumber(userExamPaperRecord.getQuestionNumber());
            //试卷正确率
            response.setRightRate(userExamPaperRecord.getRightRate());
            //试卷得分率
            response.setScoreRate(userExamPaperRecord.getScoreRate());
            //自定义题型列表
            List<QuestionTemplateItemResponse> questionTemplateItemResponseList = new ArrayList<>();
            if (!questionTemplateItemMap.isEmpty()){
                for (Map.Entry<Integer, QuestionTemplateItem> questionTemplateItemEntry : questionTemplateItemMap.entrySet()) {
                    QuestionTemplateItem questionTemplateItem = questionTemplateItemEntry.getValue();
                    QuestionTemplateItemResponse questionTemplateItemResponse = new QuestionTemplateItemResponse();
                    questionTemplateItemResponse.setId(questionTemplateItem.getId());
                    questionTemplateItemResponse.setName(questionTemplateItem.getName());
                    questionTemplateItemResponse.setSort(questionTemplateItem.getSort());
                    questionTemplateItemResponse.setQuestionType(questionTemplateItem.getQuestionType());
                    questionTemplateItemResponseList.add(questionTemplateItemResponse);
                }
            }
            response.setQuestionTemplateItemResponseList(questionTemplateItemResponseList);
            //答题卡
            List<UserExamPaperRecordDetailResponse> detailResponseList = this.toBeanUserExamPaperRecordDetailResponse(questionMap,questionTemplateItemMap,userExamPaperRecord,userExamPaperRecordDetailList);
            response.setUserExamPaperRecordDetailResponses(detailResponseList);
            //试卷总分 2025-06-28 李明洋 新增入参:playType,兼容单题练习和星级组卷
            response.setTotalScore(getTotalScore(userExamPaperRecord.getCatalog(), userExamPaperRecord.getPlayType(), userExamPaperRecord.getExamPaperId()));
            return new Response<>(OK,SUCCESS,response);
        }catch (Exception e){
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }

    /**
     * 封装做题记录详情
     * <AUTHOR>
     */
    public List<UserExamPaperRecordDetailResponse> toBeanUserExamPaperRecordDetailResponse(Map<Integer, Question> questionMap,
                                                                                           Map<Integer, QuestionTemplateItem> questionTemplateItemMap,
                                                                                           UserExamPaperRecord userExamPaperRecord,
                                                                                           List<UserExamPaperRecordDetail> userExamPaperRecordDetailList) {
        Map<Integer, ExamPaperQuestion> examPaperQuestionMap = new HashMap<>();
        List<UserExamPaperRecordDetailResponse> detailResponseList = new ArrayList<>();
        //区分系统卷和自己组的卷 playType是试卷的,或者为空的才是真正的系统卷
        boolean isSysExamPaper = QuestionArguments.SYS_EXAM_PAPER_CATALOG_CODE_LIST.contains(userExamPaperRecord.getCatalog()) && ( isEmpty(userExamPaperRecord.getPlayType()) || UserExamPaperRecordPlayType.C0.getCode().equals(userExamPaperRecord.getPlayType()) );
        if(isSysExamPaper){
            ExamPaper examPaper = examPaperService.findById(userExamPaperRecord.getExamPaperId());
            if(examPaper == null){
                return detailResponseList;
            }
            //查询试卷题目关联关系
            ExamPaperQuestionQuery examPaperQuestionQuery = new ExamPaperQuestionQuery();
            examPaperQuestionQuery.setExamPaperId(examPaper.getId());
            examPaperQuestionQuery.setStatus(DataStatus.Y.getCode());
            List<ExamPaperQuestion> examPaperQuestionList = examPaperQuestionService.findAll(examPaperQuestionQuery);
            examPaperQuestionMap = examPaperQuestionList.stream().collect(Collectors.toMap(ExamPaperQuestion::getQuestionId, Function.identity()));
        }

        //试题父ID分组
        Map<Integer, List<Question>> questionParentIdMap = questionMap.values().stream().filter(a -> !isEmpty(a.getParentId())).collect(Collectors.groupingBy(Question::getParentId));
        //试题ID映射做题记录
        Map<Integer,UserExamPaperRecordDetail> userExamPaperRecordDetailQuestionIdMap = userExamPaperRecordDetailList.stream().filter(a -> !isEmpty(a.getQuestionId())).collect(Collectors.toMap(UserExamPaperRecordDetail::getQuestionId, Function.identity(), (a,b)-> a));

        if (!isEmpty(userExamPaperRecordDetailList) && !userExamPaperRecordDetailList.isEmpty()){

            //查询主观题智能评分详情列表
            Map<Integer, List<UserExamPaperRecordScore>> recordDetailIdMappingRecordScoreMap = new HashMap<>();
            List<Integer> userExamPaperRecordDetailIdList = userExamPaperRecordDetailList.stream().map(UserExamPaperRecordDetail::getId).collect(Collectors.toList());
            UserExamPaperRecordScoreQuery userExamPaperRecordScoreQuery = new UserExamPaperRecordScoreQuery();
            userExamPaperRecordScoreQuery.setStatus(DataStatus.Y.getCode());
            userExamPaperRecordScoreQuery.setDetailIds(userExamPaperRecordDetailIdList);
            List<UserExamPaperRecordScore> userExamPaperRecordScoreList = userExamPaperRecordScoreService.findAll(userExamPaperRecordScoreQuery);
            if (!isEmpty(userExamPaperRecordScoreList) && !userExamPaperRecordScoreList.isEmpty()){
                recordDetailIdMappingRecordScoreMap = userExamPaperRecordScoreList.stream().collect(Collectors.groupingBy(UserExamPaperRecordScore::getDetailId));
            }

            for (UserExamPaperRecordDetail userExamPaperRecordDetail : userExamPaperRecordDetailList) {
                Integer questionId = userExamPaperRecordDetail.getQuestionId();
                UserExamPaperRecordDetailResponse detailResponse = new UserExamPaperRecordDetailResponse();
                //主观题智能评分详情列表
                if (recordDetailIdMappingRecordScoreMap.containsKey(userExamPaperRecordDetail.getId())){
                    List<UserExamPaperRecordScoreResponse> userExamPaperRecordScoreResponses = new ArrayList<>();
                    for (UserExamPaperRecordScore userExamPaperRecordScore : recordDetailIdMappingRecordScoreMap.get(userExamPaperRecordDetail.getId())) {
                        UserExamPaperRecordScoreResponse userExamPaperRecordScoreResponse = new UserExamPaperRecordScoreResponse();
                        //得分点评分记录ID
                        userExamPaperRecordScoreResponse.setId(userExamPaperRecordScore.getId());
                        //采分类型
                        userExamPaperRecordScoreResponse.setStepType(userExamPaperRecordScore.getStepType());
                        //采分点
                        userExamPaperRecordScoreResponse.setScorePoint(userExamPaperRecordScore.getScorePoint());
                        //获得分值
                        userExamPaperRecordScoreResponse.setUserScore(userExamPaperRecordScore.getUserScore());
                        //原分值
                        userExamPaperRecordScoreResponse.setScore(userExamPaperRecordScore.getScore());
                        userExamPaperRecordScoreResponses.add(userExamPaperRecordScoreResponse);
                    }
                    detailResponse.setUserExamPaperRecordScoreResponseList(userExamPaperRecordScoreResponses);
                }

                //试题ID
                detailResponse.setQuestionId(questionId);
                //标记
                detailResponse.setMark(userExamPaperRecordDetail.getMark());
                //题型
                if (questionMap.containsKey(questionId)){
                    Question question = questionMap.get(questionId);
                    //如果是材料题的小题 不进行返回
                    if (0 != question.getParentId()){
                        continue;
                    }
                    if (questionTemplateItemMap.containsKey(question.getQuestionTemplateItemId())){
                        QuestionTemplateItem questionTemplateItem = questionTemplateItemMap.get(question.getQuestionTemplateItemId());
                        //题型模板ID
                        detailResponse.setQuestionTemplateItemId(questionTemplateItem.getId());
                        //真实题型
                        detailResponse.setQuestionType(questionTemplateItem.getQuestionType());
                        //自定义题型名称
                        detailResponse.setQuestionTemplateItemName(questionTemplateItem.getName());
                    }
                }
                //答题状态:已答(答案不为空) 未答(答案为空)
                detailResponse.setAnswer(userExamPaperRecordDetail.getAnswer());
                detailResponse.setCustomerImgUrl(userExamPaperRecordDetail.getCustomerImgUrls());
                //对错结果
                detailResponse.setQuestionResult(userExamPaperRecordDetail.getQuestionResult());
                //排序字段
                if(examPaperQuestionMap.containsKey(questionId)){
                    detailResponse.setQuestionTemplateItemSort(examPaperQuestionMap.get(questionId).getSort());
                }
                //题目分值(区分系统试卷和用户自己组卷)
                detailResponse.setScore(userExamPaperRecordDetail.getQuestionScore());
                //用户得分
                detailResponse.setUserScore(userExamPaperRecordDetail.getScore());
                //题目分值(区分系统试卷和用户自己组卷)   用户得分
                BigDecimal score = BigDecimal.ZERO, userScore = BigDecimal.ZERO;
                //如果还能拿到这个试题的基本信息,并且知道他是个材料题 就去取出来他的小题列表,并从记录中获取这些小题的答题情况,计算他获取的总分值
                if (questionMap.containsKey(questionId) && QuestionBaseType.C6.getCode().equals(questionMap.get(questionId).getType())){
                    if(questionParentIdMap.containsKey(questionId)){
                        List<Question> smallQuestionList = questionParentIdMap.get(questionId);
                        for (Question smallQuestion : smallQuestionList) {
                            if (userExamPaperRecordDetailQuestionIdMap.containsKey(smallQuestion.getId())){
                                UserExamPaperRecordDetail smallUserExamPaperRecordDetail = userExamPaperRecordDetailQuestionIdMap.get(smallQuestion.getId());
                                if (!isEmpty(smallUserExamPaperRecordDetail.getQuestionScore())){
                                    score = score.add(smallUserExamPaperRecordDetail.getQuestionScore());
                                }
                                if (!isEmpty(smallUserExamPaperRecordDetail.getScore())){
                                    userScore = userScore.add(smallUserExamPaperRecordDetail.getScore());
                                }
                            }
                        }
                    }
                } else {
                    //如果数据库中删除了这个题目,我们也不知道这个题目的真实类型了,或者这个题目不是材料题,就不再计算,或者赋null值
                    score = userExamPaperRecordDetail.getQuestionScore();
                    userScore = userExamPaperRecordDetail.getScore();
                }

                detailResponse.setScore(score);
                detailResponse.setUserScore(userScore);
                detailResponseList.add(detailResponse);
            }
        }
        if(isSysExamPaper){
            //系统卷按照排序字段排序
            detailResponseList = detailResponseList.stream().filter(detail -> !isEmpty(detail.getQuestionTemplateItemSort()))
                    .sorted(Comparator.comparing(UserExamPaperRecordDetailResponse::getQuestionTemplateItemSort))
                    .collect(Collectors.toList());
        }else{
            //虚拟卷先按题目类型排序 再按ID排序
            detailResponseList = detailResponseList.stream().filter(detail -> !isEmpty(detail.getQuestionType()))
                    .sorted(Comparator.comparing(UserExamPaperRecordDetailResponse::getQuestionType).thenComparing(UserExamPaperRecordDetailResponse::getQuestionId))
                    .collect(Collectors.toList());
        }
        return detailResponseList;
    }
    /**
     * 做题报告接口中获取试卷总分的方法
     * 1.后台创建试卷:直接从表属性中获取; 2.快速练习、智能组卷、课后练习从tb_user_exam_paper和question关联表中统计获取
     * 2025-06-28 李明洋 新增入参:playType,兼容单题练习和星级组卷
     * @return 当前试卷总分
     * <AUTHOR>
     * @date 2024-04-16
     */
    private BigDecimal getTotalScore(String userExamPaperRecordCatalog, String playType, Integer examPaperId){
        BigDecimal result = BigDecimal.ZERO;

        if (isEmpty(userExamPaperRecordCatalog) || isEmpty(examPaperId)){
            return result;
        }

        UserExamPaperRecordCatalog catalog = UserExamPaperRecordCatalog.get(userExamPaperRecordCatalog);
        if (catalog == null){
            return result;
        }

        if (isEmpty(playType) || UserExamPaperRecordPlayType.C0.getCode().equals(playType)){
            switch (catalog){
                case C0: case C1: case C6: case C7:
                    UserExamPaperQuestionQuery userExamPaperQuestionQuery = new UserExamPaperQuestionQuery();
                    userExamPaperQuestionQuery.setStatus(DataStatus.Y.getCode());
                    userExamPaperQuestionQuery.setExamPaperId(examPaperId);
                    List<UserExamPaperQuestion> userExamPaperQuestionList = userExamPaperQuestionService.findAll(userExamPaperQuestionQuery);
                    if (!isEmpty(userExamPaperQuestionList) && !userExamPaperQuestionList.isEmpty()){
                        for (UserExamPaperQuestion userExamPaperQuestion : userExamPaperQuestionList) {
                            if (!isEmpty(userExamPaperQuestion.getScore())){
                                result = result.add(userExamPaperQuestion.getScore());
                            }
                        }
                    }
                    break;
                case C2: case C3: case C4: case C5:
                    ExamPaper examPaper = examPaperService.findById(examPaperId);
                    if (!isEmpty(examPaper) && !isEmpty(examPaper.getTotalScore())){
                        result = examPaper.getTotalScore();
                    }
                    break;
                default:
                    break;
            }
        } else if (UserExamPaperRecordPlayType.C1.getCode().equals(playType) || UserExamPaperRecordPlayType.C2.getCode().equals(playType)){
            UserExamPaperQuestionQuery userExamPaperQuestionQuery = new UserExamPaperQuestionQuery();
            userExamPaperQuestionQuery.setStatus(DataStatus.Y.getCode());
            userExamPaperQuestionQuery.setExamPaperId(examPaperId);
            List<UserExamPaperQuestion> userExamPaperQuestionList = userExamPaperQuestionService.findAll(userExamPaperQuestionQuery);
            if (!isEmpty(userExamPaperQuestionList) && !userExamPaperQuestionList.isEmpty()){
                for (UserExamPaperQuestion userExamPaperQuestion : userExamPaperQuestionList) {
                    if (!isEmpty(userExamPaperQuestion.getScore())){
                        result = result.add(userExamPaperQuestion.getScore());
                    }
                }
            }
        }

        return result;
    }

    /**
     * 交卷/保存 官网 APP
     * 生成做题报告 返回做卷记录ID 根据此ID可调用查询做题报告接口查看详情
     * 2024-08-08 李明洋 得分率: 修改计算规则: 总得分/试卷总分=得分率 (之前计算规则: 总得分/可判断对错的试题+自己打过分的试题的总分值)
     * <AUTHOR>
     * @date 2024-03-28
     */
    @RequestMapping(value = "/v1/home/<USER>/paper/record/save/or/submit")
    @Token
    public Response<?> saveOrSubmit(@RequestBody UserExamPaperRecordRequest request){
        Integer customerId = getUserToken().getCustomerId();
        Lock lock = getLock(redisTemplate, CacheKey.USER_EXAM_PAPER_RECORD_SAVE_OR_SUBMIT, String.valueOf(customerId));
        if (!lock.tryLock()) {
            return new Response<>(ERROR, "请勿点击过快");
        }
        try{
            //1.1 校验 交卷基础信息
            UserExamPaperRecordValidator validator = new UserExamPaperRecordValidator();
            if (!validator
                    .onProjectId(request.getProjectId())
                    .onExamPaperId(request.getExamPaperId())
                    .onCatalog(request.getCatalog())
                    .onOver(request.getOver())
                    .onUserExamRecordDetailRequests(request.getUserExamPaperRecordDetailRequests())
                    .onMode(request.getMode())
                    .result()){
                return new Response<>(ERROR,validator.getErrorMessage());
            }

            if (!isEmpty(request.getPlayType()) && UserExamPaperRecordPlayType.get(request.getPlayType()) == null){
                return new Response<>(ERROR, "做卷类型无效");
            }

            //1.2 校验 交卷每题详细信息 搜集交卷详情Map K:试题ID V:做题详情(可能包含的参数:id,answer)
            Map<Integer, UserExamPaperRecordDetailRequest> questionIdMappingUserExamPaperRecordDetailRequestMap = new HashMap<>();
            UserExamPaperRecordDetailValidator detailValidator;
            for (UserExamPaperRecordDetailRequest userExamPaperRecordDetailRequest : request.getUserExamPaperRecordDetailRequests()) {
                detailValidator = new UserExamPaperRecordDetailValidator();
                if (!detailValidator
                        .onQuestionId(userExamPaperRecordDetailRequest.getQuestionId())
                        .onMark(userExamPaperRecordDetailRequest.getMark())
                        .result()){
                    return new Response<>(ERROR, detailValidator.getErrorMessage());
                }
                if (questionIdMappingUserExamPaperRecordDetailRequestMap.containsKey(userExamPaperRecordDetailRequest.getQuestionId())){
                    return new Response<>(ERROR, "试题编号存在重复");
                }
                questionIdMappingUserExamPaperRecordDetailRequestMap.put(userExamPaperRecordDetailRequest.getQuestionId(), userExamPaperRecordDetailRequest);
            }

            //获取当前试卷类型是否是系统试卷 true:系统试卷  false:用户组卷     提前获取一个区分标识,方便后续使用
            boolean isSysExamPaper = QuestionArguments.SYS_EXAM_PAPER_CATALOG_CODE_LIST.contains(request.getCatalog());
            if (!isEmpty(request.getUseUserExamPaper()) && request.getUseUserExamPaper()){
                isSysExamPaper = false;
            }
            Date serverTime = getServerTime();

            //2.1 查询试卷详情 包含试题列表(注意用户组卷中材料题小题需要额外查询)
            Map<Integer, ExamPaperQuestion> questionIdMappingExamPaperQuestionMap = new HashMap<>();
            List<Question> questionList = new ArrayList<>();
            String examPaperName = null;
            String correctSwitch = null;
            if (isSysExamPaper){
                ExamPaper examPaper = examPaperService.findById(request.getExamPaperId());
                if (examPaper == null || !DataStatus.Y.getCode().equals(examPaper.getStatus()) || !PublicStage.Y.getCode().equals(examPaper.getStage())){
                    return new Response<>(ERROR, "试卷不存在或已下架");
                }
                examPaperName = examPaper.getName();
                correctSwitch = examPaper.getCorrectSwitch();
                ExamPaperQuestionQuery examPaperQuestionQuery = new ExamPaperQuestionQuery();
                examPaperQuestionQuery.setStatus(DataStatus.Y.getCode());
                examPaperQuestionQuery.setExamPaperId(request.getExamPaperId());
                List<ExamPaperQuestion> examPaperQuestionList = examPaperQuestionService.findAll(examPaperQuestionQuery);
                if (!isEmpty(examPaperQuestionList) && !examPaperQuestionList.isEmpty()){
                    //K: 试题ID       V: 试卷和试题的关联关系           搜集该Map后续会使用系统创建试卷时候设置的分值信息
                    questionIdMappingExamPaperQuestionMap = examPaperQuestionList.stream().filter(a -> !isEmpty(a.getQuestionId())).collect(Collectors.toMap(ExamPaperQuestion::getQuestionId, Function.identity(), (a,b) -> a));
                    if (!questionIdMappingExamPaperQuestionMap.isEmpty()){
                        QuestionQuery questionQuery = new QuestionQuery();
                        questionQuery.setStatus(DataStatus.Y.getCode());
                        questionQuery.setIds(new ArrayList<>(questionIdMappingExamPaperQuestionMap.keySet()));
                        questionList = questionService.findAll(questionQuery);
                    }
                }
            } else {
                UserExamPaper userExamPaper = userExamPaperService.findById(request.getExamPaperId());
                if (userExamPaper == null || !DataStatus.Y.getCode().equals(userExamPaper.getStatus())){
                    return new Response<>(ERROR, "试卷不存在或已下架");
                }
                UserExamPaperQuestionQuery userExamPaperQuestionQuery = new UserExamPaperQuestionQuery();
                userExamPaperQuestionQuery.setStatus(DataStatus.Y.getCode());
                userExamPaperQuestionQuery.setExamPaperId(request.getExamPaperId());
                List<UserExamPaperQuestion> userExamPaperQuestionList = userExamPaperQuestionService.findAll(userExamPaperQuestionQuery);
                if (!isEmpty(userExamPaperQuestionList) && !userExamPaperQuestionList.isEmpty()){
                    List<Integer> questionIdList = userExamPaperQuestionList.stream().filter(userExamPaperQuestion -> !isEmpty(userExamPaperQuestion.getQuestionId())).map(UserExamPaperQuestion::getQuestionId).collect(Collectors.toList());
                    if (!questionIdList.isEmpty()){
                        //查询试题
                        QuestionQuery questionQuery = new QuestionQuery();
                        questionQuery.setStatus(DataStatus.Y.getCode());
                        questionQuery.setIds(new ArrayList<>(questionIdList));
                        List<Question> questionParentList = questionService.findAll(questionQuery);
                        List<Integer> questionIdForC6 = new ArrayList<>();
                        if (!isEmpty(questionParentList) && !questionParentList.isEmpty()){
                            questionList.addAll(questionParentList);
                            //查询材料题下的小题信息
                            for (Question question : questionParentList) {
                                if (QuestionBaseType.C6.getCode().equals(question.getType())){
                                    questionIdForC6.add(question.getId());
                                }
                            }
                            if (!questionIdForC6.isEmpty()){
                                QuestionQuery questionQueryForChildren = new QuestionQuery();
                                questionQueryForChildren.setStatus(DataStatus.Y.getCode());
                                questionQueryForChildren.setParentIds(questionIdForC6);
                                List<Question> questionForC6ChildrenList = questionService.findAll(questionQueryForChildren);
                                if (!isEmpty(questionForC6ChildrenList) && !questionForC6ChildrenList.isEmpty()){
                                    questionList.addAll(questionForC6ChildrenList);
                                }
                            }
                        }
                    }
                }
            }

            //2.2 试卷中有效的试题详情
            if (isEmpty(questionList) || questionList.isEmpty()){
                return new Response<>(ERROR, "查询试题元数据信息失败!");
            }

            //2.2.2 封装每个试题分别是多少分 因为系统试卷和用户试卷分值取值地方不同,在生成做题报告的时候需要这些参数
            Map<Integer, BigDecimal> questionIdMappingScoreMap = new HashMap<>();
            for (Question question : questionList) {
                BigDecimal score = BigDecimal.ZERO;
                //系统试卷 尝试使用系统试卷创建时候的分值
                if (isSysExamPaper){
                    if (questionIdMappingExamPaperQuestionMap.containsKey(question.getId())){
                        ExamPaperQuestion examPaperQuestion = questionIdMappingExamPaperQuestionMap.get(question.getId());
                        if (!isEmpty(examPaperQuestion.getScore())){
                            score = examPaperQuestion.getScore();
                        }
                    }
                } else {
                    //用户组卷 尝试使用试题添加时候的默认分值
                    score = isEmpty(question.getScore()) ? BigDecimal.ZERO : question.getScore();
                }
                questionIdMappingScoreMap.put(question.getId(), score);
            }

            //3.1 创建或修改交卷记录
            UserExamPaperRecord userExamPaperRecordCreateOrModify = new UserExamPaperRecord();
            if (!isEmpty(request.getId())){
                UserExamPaperRecord userExamPaperRecord = userExamPaperRecordService.findById(request.getId());
                if (isEmpty(userExamPaperRecord) || !DataStatus.Y.getCode().equals(userExamPaperRecord.getStatus())){
                    return new Response<>(ERROR, "做题记录编号无效");
                }
                userExamPaperRecordCreateOrModify.setId(request.getId());
            } else {
                userExamPaperRecordCreateOrModify.setStatus(DataStatus.Y.getCode());
                userExamPaperRecordCreateOrModify.setCreateTime(serverTime);
            }
            //项目编号
            userExamPaperRecordCreateOrModify.setProjectId(request.getProjectId());
            //客户编号
            userExamPaperRecordCreateOrModify.setCustomerId(customerId);
            //userId编号
            userExamPaperRecordCreateOrModify.setUserId(getUserToken().getId());
            //考卷编号
            userExamPaperRecordCreateOrModify.setExamPaperId(request.getExamPaperId());
            //做题记录分类
            userExamPaperRecordCreateOrModify.setCatalog(request.getCatalog());
            //完成(交卷类型: 交卷/保存)
            userExamPaperRecordCreateOrModify.setOver(request.getOver());
            //剩余交卷时间
            userExamPaperRecordCreateOrModify.setSecond(request.getSecond());
            //用户做卷开始时间
            if (!isEmpty(request.getStartTime())){
                userExamPaperRecordCreateOrModify.setStartTime(DateUtil.parse(request.getStartTime(), DATETIME_FORMAT));
            }
            //用户做卷结束时间 只有提交了才有时间
            if (UserExamPaperRecordOver.Y.getCode().equals(request.getOver())){
                userExamPaperRecordCreateOrModify.setEndTime(serverTime);
            }
            //耗时
            userExamPaperRecordCreateOrModify.setDuration(request.getDuration());
            //上次练习位置
            userExamPaperRecordCreateOrModify.setLastExerciseQuestion(request.getLastExerciseQuestion());
            //是否批阅(默认未完成)
            userExamPaperRecordCreateOrModify.setReadOver(UserExamOverStatus.C1.getCode());
            //更新时间
            userExamPaperRecordCreateOrModify.setModifyTime(serverTime);

            //3.2 创建或修改交卷记录详情
            //3.2.1 查询是不是存在之前保存过的做题详情记录
            List<UserExamPaperRecordDetail> userExamPaperRecordDetailCreateOrModifyList = new ArrayList<>();
            Map<Integer, UserExamPaperRecordDetail> questionIdMappingUserExamPaperRecordDetailMap = new HashMap<>();
            if (!isEmpty(request.getId())){
                UserExamPaperRecordDetailQuery userExamPaperRecordDetailQuery = new UserExamPaperRecordDetailQuery();
                userExamPaperRecordDetailQuery.setStatus(DataStatus.Y.getCode());
                userExamPaperRecordDetailQuery.setUserExamPaperRecordId(request.getId());
                List<UserExamPaperRecordDetail> userExamPaperRecordDetailList = userExamPaperRecordDetailService.findAll(userExamPaperRecordDetailQuery);
                if (!isEmpty(userExamPaperRecordDetailList) && !userExamPaperRecordDetailList.isEmpty()){
                    questionIdMappingUserExamPaperRecordDetailMap = userExamPaperRecordDetailList.stream().filter(a -> !isEmpty(a.getQuestionId())).collect(Collectors.toMap(UserExamPaperRecordDetail::getQuestionId, Function.identity(), (a,b) -> a));
                }
            }
            /*
             * 总得分:     基础题型中答对的总得分
             * 试题数量:   大题试题的数量
             * 做题数量:   答案不为空的试题数量(只算大题)
             * 试卷正确率: 基础题型中做对的数量 / 基础题型数量   (不区分大小题)
             * 试卷得分率: 总得分 / 总分  (不区分大小题)
             */
            //试题数量                                  做题数量
            int questionNumber = 0,                     doQuestionNumber = 0;
            //基础题型中做对的数量            /            基础题型数量         =       试卷正确率
            int rightNumber = 0,                        canRightNumber = 0;
            //总得分                        /            试卷总分          =       试卷得分率;        2024-08-08修改为使用 总得分/试卷总分=得分率
            BigDecimal sumScore = BigDecimal.ZERO,      allScore = BigDecimal.ZERO;
            //用来判断试卷下是否包含不能自动判断对错的题目
            int notCanDetermineAnswerRightOrWrong = 0;

            //3.2.3 开始以试卷关联的所有试题进行搜集    这里试题列表包含大题和小题
            for (Question question : questionList) {
                UserExamPaperRecordDetail userExamPaperRecordDetailCreateOrModify = this.getUserExamPaperRecordDetailCreateOrModify(questionIdMappingUserExamPaperRecordDetailRequestMap.get(question.getId()), questionIdMappingUserExamPaperRecordDetailMap.get(question.getId()), question, getUserToken(), serverTime);
                //本试题设置分数
                BigDecimal questionScore = questionIdMappingScoreMap.getOrDefault(question.getId(), BigDecimal.ZERO);
                userExamPaperRecordDetailCreateOrModify.setQuestionScore(questionScore);

                //大题
                if (0 == question.getParentId()){
                    allScore = allScore.add(questionScore);
                    //试题数量
                    questionNumber ++;
                    if (!isEmpty(userExamPaperRecordDetailCreateOrModify.getAnswer())){
                        //做题数量
                        doQuestionNumber ++;
                    }
                }

                //所有可以判断对错的题目中
                if (QuestionArguments.CAN_DETERMINE_ANSWER_RIGHT_OR_WRONG.contains(question.getType())){
                    canRightNumber ++;
                    //默认做错
                    UserExamPaperRecordDetailQuestionResult result = UserExamPaperRecordDetailQuestionResult.N;
                    //用户提交了本题的做题详情
                    if (questionIdMappingUserExamPaperRecordDetailRequestMap.containsKey(question.getId())){
                        UserExamPaperRecordDetailRequest userExamPaperRecordDetailRequest = questionIdMappingUserExamPaperRecordDetailRequestMap.get(question.getId());
                        String userAnswer = userExamPaperRecordDetailRequest.getAnswer();
                        if (!isEmpty(userAnswer) && userAnswer.equals(question.getAnswer())){
                            rightNumber ++;
                            sumScore = sumScore.add(questionScore);
                            result = UserExamPaperRecordDetailQuestionResult.Y;
                            //得分
                            userExamPaperRecordDetailCreateOrModify.setScore(questionScore);
                        }
                    }
                    //对错
                    userExamPaperRecordDetailCreateOrModify.setQuestionResult(result.getCode());
                } else {
                    //所有不能直接判断对错的题目中,如果用户自评分不为空,需要计算到得分率中,不用计算正确率
                    if (!isEmpty(userExamPaperRecordDetailCreateOrModify.getScore())){
                        sumScore = sumScore.add(userExamPaperRecordDetailCreateOrModify.getScore());
                    }

                }
                userExamPaperRecordDetailCreateOrModifyList.add(userExamPaperRecordDetailCreateOrModify);
                if(!QuestionBaseType.C6.getCode().equals(question.getType()) && !QuestionArguments.CAN_DETERMINE_ANSWER_RIGHT_OR_WRONG.contains(question.getType())){
                    notCanDetermineAnswerRightOrWrong ++;
                }
            }

            //总得分
            userExamPaperRecordCreateOrModify.setSumScore(sumScore);
            //总分
            userExamPaperRecordCreateOrModify.setAllScore(allScore);
            //试题数量
            userExamPaperRecordCreateOrModify.setQuestionNumber(questionNumber);
            //做题数量
            userExamPaperRecordCreateOrModify.setPlayQuestionNumber(doQuestionNumber);
            //试卷正确率
            BigDecimal rightRate = rightNumber == 0 ? BigDecimal.ZERO : new BigDecimal(rightNumber).divide(new BigDecimal(canRightNumber), 2,  BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100));
            userExamPaperRecordCreateOrModify.setRightRate(rightRate);
            //试卷得分率
            BigDecimal scoreRate = sumScore.equals(BigDecimal.ZERO) ? BigDecimal.ZERO : sumScore.divide(allScore, 2, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100));
            userExamPaperRecordCreateOrModify.setScoreRate(scoreRate);
            //做题模式
            userExamPaperRecordCreateOrModify.setMode(request.getMode());

            //如果是模拟考试 并且是交卷操作 试卷下全是主观题（可以自动判断对错的题目） 或者试卷不需要批阅 批阅状态设置为已批阅
            if (UserExamPaperRecordOver.Y.getCode().equals(request.getOver()) &&
                    UserExamPaperRecordCatalog.C2.getCode().equals(request.getCatalog()) &&
                    (notCanDetermineAnswerRightOrWrong == 0 || ExamPaperCorrectSwitch.S1.getCode().equals(correctSwitch))) {
                userExamPaperRecordCreateOrModify.setReadOver(UserExamOverStatus.C0.getCode());
            }

            //默认是试卷类型
            String playType = !isEmpty(request.getPlayType()) ? request.getPlayType() : UserExamPaperRecordPlayType.C0.getCode();
            userExamPaperRecordCreateOrModify.setPlayType(playType);
            //5 持久化
            userExamPaperRecordService.createOrModify(userExamPaperRecordCreateOrModify, userExamPaperRecordDetailCreateOrModifyList);
            //6 发消息: 1.得分率 <= 30%;  2.无主观题;  3.模拟考试  4.交卷
            boolean sendMessage = scoreRate.compareTo(new BigDecimal(30)) <= 0 && notCanDetermineAnswerRightOrWrong == 0 && UserExamPaperRecordCatalog.C2.getCode().equals(request.getCatalog()) && UserExamPaperRecordOver.Y.getCode().equals(request.getOver());
            if (sendMessage){
                UserQuery userQuery = new UserQuery();
                userQuery.setStatus(DataStatus.Y.getCode());
                userQuery.setProjectId(request.getProjectId());
                userQuery.setCustomerId(customerId);
                List<User> userList = userService.findAll(userQuery);
                if (!isEmpty(userList) && !userList.isEmpty()){
                    User user = userList.get(0);
                    //发消息给 学管师
                    SysUserMasterQuery sysUserMasterQuery = new SysUserMasterQuery();
                    sysUserMasterQuery.setStatus(DataStatus.Y.getCode());
                    sysUserMasterQuery.setUserId(user.getId());
                    List<SysUserMaster> sysUserMasterList = sysUserMasterService.findAll(sysUserMasterQuery);
                    if (!isEmpty(sysUserMasterList) && !sysUserMasterList.isEmpty()){
                        SysUserMaster sysUserMaster = sysUserMasterList.get(0);
                        Integer sysUserId = sysUserMaster.getSysUserId();
                        SysUser sysUser = sysUserService.findById(sysUserId);
                        if (!isEmpty(sysUser) && DataStatus.Y.getCode().equals(sysUser.getStatus()) && !isEmpty(sysUser.getDingUserId())){
                            BatchSendOTORequest batchSendOTORequest = new BatchSendOTORequest();
                            batchSendOTORequest.setRobotCode(com.api.constant.App.DING_LXFK_MESSAGE_ROBOT_CODE);
                            batchSendOTORequest.setMsgKey(DingMessageKey.SAMPLE_TEXT.getCode());
                            batchSendOTORequest.setUserIds(Collections.singletonList(String.valueOf(sysUser.getDingUserId())));
                            DingMessageSampleTextParam sampleTextParam = new DingMessageSampleTextParam();
                            String content = "律学-【学员考试低分提醒】你名下的线索编号为：" + user.getId() + " 学员近期模拟考试：" + examPaperName + "（模拟考试试卷名称），分数" + userExamPaperRecordCreateOrModify.getSumScore().stripTrailingZeros().toPlainString() + "分，试卷总分：" + userExamPaperRecordCreateOrModify.getAllScore().stripTrailingZeros().toPlainString() + "分，得分率为" + userExamPaperRecordCreateOrModify.getScoreRate().stripTrailingZeros().toPlainString() + "%，请通知提醒学员努力学习，欲度关山，何惧狂澜；风生水起，正好扬帆。";
                            sampleTextParam.setContent(content);
                            batchSendOTORequest.setMsgParam(this.getJSON(sampleTextParam));
                            MQProducer.INSTANCE.sendOneway(LogSequence.get(), MQTopic.LXFK_API, MQTag.LXFK_API_DING_MESSAGE_MESSAGE, this.getJSON(batchSendOTORequest));
                        }
                    }
                    //发消息给 教辅
                    UserTeacherQuery userTeacherQuery = new UserTeacherQuery();
                    userTeacherQuery.setStatus(DataStatus.Y.getCode());
                    userTeacherQuery.setUserId(user.getId());
                    List<UserTeacher> userTeacherList = userTeacherService.findAll(userTeacherQuery);
                    if (!isEmpty(userTeacherList) && !userTeacherList.isEmpty()){
                        UserTeacher userTeacher = userTeacherList.get(0);
                        Integer sysUserId = userTeacher.getSysUserId();
                        SysUser sysUser = sysUserService.findById(sysUserId);
                        if (!isEmpty(sysUser) && DataStatus.Y.getCode().equals(sysUser.getStatus()) && !isEmpty(sysUser.getDingUserId())){
                            BatchSendOTORequest batchSendOTORequest = new BatchSendOTORequest();
                            batchSendOTORequest.setRobotCode(com.api.constant.App.DING_LXFK_MESSAGE_ROBOT_CODE);
                            batchSendOTORequest.setMsgKey(DingMessageKey.SAMPLE_TEXT.getCode());
                            batchSendOTORequest.setUserIds(Collections.singletonList(String.valueOf(sysUser.getDingUserId())));
                            DingMessageSampleTextParam sampleTextParam = new DingMessageSampleTextParam();
                            String content = "律学-【学员考试低分提醒】你名下的线索编号为：" + user.getId() + "学员近期模拟考试：" + examPaperName + "（模拟考试试卷名称），分数" + userExamPaperRecordCreateOrModify.getSumScore().stripTrailingZeros().toPlainString() + "分，试卷总分：" + userExamPaperRecordCreateOrModify.getAllScore().stripTrailingZeros().toPlainString() + "分，得分率为" + userExamPaperRecordCreateOrModify.getScoreRate().stripTrailingZeros().toPlainString() + "%，请通知提醒学员努力学习，夜色难免黑凉 前行必有曙光。";
                            sampleTextParam.setContent(content);
                            batchSendOTORequest.setMsgParam(this.getJSON(sampleTextParam));
                            MQProducer.INSTANCE.sendOneway(LogSequence.get(), MQTopic.LXFK_API, MQTag.LXFK_API_DING_MESSAGE_MESSAGE, this.getJSON(batchSendOTORequest));
                        }
                    }
                }
            }

            return new Response<>(OK,SUCCESS,userExamPaperRecordCreateOrModify.getId());
        }catch (Exception e){
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        } finally {
            lock.unlock();
        }
    }

    /**
     * 获取一个创建或更新的UserExamPaperRecordDetail实体
     * @param request 本次创建或更新的最新数据载体(可以是前端提交的参数)
     * @param userExamPaperRecordDetail 数据库中当前做卷记录中对当前试题的做题记录(如果之前没提交过,就不存在;如果试卷有多增加了几个题,也会不存在)
     * <AUTHOR>
     * @date 2023-03-29
     */
    private UserExamPaperRecordDetail getUserExamPaperRecordDetailCreateOrModify(
            UserExamPaperRecordDetailRequest request,
            UserExamPaperRecordDetail userExamPaperRecordDetail,
            Question question, UserToken userToken, Date serverTime){

        UserExamPaperRecordDetail result = new UserExamPaperRecordDetail();

        if (question != null){
            //考题编号
            result.setQuestionId(question.getId());
        }

        if (userToken != null){
            //客户编号
            result.setCustomerId(userToken.getCustomerId());
            //UserId编号
            result.setUserId(userToken.getId());
        }

        //用户输入相关
        if (request != null){
            //答案 可以为空
            result.setAnswer(request.getAnswer());
            //标记
            result.setMark(request.getMark());
            //用户上传图片
            result.setCustomerImgUrls(request.getCustomerImgUrls());
            //耗时
            result.setDuration(request.getDuration());
            //用户自评分
            result.setScore(request.getScore());
        }

        //更新/修改相关
        if (userExamPaperRecordDetail != null){
            //ID 可能为空 为空了就在service层进行create
            result.setId(userExamPaperRecordDetail.getId());
        } else {
            result.setStatus(DataStatus.Y.getCode());
            result.setCreateTime(serverTime);
        }

        //基础字段
        result.setModifyTime(serverTime);

        return result;
    }

    /**
     * 试卷批阅查询
     *
     * <AUTHOR>
     */
    @RequestMapping(value = "/v1/user/exam/paper/record/query")
    @Token
    public Response<NewPageResponse<UserExamPaperRecordResponse>> query(@RequestBody UserExamPaperRecordRequest request) {
        NewPageResponse<UserExamPaperRecordResponse> response = new NewPageResponse<>();
        try {
            response.setTotal(0);
            response.setItems(new ArrayList<>());
            //再查询这些卷子的做题记录已完成的
            UserExamPaperRecordQuery userExamPaperRecordQuery = new UserExamPaperRecordQuery();
            userExamPaperRecordQuery.setStatus(DataStatus.Y.getCode());
            userExamPaperRecordQuery.setOver(UserExamRecordOver.Y.getCode());
            userExamPaperRecordQuery.setCatalog(UserExamPaperRecordCatalog.C2.getCode());
            //是否批阅(0:是 1:否)
            userExamPaperRecordQuery.setReadOver(request.getReadOver());
            //项目编号
            userExamPaperRecordQuery.setProjectId(request.getProjectId());
            //考卷编号
            userExamPaperRecordQuery.setExamPaperId(request.getExamPaperId());
            //批阅人编号
            userExamPaperRecordQuery.setReadOverSysUserId(request.getReadOverSysUserId());

            userExamPaperRecordQuery.setCatalog(UserExamPaperRecordCatalog.C2.getCode());
            if(!isEmpty(request.getKeyword())){
                Customer customer = customerService.findByUsername(request.getKeyword());
                if(customer != null){
                    userExamPaperRecordQuery.setCustomerId(customer.getId());
                }else{
                    return new Response<>(response);
                }
            }
            Integer count = userExamPaperRecordService.count(userExamPaperRecordQuery);

            if (count == 0) {
                return new Response<>(response);
            }
            response.setTotal(count);
            //构建分页参数
            Pager pager = new Pager(count, request.getPage(), request.getLimit());
            userExamPaperRecordQuery.setStart(pager.getOffset());
            userExamPaperRecordQuery.setLimit(pager.getLimit());
            List<UserExamPaperRecord> userExamPaperRecordList = userExamPaperRecordService.find(userExamPaperRecordQuery);
            Set<Integer> customerIdSet = new HashSet<>();
            Set<Integer> projectIdSet = new HashSet<>();
            Set<Integer> examPaperIdSet = new HashSet<>();
            Set<Integer> sysUserIdSet = new HashSet<>();
            Set<Integer> userExamPaperRecordIdSet = new HashSet<>();
            for (UserExamPaperRecord userExamPaperRecord : userExamPaperRecordList) {
                if(!isEmpty(userExamPaperRecord.getCustomerId())){
                    customerIdSet.add(userExamPaperRecord.getCustomerId());
                }
                if(!isEmpty(userExamPaperRecord.getProjectId())){
                    projectIdSet.add(userExamPaperRecord.getProjectId());
                }
                if(!isEmpty(userExamPaperRecord.getExamPaperId())){
                    examPaperIdSet.add(userExamPaperRecord.getExamPaperId());
                }
                if(!isEmpty(userExamPaperRecord.getReadOverSysUserId())){
                    sysUserIdSet.add(userExamPaperRecord.getReadOverSysUserId());
                }
                userExamPaperRecordIdSet.add(userExamPaperRecord.getId());
            }

            Map<Integer, Customer> customerMap = new HashMap<>();
            Map<Integer, Project> projectMap = new HashMap<>();
            Map<Integer, ExamPaper> examPaperMap = new HashMap<>();
            Map<Integer, SysUser> sysUserMap = new HashMap<>();
            List<Integer> userExamPaperRecordIdList = new ArrayList<>();
            if (!customerIdSet.isEmpty()) {
                List<Customer> customerList = customerService.findByIds(new ArrayList<>(customerIdSet));
                for (Customer customer : customerList) {
                    customerMap.put(customer.getId(), customer);
                }
            }
            if (!projectIdSet.isEmpty()) {
                List<Project> projectList = projectService.findByIds(new ArrayList<>(projectIdSet));
                for (Project project : projectList) {
                    projectMap.put(project.getId(), project);
                }
            }
            //查询做题记录详情
            UserExamPaperRecordDetailQuery userExamPaperRecordDetailQuery = new UserExamPaperRecordDetailQuery();
            userExamPaperRecordDetailQuery.setUserExamPaperRecordIds(new ArrayList<>(userExamPaperRecordIdSet));
            userExamPaperRecordDetailQuery.setStatus(DataStatus.Y.getCode());
            List<UserExamPaperRecordDetail> recordDetails = userExamPaperRecordDetailService.findAll(userExamPaperRecordDetailQuery);

            Set<Integer> questionIdSet = new HashSet<>();
            Map<Integer, List<Integer>> recordIdQuestionIdsMap = new HashMap<>();
            for (UserExamPaperRecordDetail userExamPaperRecordDetail : recordDetails) {

                if(recordIdQuestionIdsMap.containsKey(userExamPaperRecordDetail.getUserExamPaperRecordId())){
                    recordIdQuestionIdsMap.get(userExamPaperRecordDetail.getUserExamPaperRecordId()).add(userExamPaperRecordDetail.getQuestionId());
                }else{
                    List<Integer> questionIdList = new ArrayList<>();
                    questionIdList.add(userExamPaperRecordDetail.getQuestionId());
                    recordIdQuestionIdsMap.put(userExamPaperRecordDetail.getUserExamPaperRecordId(),questionIdList);
                }
                questionIdSet.add(userExamPaperRecordDetail.getQuestionId());
            }
            //查询题目信息
            List<Question> questionList = questionService.findByIds(new ArrayList<>(questionIdSet));
            Map<Integer, String> questionTypeMap = questionList.stream().collect(Collectors.toMap(Question::getId, Question::getType));

            for (Integer recordId : recordIdQuestionIdsMap.keySet()) {
                List<Integer> questionIds = recordIdQuestionIdsMap.get(recordId);
                for (Integer questionId : questionIds) {
                    if(questionTypeMap.get(questionId) != null && !QuestionArguments.CAN_DETERMINE_ANSWER_RIGHT_OR_WRONG.contains(questionTypeMap.get(questionId))){
                        userExamPaperRecordIdList.add(recordId);
                        break;
                    }
                }
            }

            if (!examPaperIdSet.isEmpty()) {
                List<ExamPaper> examPaperList = examPaperService.findByIds(new ArrayList<>(examPaperIdSet));
                for (ExamPaper examPaper : examPaperList) {
                    examPaperMap.put(examPaper.getId(), examPaper);
                }
            }

            if (!sysUserIdSet.isEmpty()) {
                List<SysUser> sysUserList = sysUserService.findByIds(new ArrayList<>(sysUserIdSet));
                for (SysUser sysUser : sysUserList) {
                    sysUserMap.put(sysUser.getId(), sysUser);
                }
            }

            for (UserExamPaperRecord userExamPaperRecord : userExamPaperRecordList) {
                UserExamPaperRecordResponse userExamPaperRecordResponse = new UserExamPaperRecordResponse();
                userExamPaperRecordResponse.setId(userExamPaperRecord.getId());
                //用户名称、头像
                userExamPaperRecordResponse.setUserId(userExamPaperRecord.getUserId());
                if (customerMap.containsKey(userExamPaperRecord.getCustomerId())) {
                    userExamPaperRecordResponse.setUserName(customerMap.get(userExamPaperRecord.getCustomerId()).getName());
                    userExamPaperRecordResponse.setUserAvatar(customerMap.get(userExamPaperRecord.getCustomerId()).getAvatar());
                }
                //用户手机号
                if (customerMap.containsKey(userExamPaperRecord.getCustomerId())) {
                    userExamPaperRecordResponse.setUserMobile(this.getMobile(customerMap.get(userExamPaperRecord.getCustomerId()).getUsername()));
                }
                //提交时间
                userExamPaperRecordResponse.setCreateTime(DateUtil.format(userExamPaperRecord.getEndTime(), DATETIME_FORMAT));
                //项目名称
                userExamPaperRecordResponse.setProjectId(userExamPaperRecord.getProjectId());
                if (projectMap.containsKey(userExamPaperRecord.getProjectId())) {
                    userExamPaperRecordResponse.setProjectName(projectMap.get(userExamPaperRecord.getProjectId()).getName());
                }
                //考卷名称
                userExamPaperRecordResponse.setExamPaperId(userExamPaperRecord.getExamPaperId());
                if (examPaperMap.containsKey(userExamPaperRecord.getExamPaperId())) {
                    userExamPaperRecordResponse.setExamPaperName(examPaperMap.get(userExamPaperRecord.getExamPaperId()).getName());
                }
                //考卷分数
                userExamPaperRecordResponse.setSumScore(userExamPaperRecord.getSumScore());
                //考卷状态
                userExamPaperRecordResponse.setReadOver(userExamPaperRecord.getReadOver());
                userExamPaperRecordResponse.setReadOverName(UserExamRecordReadOver.getName(userExamPaperRecord.getReadOver()));
                //批阅人
                userExamPaperRecordResponse.setReadOverSysUserId(userExamPaperRecord.getReadOverSysUserId());
                if (sysUserMap.containsKey(userExamPaperRecord.getReadOverSysUserId())) {
                    userExamPaperRecordResponse.setReadOverSysUserName(sysUserMap.get(userExamPaperRecord.getReadOverSysUserId()).getName());
                }
                //批阅时间
                if (!this.isEmpty(userExamPaperRecord.getReadOverTime())) {
                    userExamPaperRecordResponse.setReadOverTime(DateUtil.format(userExamPaperRecord.getReadOverTime(), DATETIME_FORMAT));
                }
                if(userExamPaperRecordIdList.contains(userExamPaperRecord.getId())){
                    userExamPaperRecordResponse.setModifyStage(PublicStage.Y.getCode());
                }else{
                    userExamPaperRecordResponse.setModifyStage(PublicStage.N.getCode());
                }
                response.getItems().add(userExamPaperRecordResponse);
            }
            return new Response<>(response);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }

    /**
     * 试卷批阅统计查询
     *
     * <AUTHOR>
     */
    @RequestMapping(value = "/v1/user/exam/paper/record/report/id/query")
    @Token
    public Response<UserExamPaperRecordResponse> queryExamPaperRecordReportById(@RequestBody UserExamPaperRecordRequest request) {
        UserExamPaperRecordResponse userExamPaperRecordResponse = new UserExamPaperRecordResponse();
        try {
            if (this.isEmpty(request.getId())) {
                return new Response<>(ERROR, "数据编号为空，请检查");
            }
            UserExamPaperRecord userExamPaperRecord = userExamPaperRecordService.findById(request.getId());
            if (this.isEmpty(userExamPaperRecord)) {
                return new Response<>(ERROR, "数据无效，请检查");
            }

            userExamPaperRecordResponse.setId(userExamPaperRecord.getId());
            if(!isEmpty(userExamPaperRecord.getCatalog())){
                userExamPaperRecordResponse.setCatalogName(UserExamPaperRecordCatalog.getName(userExamPaperRecord.getCatalog()));
            }
            //用户名称
            userExamPaperRecordResponse.setUserId(userExamPaperRecord.getUserId());
            if (!this.isEmpty(userExamPaperRecord.getCustomerId())) {
                Customer customer = customerService.findById(userExamPaperRecord.getCustomerId());
                if (customer != null && !DataStatus.N.getCode().equals(customer.getStatus())) {
                    userExamPaperRecordResponse.setUserName(customer.getName());
                }
            }

            //提交时间
            userExamPaperRecordResponse.setCreateTime(DateUtil.format(userExamPaperRecord.getCreateTime(), DATETIME_FORMAT));

            //考卷分数
            userExamPaperRecordResponse.setSumScore(userExamPaperRecord.getSumScore());

            List<UserExamPaperRecordDetailResponse> userExamPaperRecordDetailResponses = new ArrayList<>();

            //查询当前记录的的试题
            //查询当前记录下的题目编号
            UserExamPaperRecordDetailQuery userExamPaperRecordDetailQuery = new UserExamPaperRecordDetailQuery();
            userExamPaperRecordDetailQuery.setStatus(DataStatus.Y.getCode());
            userExamPaperRecordDetailQuery.setUserExamPaperRecordId(request.getId());
            List<UserExamPaperRecordDetail> userExamRecordDetailList = userExamPaperRecordDetailService.findAll(userExamPaperRecordDetailQuery);

            Map<Integer, ExamPaperQuestion> questionIdExamQuestionMap = new HashMap<>();
            //查询题干模板
            Map<Integer, QuestionTemplateItem> questionTemplateItemMap = new HashMap<>();
            if (!this.isEmpty(userExamRecordDetailList) && !userExamRecordDetailList.isEmpty()) {
                //题目ids
                List<Integer> questionIdList = userExamRecordDetailList.stream().filter(u -> !this.isEmpty(u.getQuestionId())).map(UserExamPaperRecordDetail::getQuestionId).collect(Collectors.toList());
                Map<Integer, Question> questionMap = new HashMap<>();
                Set<Integer> questionTemplateItemIdSet = new HashSet<>();
                if (!this.isEmpty(questionIdList) && !questionIdList.isEmpty()) {
                    QuestionQuery questionQuery = new QuestionQuery();
                    questionQuery.setIds(questionIdList);
                    List<Question> questionList = questionService.findAll(questionQuery);
                    for (Question question : questionList) {
                        questionMap.put(question.getId(), question);
                        questionTemplateItemIdSet.add(question.getQuestionTemplateItemId());
                    }
                }

                if (!isEmpty(questionTemplateItemIdSet) && !questionTemplateItemIdSet.isEmpty()) {
                    QuestionTemplateItemQuery questionTemplateItemQuery = new QuestionTemplateItemQuery();
                    questionTemplateItemQuery.setStatus(DataStatus.Y.getCode());
                    questionTemplateItemQuery.setIds(new ArrayList<>(questionTemplateItemIdSet));
                    List<QuestionTemplateItem> questionTemplateItemList = questionTemplateItemService.findAll(questionTemplateItemQuery);
                    questionTemplateItemMap = questionTemplateItemList.stream().collect(Collectors.toMap(QuestionTemplateItem::getId, Function.identity()));
                }
                //查询卷子绑定题目分数
                if (!this.isEmpty(userExamPaperRecord.getExamPaperId())) {
                    ExamPaperQuestionQuery examPaperQuestionQuery = new ExamPaperQuestionQuery();
                    examPaperQuestionQuery.setExamPaperId(userExamPaperRecord.getExamPaperId());
                    examPaperQuestionQuery.setStatus(DataStatus.Y.getCode());
                    List<ExamPaperQuestion> examPaperQuestionList = examPaperQuestionService.findAll(examPaperQuestionQuery);
                    for (ExamPaperQuestion examPaperQuestion : examPaperQuestionList) {
                        if (!this.isEmpty(examPaperQuestion.getQuestionId())) {
                            questionIdExamQuestionMap.put(examPaperQuestion.getQuestionId(), examPaperQuestion);
                        }
                    }
                }
                //封装做题记录详情树结构
                List<UserExamPaperRecordDetailResponse> userExamPaperRecordDetailResponseList = allBeanToResponse(userExamRecordDetailList,questionMap,questionIdExamQuestionMap,questionTemplateItemMap);
                List<UserExamPaperRecordDetailResponse> userExamPaperRecordDetailTree = buildUserExamPaperRecordDetailTree(userExamPaperRecordDetailResponseList, 0);

                //按题目类型模板分组
                Map<Integer, List<UserExamPaperRecordDetailResponse>> groupUserExamRecordDetailResponseMap = userExamPaperRecordDetailTree.stream().collect(Collectors.groupingBy(UserExamPaperRecordDetailResponse::getQuestionTemplateItemId, LinkedHashMap::new, Collectors.toList()));


                for (Integer questionTemplateItemId : groupUserExamRecordDetailResponseMap.keySet()) {
                    if(questionTemplateItemMap.containsKey(questionTemplateItemId)){

                        QuestionTemplateItem questionTemplateItem = questionTemplateItemMap.get(questionTemplateItemId);
                        List<UserExamPaperRecordDetailResponse> userExamRecordDetailResponseList = groupUserExamRecordDetailResponseMap.get(questionTemplateItemId);
                        //如果是材料题
                        if(QuestionBaseType.C6.getCode().equals(questionTemplateItem.getQuestionType())){
                            UserExamPaperRecordDetailResponse userExamPaperRecordDetailResponse = new UserExamPaperRecordDetailResponse();
                            //code
                            userExamPaperRecordDetailResponse.setQuestionType(questionTemplateItem.getQuestionType());
                            //名称
                            userExamPaperRecordDetailResponse.setQuestionTypeName(questionTemplateItem.getName());
                            userExamPaperRecordDetailResponse.setQuestionTemplateItemId(questionTemplateItem.getId());
                            //总分
                            BigDecimal questionGroupSumScoreParent = BigDecimal.ZERO;
                            //正确题数
                            Integer questionGroupRightNumParent = 0;
                            //错误题数
                            Integer questionGroupErrorNumParent = 0;
                            List<UserExamPaperRecordDetailResponse> userExamPaperRecordDetailChildrenList = new ArrayList<>();
                            //取出材料题的子题
                            for (UserExamPaperRecordDetailResponse userExamPaperRecordDetailChildren : userExamRecordDetailResponseList) {
                                if(!isEmpty(userExamPaperRecordDetailChildren.getChildren())){
                                    userExamPaperRecordDetailChildrenList.addAll(userExamPaperRecordDetailChildren.getChildren());
                                }
                            }
                            List<UserExamPaperRecordDetailResponse> userExamPaperRecordDetailChildrenResponses = new ArrayList<>();
                            LinkedHashMap<String, List<UserExamPaperRecordDetailResponse>> groupUserExamRecordDetailChildrenMap = userExamPaperRecordDetailChildrenList.stream().collect(Collectors.groupingBy(UserExamPaperRecordDetailResponse::getQuestionType, LinkedHashMap::new, Collectors.toList()));

                            for (String questionType : groupUserExamRecordDetailChildrenMap.keySet()) {
                                List<UserExamPaperRecordDetailResponse> userExamRecordDetailChildrenResponseList = groupUserExamRecordDetailChildrenMap.get(questionType);

                                UserExamPaperRecordDetailResponse userExamPaperRecordDetailChildrenResponse = new UserExamPaperRecordDetailResponse();
                                //code
                                userExamPaperRecordDetailChildrenResponse.setQuestionType(questionType);
                                //名称
                                userExamPaperRecordDetailChildrenResponse.setQuestionTypeName(QuestionBaseType.getName(questionType));
                                //总分
                                BigDecimal questionGroupSumScore = BigDecimal.ZERO;
                                //正确题数
                                Integer questionGroupRightNum = 0;
                                //错误题数
                                Integer questionGroupErrorNum = 0;

                                for (UserExamPaperRecordDetailResponse recordDetailResponse : userExamRecordDetailChildrenResponseList) {
                                    if (!this.isEmpty(recordDetailResponse.getScore())) {
                                        questionGroupSumScore = questionGroupSumScore.add(recordDetailResponse.getScore());
                                        questionGroupSumScoreParent = questionGroupSumScoreParent.add(recordDetailResponse.getScore());
                                    }
                                    if (!this.isEmpty(recordDetailResponse.getQuestionResult())) {
                                        if (UserExamPaperRecordDetailQuestionResult.Y.getCode().equals(recordDetailResponse.getQuestionResult())) {
                                            questionGroupRightNum++;
                                            questionGroupRightNumParent = questionGroupRightNumParent + 1;
                                        } else if (UserExamPaperRecordDetailQuestionResult.N.getCode().equals(recordDetailResponse.getQuestionResult())) {
                                            questionGroupErrorNum++;
                                            questionGroupErrorNumParent = questionGroupErrorNumParent + 1;
                                        }
                                    }
                                }
                                userExamPaperRecordDetailChildrenResponse.setQuestionGroupSumScore(questionGroupSumScore);
                                userExamPaperRecordDetailChildrenResponse.setQuestionGroupRightNum(questionGroupRightNum);
                                userExamPaperRecordDetailChildrenResponse.setQuestionGroupErrorNum(questionGroupErrorNum);

                                userExamPaperRecordDetailChildrenResponses.add(userExamPaperRecordDetailChildrenResponse);
                            }
                            userExamPaperRecordDetailResponse.setChildren(userExamPaperRecordDetailChildrenResponses);

                            userExamPaperRecordDetailResponse.setQuestionGroupSumScore(questionGroupSumScoreParent);
                            userExamPaperRecordDetailResponse.setQuestionGroupRightNum(questionGroupRightNumParent);
                            userExamPaperRecordDetailResponse.setQuestionGroupErrorNum(questionGroupErrorNumParent);
                            userExamPaperRecordDetailResponses.add(userExamPaperRecordDetailResponse);
                        }else{
                            userExamPaperRecordDetailResponses.add(createUserExamPaperRecordDetailResponse(questionTemplateItem,userExamRecordDetailResponseList));
                        }
                    }
                }
            }
            userExamPaperRecordResponse.setUserExamPaperRecordDetailResponses(userExamPaperRecordDetailResponses);
            return new Response<>(userExamPaperRecordResponse);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }

    public  UserExamPaperRecordDetailResponse createUserExamPaperRecordDetailResponse( QuestionTemplateItem questionTemplateItem,List<UserExamPaperRecordDetailResponse> userExamRecordDetailResponseList){
        UserExamPaperRecordDetailResponse userExamPaperRecordDetailResponse = new UserExamPaperRecordDetailResponse();
        userExamPaperRecordDetailResponse.setQuestionTemplateItemId(questionTemplateItem.getId());
        //code
        userExamPaperRecordDetailResponse.setQuestionType(questionTemplateItem.getQuestionType());
        //名称
        userExamPaperRecordDetailResponse.setQuestionTypeName(questionTemplateItem.getName());
        //总分
        BigDecimal questionGroupSumScore = BigDecimal.ZERO;
        //正确题数
        Integer questionGroupRightNum = 0;
        //错误题数
        Integer questionGroupErrorNum = 0;

        for (UserExamPaperRecordDetailResponse recordDetailResponse : userExamRecordDetailResponseList) {
            if (!this.isEmpty(recordDetailResponse.getScore())) {
                questionGroupSumScore = questionGroupSumScore.add(recordDetailResponse.getScore());
            }
            if (!this.isEmpty(recordDetailResponse.getQuestionResult())) {
                if (UserExamPaperRecordDetailQuestionResult.Y.getCode().equals(recordDetailResponse.getQuestionResult())) {
                    questionGroupRightNum++;
                } else if (UserExamPaperRecordDetailQuestionResult.N.getCode().equals(recordDetailResponse.getQuestionResult())) {
                    questionGroupErrorNum++;
                }
            }
        }
        userExamPaperRecordDetailResponse.setQuestionGroupSumScore(questionGroupSumScore);
        userExamPaperRecordDetailResponse.setQuestionGroupRightNum(questionGroupRightNum);
        userExamPaperRecordDetailResponse.setQuestionGroupErrorNum(questionGroupErrorNum);
        return userExamPaperRecordDetailResponse;
    }
    private List<UserExamPaperRecordDetailResponse> allBeanToResponse(List<UserExamPaperRecordDetail> userExamPaperRecordDetailList,Map<Integer, Question> questionMap,Map<Integer, ExamPaperQuestion> questionIdExamQuestionMap, Map<Integer, QuestionTemplateItem> questionTemplateItemMap){
        List<UserExamPaperRecordDetailResponse> responses = new ArrayList<>();
        if(!userExamPaperRecordDetailList.isEmpty()){
            for (UserExamPaperRecordDetail userExamPaperRecordDetail : userExamPaperRecordDetailList) {
                UserExamPaperRecordDetailResponse response = new UserExamPaperRecordDetailResponse();
                response.setId(userExamPaperRecordDetail.getId());
                response.setAnswer(userExamPaperRecordDetail.getAnswer());
                response.setScore(userExamPaperRecordDetail.getScore());
                response.setQuestionId(userExamPaperRecordDetail.getQuestionId());
                if(questionMap.containsKey(userExamPaperRecordDetail.getQuestionId())){
                    Question question = questionMap.get(userExamPaperRecordDetail.getQuestionId());
                    response.setParentQuestionId(question.getParentId());
                    //题干信息
                    QuestionResponse questionResponse = new QuestionResponse();
                    //题目标题 简答题区分材料
                    questionResponse.setQuestion(question.getQuestion());
                    //问题答案
                    questionResponse.setAnswer(question.getAnswer());
                    //问题分数
                    if (questionIdExamQuestionMap.containsKey(question.getId())) {
                        questionResponse.setQuestionScore(questionIdExamQuestionMap.get(question.getId()).getScore());
                    }else {
                        questionResponse.setQuestionScore(BigDecimal.ZERO);
                    }
                    //问题类型
                    questionResponse.setType(question.getType());
                    questionResponse.setTypeName(QuestionBaseType.getName(question.getType()));
                    response.setQuestionType(question.getType());
                    //题目类型编号
                    questionResponse.setQuestionTemplateItemId(question.getQuestionTemplateItemId());
                    if (questionTemplateItemMap.containsKey(question.getQuestionTemplateItemId())){
                        response.setQuestionTemplateItemId(question.getQuestionTemplateItemId());
                        questionResponse.setQuestionTemplateItemName(questionTemplateItemMap.get(question.getQuestionTemplateItemId()).getName());
                        response.setQuestionTypeName(questionTemplateItemMap.get(question.getQuestionTemplateItemId()).getName());
                    }else{
                        questionResponse.setQuestionTemplateItemName(QuestionBaseType.getName(question.getType()));
                        response.setQuestionTypeName(QuestionBaseType.getName(question.getType()));

                    }
                    //response.setQuestionResponse(questionResponse);
                }
                if(!isEmpty(userExamPaperRecordDetail.getCustomerImgUrls())){
                    response.setCustomerImgUrls(Arrays.asList(userExamPaperRecordDetail.getCustomerImgUrls().split(App.COMMA)));
                }
                if(!isEmpty(userExamPaperRecordDetail.getCommentImgUrls())){
                    response.setCommentImgUrls(Arrays.asList(userExamPaperRecordDetail.getCommentImgUrls().split(App.COMMA)));
                }
                response.setUserExamPaperRecordId(userExamPaperRecordDetail.getUserExamPaperRecordId());
                response.setMark(userExamPaperRecordDetail.getMark());
                response.setQuestionResult(userExamPaperRecordDetail.getQuestionResult());
                response.setQuestionResultName(UserExamPaperRecordDetailQuestionResult.getName(userExamPaperRecordDetail.getQuestionResult()));
                response.setStatus(userExamPaperRecordDetail.getStatus());
                response.setTeacherComment(userExamPaperRecordDetail.getTeacherComment());
                responses.add(response);
            }
        }
        return responses;
    }
    /**
     * 构建树结构
     */
    private List<UserExamPaperRecordDetailResponse> buildUserExamPaperRecordDetailTree(List<UserExamPaperRecordDetailResponse> list, Integer pid) {
        List<UserExamPaperRecordDetailResponse> tree = new ArrayList<>();
        for (UserExamPaperRecordDetailResponse node : list) {
            if (pid.equals(node.getParentQuestionId())) {
                tree.add(findChild(node, list));
            }
        }
        tree.sort(Comparator.comparing(UserExamPaperRecordDetailResponse::getQuestionType));
        return tree;
    }

    /**
     * 递归构建子级树结构
     */
    private UserExamPaperRecordDetailResponse findChild(UserExamPaperRecordDetailResponse node, List<UserExamPaperRecordDetailResponse> list) {
        node.setChildren(new ArrayList<>());

        for (UserExamPaperRecordDetailResponse item : list) {
            if (node.getQuestionId().equals(item.getParentQuestionId())) {
                node.getChildren().add(findChild(item, list));
            }
        }
        if (node.getChildren() != null && !node.getChildren().isEmpty()) {
            node.getChildren().sort(Comparator.comparing(UserExamPaperRecordDetailResponse::getQuestionType));
        }
        return node;
    }
    /**
     * 试卷批阅编辑
     *
     * <AUTHOR>
     */
    @RequestMapping(value = "/v1/user/exam/paper/record/modify")
    @Token
    public Response<?> modifyReadOver(@RequestBody UserExamPaperRecordRequest request) {
        Date serverTime = DateUtil.getServerTime();
        UserToken userToken = this.getUserToken();
        try {
            //校验参数
            UserExamPaperRecordValidator validator = new UserExamPaperRecordValidator();
            if (!validator
                    .onId(request.getId())
                    .onSumScore(request.getSumScore())
                    .onUserExamRecordDetailRequests(request.getUserExamPaperRecordDetailRequests())
                    .result()) {
                return new Response<>(ERROR, validator.getErrorMessage());
            }

            UserExamPaperRecordDetailValidator userExamPaperRecordDetailValidator = new UserExamPaperRecordDetailValidator();
            for (UserExamPaperRecordDetailRequest userExamPaperRecordDetailRequest : request.getUserExamPaperRecordDetailRequests()) {
                if (!userExamPaperRecordDetailValidator
                        .onId(userExamPaperRecordDetailRequest.getId())
                        .onScore(userExamPaperRecordDetailRequest.getScore())
                        .result()) {
                    return new Response<>(ERROR, userExamPaperRecordDetailValidator.getErrorMessage());
                }
            }
            UserExamPaperRecord userExamPaperRecord = userExamPaperRecordService.findById(request.getId());
            if(userExamPaperRecord == null || DataStatus.N.getCode().equals(userExamPaperRecord.getStatus())){
                return new Response<>(ERROR, "未查询到该做题记录");
            }
            //构建做题记录编辑对象
            userExamPaperRecord.setSumScore(request.getSumScore());
            userExamPaperRecord.setReadOver(UserExamOverStatus.C0.getCode());
            userExamPaperRecord.setReadOverSysUserId(userToken.getId());
            userExamPaperRecord.setReadOverTime(serverTime);
            userExamPaperRecord.setModifyTime(serverTime);
            //封装做题记录详情信息
            Map<Integer, UserExamPaperRecordDetail> userExamPaperRecordDetailMap = new HashMap<>();
            List<UserExamPaperRecordDetailRequest> userExamPaperRecordDetailRequests = request.getUserExamPaperRecordDetailRequests();

            //查询做题详情
            UserExamPaperRecordDetailQuery userExamPaperRecordDetailQuery = new UserExamPaperRecordDetailQuery();
            userExamPaperRecordDetailQuery.setUserExamPaperRecordId(userExamPaperRecord.getId());
            userExamPaperRecordDetailQuery.setStatus(DataStatus.Y.getCode());
            List<UserExamPaperRecordDetail> userExamPaperRecordDetails = userExamPaperRecordDetailService.findAll(userExamPaperRecordDetailQuery);
            userExamPaperRecordDetailMap = userExamPaperRecordDetails.stream().collect(Collectors.toMap(UserExamPaperRecordDetail::getId,Function.identity()));


            List<UserExamPaperRecordDetail> userExamPaperRecordDetailList = new ArrayList<>();
            for (UserExamPaperRecordDetailRequest userExamPaperRecordDetailRequest : userExamPaperRecordDetailRequests) {
                UserExamPaperRecordDetail userExamPaperRecordDetail = new UserExamPaperRecordDetail();
                userExamPaperRecordDetail.setId(userExamPaperRecordDetailRequest.getId());
                userExamPaperRecordDetail.setScore(userExamPaperRecordDetailRequest.getScore());
                if(userExamPaperRecordDetailMap.containsKey(userExamPaperRecordDetailRequest.getId()) && !isEmpty(userExamPaperRecordDetailMap.get(userExamPaperRecordDetailRequest.getId()).getQuestionScore())){
                    userExamPaperRecordDetail.setQuestionScore(userExamPaperRecordDetailMap.get(userExamPaperRecordDetailRequest.getId()).getQuestionScore());
                }
                userExamPaperRecordDetail.setTeacherComment(userExamPaperRecordDetailRequest.getTeacherComment());
                userExamPaperRecordDetail.setCommentImgUrls(userExamPaperRecordDetailRequest.getCommentImgUrls());
                userExamPaperRecordDetail.setQuestionResult(userExamPaperRecordDetailRequest.getQuestionResult());
                userExamPaperRecordDetail.setModifyTime(serverTime);
                userExamPaperRecordDetailList.add(userExamPaperRecordDetail);
            }

            userExamPaperRecordService.modify(userExamPaperRecord, userExamPaperRecordDetailList,userExamPaperRecordDetailMap);

            //批卷后发消息(能走到批阅这个流程的交卷都不包含纯"客观题"了,如果纯"客观题"试卷就不需要批阅了,而且这种试卷在交卷的时候就已经发过消息了): scoreRate在service层中进行了更新
            if (userExamPaperRecord.getScoreRate().compareTo(new BigDecimal(30)) <= 0 && !isEmpty(userExamPaperRecord.getCustomerId()) && !isEmpty(userExamPaperRecord.getProjectId())) {
                if (!isEmpty(userExamPaperRecord.getExamPaperId())){
                    ExamPaper examPaper = examPaperService.findById(userExamPaperRecord.getExamPaperId());
                    if (!isEmpty(examPaper) && DataStatus.Y.getCode().equals(examPaper.getStatus()) && !isEmpty(examPaper.getName()) && !isEmpty(userExamPaperRecord.getSumScore()) && !isEmpty(userExamPaperRecord.getAllScore()) && !isEmpty(userExamPaperRecord.getScoreRate())){
                        UserQuery userQuery = new UserQuery();
                        userQuery.setStatus(DataStatus.Y.getCode());
                        userQuery.setProjectId(userExamPaperRecord.getProjectId());
                        userQuery.setCustomerId(userExamPaperRecord.getCustomerId());
                        List<User> userList = userService.findAll(userQuery);
                        if (!isEmpty(userList) && !userList.isEmpty()) {
                            User user = userList.get(0);
                            //发消息给 学管师
                            SysUserMasterQuery sysUserMasterQuery = new SysUserMasterQuery();
                            sysUserMasterQuery.setStatus(DataStatus.Y.getCode());
                            sysUserMasterQuery.setUserId(user.getId());
                            List<SysUserMaster> sysUserMasterList = sysUserMasterService.findAll(sysUserMasterQuery);
                            if (!isEmpty(sysUserMasterList) && !sysUserMasterList.isEmpty()) {
                                SysUserMaster sysUserMaster = sysUserMasterList.get(0);
                                Integer sysUserId = sysUserMaster.getSysUserId();
                                SysUser sysUser = sysUserService.findById(sysUserId);
                                if (!isEmpty(sysUser) && DataStatus.Y.getCode().equals(sysUser.getStatus()) && !isEmpty(sysUser.getDingUserId())) {
                                    BatchSendOTORequest batchSendOTORequest = new BatchSendOTORequest();
                                    batchSendOTORequest.setRobotCode(com.api.constant.App.DING_LXFK_MESSAGE_ROBOT_CODE);
                                    batchSendOTORequest.setMsgKey(DingMessageKey.SAMPLE_TEXT.getCode());
                                    batchSendOTORequest.setUserIds(Collections.singletonList(String.valueOf(sysUser.getDingUserId())));
                                    DingMessageSampleTextParam sampleTextParam = new DingMessageSampleTextParam();
                                    String content = "律学-【学员考试低分提醒】你名下的线索编号为：" + user.getId() + " 学员近期模拟考试：" + examPaper.getName() + "（模拟考试试卷名称），分数" + userExamPaperRecord.getSumScore().stripTrailingZeros().toPlainString() + "分，试卷总分：" + userExamPaperRecord.getAllScore().stripTrailingZeros().toPlainString() + "分，得分率为" + userExamPaperRecord.getScoreRate().stripTrailingZeros().toPlainString() + "%，请通知提醒学员努力学习，欲度关山，何惧狂澜；风生水起，正好扬帆。";
                                    sampleTextParam.setContent(content);
                                    batchSendOTORequest.setMsgParam(this.getJSON(sampleTextParam));
                                    MQProducer.INSTANCE.sendOneway(LogSequence.get(), MQTopic.LXFK_API, MQTag.LXFK_API_DING_MESSAGE_MESSAGE, this.getJSON(batchSendOTORequest));
                                }
                            }
                            //发消息给 教辅
                            UserTeacherQuery userTeacherQuery = new UserTeacherQuery();
                            userTeacherQuery.setStatus(DataStatus.Y.getCode());
                            userTeacherQuery.setUserId(user.getId());
                            List<UserTeacher> userTeacherList = userTeacherService.findAll(userTeacherQuery);
                            if (!isEmpty(userTeacherList) && !userTeacherList.isEmpty()) {
                                UserTeacher userTeacher = userTeacherList.get(0);
                                Integer sysUserId = userTeacher.getSysUserId();
                                SysUser sysUser = sysUserService.findById(sysUserId);
                                if (!isEmpty(sysUser) && DataStatus.Y.getCode().equals(sysUser.getStatus()) && !isEmpty(sysUser.getDingUserId())) {
                                    BatchSendOTORequest batchSendOTORequest = new BatchSendOTORequest();
                                    batchSendOTORequest.setRobotCode(com.api.constant.App.DING_LXFK_MESSAGE_ROBOT_CODE);
                                    batchSendOTORequest.setMsgKey(DingMessageKey.SAMPLE_TEXT.getCode());
                                    batchSendOTORequest.setUserIds(Collections.singletonList(String.valueOf(sysUser.getDingUserId())));
                                    DingMessageSampleTextParam sampleTextParam = new DingMessageSampleTextParam();
                                    String content = "律学-【学员考试低分提醒】你名下的线索编号为：" + user.getId() + "学员近期模拟考试：" + examPaper.getName() + "（模拟考试试卷名称），分数" + userExamPaperRecord.getSumScore().stripTrailingZeros().toPlainString() + "分，试卷总分：" + userExamPaperRecord.getAllScore().stripTrailingZeros().toPlainString() + "分，得分率为" + userExamPaperRecord.getScoreRate().stripTrailingZeros().toPlainString() + "%，请通知提醒学员努力学习，夜色难免黑凉 前行必有曙光。";
                                    sampleTextParam.setContent(content);
                                    batchSendOTORequest.setMsgParam(this.getJSON(sampleTextParam));
                                    MQProducer.INSTANCE.sendOneway(LogSequence.get(), MQTopic.LXFK_API, MQTag.LXFK_API_DING_MESSAGE_MESSAGE, this.getJSON(batchSendOTORequest));
                                }
                            }
                        }
                    }
                }
            }
            return new Response<>(OK, SUCCESS);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }

    /**
     * 我的题库(错题记录,收藏记录,做题笔记)——用户端
     *
     * <AUTHOR>
     */
    @RequestMapping(value = "/v1/user/exam/paper/record/my/query")
    @Token
    public Response<MyQuestionRecordResponse> userExamPaperRecord(@RequestBody MyQuestionRecordRequest request) {
        UserToken userToken = this.getUserToken();
        UserExamPaperRecordValidator validator = new UserExamPaperRecordValidator();
        try {
            if (!validator.onProjectId(request.getProjectId()).onQuestionBankType(request.getQuestionBankType()).result()) {
                return new Response<>(ERROR, validator.getErrorMessage());
            }

            MyQuestionRecordResponse myQuestionRecordResponse = new MyQuestionRecordResponse();
            myQuestionRecordResponse.setProjectId(request.getProjectId());
            myQuestionRecordResponse.setMyQuestionItemRecordResponseList(new ArrayList<>());
            myQuestionRecordResponse.setMyQuestionRecordDateResponseList(new ArrayList<>());
            //我的题库 做题类型(1:错题档案 2: 收藏题目 3:做题笔记 )
            QuestionBankType bankType = QuestionBankType.get(request.getQuestionBankType());
            switch (Objects.requireNonNull(bankType)) {
                case C1:
                    //查询用户的错题记录
                    UserExamPaperRecordQuery userExamPaperRecordQuery = new UserExamPaperRecordQuery();
                    userExamPaperRecordQuery.setProjectId(request.getProjectId());
                    userExamPaperRecordQuery.setCustomerId(userToken.getCustomerId());
                    userExamPaperRecordQuery.setOver(UserExamRecordOver.Y.getCode());
                    userExamPaperRecordQuery.setStatus(DataStatus.Y.getCode());
                    List<UserExamPaperRecord> userExamPaperRecordList = userExamPaperRecordService.findAll(userExamPaperRecordQuery);

                    if (!isEmpty(userExamPaperRecordList) && !userExamPaperRecordList.isEmpty()) {
                        //封装错题记录
                        queryMyErrorQuestionRecord(request.getProjectId(),myQuestionRecordResponse, request.getShowType(), userExamPaperRecordList);
                    }
                    break;
                case C2:
                    //1.查询用户收藏的题目
                    UserExamPaperQuestionFavoriteQuery userExamPaperQuestionFavoriteQuery = new UserExamPaperQuestionFavoriteQuery();
                    userExamPaperQuestionFavoriteQuery.setProjectId(request.getProjectId());
                    userExamPaperQuestionFavoriteQuery.setCustomerId(userToken.getCustomerId());
                    userExamPaperQuestionFavoriteQuery.setStatus(DataStatus.Y.getCode());
                    List<UserExamPaperQuestionFavorite> userExamPaperQuestionFavoriteList = userExamPaperQuestionFavoriteService.findAll(userExamPaperQuestionFavoriteQuery);
                    if (!isEmpty(userExamPaperQuestionFavoriteList) && !userExamPaperQuestionFavoriteList.isEmpty()) {
                        //收藏题目的ids
                        List<Integer> questionIds = userExamPaperQuestionFavoriteList.stream().map(UserExamPaperQuestionFavorite::getQuestionId).collect(Collectors.toList());
                        //查询题目信息
                        List<Question> questionList = questionService.findByIds(new ArrayList<>(questionIds));
                        if(isEmpty(questionList) || questionList.isEmpty()){
                            return new Response<>(myQuestionRecordResponse);
                        }
                        //按知识点展示
                        if (MyQuestionRecordShowType.M1.getCode() == request.getShowType()) {
                            queryUserExamPaperRecordByProjectItem(request.getProjectId(),myQuestionRecordResponse, questionList);
                        }
                        //按时间展示
                        if (MyQuestionRecordShowType.M2.getCode() == request.getShowType()) {
                            List<UserExamPaperQuestionFavorite> userExamPaperQuestionFavorites = userExamPaperQuestionFavoriteList.stream().sorted(Comparator.comparing(UserExamPaperQuestionFavorite::getModifyTime).reversed()).collect(Collectors.toList());
                            Map<String, List<UserExamPaperQuestionFavorite>> timeUserExamPaperQuestionFavoritesMap = new LinkedHashMap<>();
                            for (UserExamPaperQuestionFavorite userExamPaperQuestionFavorite : userExamPaperQuestionFavorites) {
                                String date = DateUtil.format(userExamPaperQuestionFavorite.getModifyTime(), DATE_FORMAT);
                                if (timeUserExamPaperQuestionFavoritesMap.containsKey(date)) {
                                    timeUserExamPaperQuestionFavoritesMap.get(date).add(userExamPaperQuestionFavorite);
                                } else {
                                    List<UserExamPaperQuestionFavorite> questionFavorites = new ArrayList<>();
                                    questionFavorites.add(userExamPaperQuestionFavorite);
                                    timeUserExamPaperQuestionFavoritesMap.put(date, questionFavorites);
                                }
                            }
                            for (String date : timeUserExamPaperQuestionFavoritesMap.keySet()) {
                                MyQuestionRecordDateResponse myQuestionRecordDateResponse = new MyQuestionRecordDateResponse();
                                // 日期
                                myQuestionRecordDateResponse.setDate(date);
                                List<UserExamPaperQuestionFavorite> questionFavorites = timeUserExamPaperQuestionFavoritesMap.get(date);
                                //题目ID集合
                                Set<Integer> questionIdList = questionFavorites.stream().map(UserExamPaperQuestionFavorite::getQuestionId).collect(Collectors.toSet());
                                myQuestionRecordDateResponse.setQuestionNum(questionIdList.size());

                                myQuestionRecordDateResponse.setQuestionIds(new ArrayList<>(questionIdList));
                                myQuestionRecordResponse.getMyQuestionRecordDateResponseList().add(myQuestionRecordDateResponse);
                            }
                        }
                    }
                    break;
                case C3:
                    //查询用户笔记
                    UserExamPaperQuestionNoteQuery userExamPaperQuestionNoteQuery = new UserExamPaperQuestionNoteQuery();
                    userExamPaperQuestionNoteQuery.setProjectId(request.getProjectId());
                    userExamPaperQuestionNoteQuery.setCustomerId(userToken.getCustomerId());
                    userExamPaperQuestionNoteQuery.setStatus(DataStatus.Y.getCode());
                    List<UserExamPaperQuestionNote> userExamPaperQuestionNoteList = userExamPaperQuestionNoteService.findAll(userExamPaperQuestionNoteQuery);
                    if (!isEmpty(userExamPaperQuestionNoteList) && !userExamPaperQuestionNoteList.isEmpty()) {
                        //收藏题目的ids
                        List<Integer> questionIds = userExamPaperQuestionNoteList.stream().map(UserExamPaperQuestionNote::getQuestionId).collect(Collectors.toList());
                        //查询题目信息
                        List<Question> questionList = questionService.findByIds(new ArrayList<>(questionIds));
                        if(isEmpty(questionList) || questionList.isEmpty()){
                            return new Response<>(myQuestionRecordResponse);
                        }
                        //按知识点展示
                        if (MyQuestionRecordShowType.M1.getCode() == request.getShowType()) {
                            queryUserExamPaperRecordByProjectItem(request.getProjectId(),myQuestionRecordResponse, questionList);
                        }
                        //按时间展示
                        if (MyQuestionRecordShowType.M2.getCode() == request.getShowType()) {
                            List<UserExamPaperQuestionNote> userExamPaperQuestionNotes = userExamPaperQuestionNoteList.stream().sorted(Comparator.comparing(UserExamPaperQuestionNote::getModifyTime).reversed()).collect(Collectors.toList());
                            Map<String, List<UserExamPaperQuestionNote>> timeUserExamPaperQuestionNotesMap = new LinkedHashMap<>();
                            for (UserExamPaperQuestionNote userExamPaperQuestionNote : userExamPaperQuestionNotes) {
                                String date = DateUtil.format(userExamPaperQuestionNote.getModifyTime(), DATE_FORMAT);
                                if (timeUserExamPaperQuestionNotesMap.containsKey(date)) {
                                    timeUserExamPaperQuestionNotesMap.get(date).add(userExamPaperQuestionNote);
                                } else {
                                    List<UserExamPaperQuestionNote> notes = new ArrayList<>();
                                    notes.add(userExamPaperQuestionNote);
                                    timeUserExamPaperQuestionNotesMap.put(date, notes);
                                }
                            }
                            for (String date : timeUserExamPaperQuestionNotesMap.keySet()) {
                                MyQuestionRecordDateResponse myQuestionRecordDateResponse = new MyQuestionRecordDateResponse();
                                // 日期
                                myQuestionRecordDateResponse.setDate(date);
                                List<UserExamPaperQuestionNote> notes = timeUserExamPaperQuestionNotesMap.get(date);
                                //题目ID集合
                                Set<Integer> questionIdList = notes.stream().map(UserExamPaperQuestionNote::getQuestionId).collect(Collectors.toSet());
                                myQuestionRecordDateResponse.setQuestionNum(questionIdList.size());
                                myQuestionRecordDateResponse.setQuestionIds(new ArrayList<>(questionIdList));
                                myQuestionRecordResponse.getMyQuestionRecordDateResponseList().add(myQuestionRecordDateResponse);
                            }
                        }
                    }
                    break;
            }
            return new Response<>(OK, SUCCESS, myQuestionRecordResponse);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }

    /**
     * 封装错题档案
     */
    private void queryMyErrorQuestionRecord(Integer projectId,MyQuestionRecordResponse myQuestionRecordResponse, Integer ShowType, List<UserExamPaperRecord> userExamPaperRecordList) {
        //按修改时间降序排序
        List<UserExamPaperRecord> sortUserExamPaperRecordList =  userExamPaperRecordList.stream().sorted(Comparator.comparing(UserExamPaperRecord::getModifyTime).reversed()).collect(Collectors.toList());
        //同一个试卷多次做题记录只取最新的一次
        List<UserExamPaperRecord> userExamPaperRecords = new ArrayList<>();
        //存储 智能组卷、快速练习 课后练习-题目 组建的虚拟卷
        List<UserExamPaperRecord> userExamPaperRecordFictitious = new ArrayList<>();
        //存储 模拟考试 历年真题 科目练习 课后练习-试卷系统卷
        List<UserExamPaperRecord> userExamPaperRecordOther = new ArrayList<>();
        for (UserExamPaperRecord userExamPaperRecord : sortUserExamPaperRecordList) {
            if(QuestionArguments.USER_EXAM_PAPER_CATALOG_CODE_LIST.contains(userExamPaperRecord.getCatalog())){
                userExamPaperRecordFictitious.add(userExamPaperRecord);
            }else{
                userExamPaperRecordOther.add(userExamPaperRecord);
            }
        }
        //虚拟卷取最新做题记录
        Map<Integer, List<UserExamPaperRecord>> examPaperIdRecordFictitiousListMap = userExamPaperRecordFictitious.stream().collect(Collectors.groupingBy(UserExamPaperRecord::getExamPaperId,
                Collectors.collectingAndThen(
                        Collectors.toList(),
                        list -> list.stream()
                                .sorted(Comparator.comparing(UserExamPaperRecord::getCreateTime).reversed())
                                .collect(Collectors.toList())
                )
        ));
        for (List<UserExamPaperRecord> value : examPaperIdRecordFictitiousListMap.values()) {
            userExamPaperRecords.add(value.get(0));
        }
        //系统卷取最新做题记录
        Map<Integer, List<UserExamPaperRecord>> examPaperIdRecordOtherListMap = userExamPaperRecordOther.stream().collect(Collectors.groupingBy(UserExamPaperRecord::getExamPaperId,
                Collectors.collectingAndThen(
                        Collectors.toList(),
                        list -> list.stream()
                                .sorted(Comparator.comparing(UserExamPaperRecord::getCreateTime).reversed())
                                .collect(Collectors.toList())
                )
        ));
        for (List<UserExamPaperRecord> value : examPaperIdRecordOtherListMap.values()) {
            userExamPaperRecords.add(value.get(0));
        }

        List<Integer> userExamPaperRecordIds = userExamPaperRecords.stream().map(UserExamPaperRecord::getId).collect(Collectors.toList());


        //2.查询用户已经做过的题
        UserExamPaperRecordDetailQuery userExamPaperRecordDetailQuery = new UserExamPaperRecordDetailQuery();
        userExamPaperRecordDetailQuery.setUserExamPaperRecordIds(new ArrayList<>(userExamPaperRecordIds));
        userExamPaperRecordDetailQuery.setStatus(DataStatus.Y.getCode());
        List<UserExamPaperRecordDetail> userExamPaperRecordDetailList = userExamPaperRecordDetailService.findAll(userExamPaperRecordDetailQuery);
        //用户已经做过的题的id
        Set<Integer> questionSet = userExamPaperRecordDetailList.stream().map(UserExamPaperRecordDetail::getQuestionId).collect(Collectors.toSet());
        //查询题目信息（不包含材料题的小题）
        QuestionQuery questionQuery = new QuestionQuery();
        questionQuery.setParentId(0);
        questionQuery.setIds(new ArrayList<>(questionSet));
        questionQuery.setStatus(DataStatus.Y.getCode());
        List<Question> questionList = questionService.findAll(questionQuery);

        //存储做错的题
        Set<Integer> questionIds = new HashSet<>();
        //存储做错的题的记录详情
        List<UserExamPaperRecordDetail> errorUserExamPaperRecordDetailList = new ArrayList<>();

        //3.找出做错的题 (只判断选择题 和判断题)
        Map<Integer, Question> questionMap = questionList.stream().collect(Collectors.toMap(Question::getId, Function.identity()));
        for (UserExamPaperRecordDetail userExamPaperRecordDetail : userExamPaperRecordDetailList) {
            if(questionMap.containsKey(userExamPaperRecordDetail.getQuestionId()) &&
                    QuestionArguments.CAN_DETERMINE_ANSWER_RIGHT_OR_WRONG.contains(questionMap.get(userExamPaperRecordDetail.getQuestionId()).getType())){
                Question question = questionMap.get(userExamPaperRecordDetail.getQuestionId());
                if (!question.getAnswer().equals(userExamPaperRecordDetail.getAnswer())) {
                    questionIds.add(question.getId());
                    errorUserExamPaperRecordDetailList.add(userExamPaperRecordDetail);
                }
            }
        }

        //封装数据
        //安知识点展示
        if (MyQuestionRecordShowType.M1.getCode() == ShowType && !questionIds.isEmpty()) {
            queryUserExamPaperRecordByErrorProjectItemV2(projectId,myQuestionRecordResponse,errorUserExamPaperRecordDetailList);
        }

        //按时间展示
        if (MyQuestionRecordShowType.M2.getCode() == ShowType && !errorUserExamPaperRecordDetailList.isEmpty()) {

            Map<String, List<UserExamPaperRecordDetail>> timeUserExamPaperRecordsMap = new LinkedHashMap<>();
            for (UserExamPaperRecordDetail userExamPaperRecordDetail : errorUserExamPaperRecordDetailList) {
                String date = DateUtil.format(userExamPaperRecordDetail.getModifyTime(), DATE_FORMAT);
                if (timeUserExamPaperRecordsMap.containsKey(date)) {
                    timeUserExamPaperRecordsMap.get(date).add(userExamPaperRecordDetail);
                } else {
                    List<UserExamPaperRecordDetail> recordList = new ArrayList<>();
                    recordList.add(userExamPaperRecordDetail);
                    timeUserExamPaperRecordsMap.put(date, recordList);
                }
            }

            for (String date : timeUserExamPaperRecordsMap.keySet()) {
                MyQuestionRecordDateResponse myQuestionRecordDateResponse = new MyQuestionRecordDateResponse();
                // 日期
                myQuestionRecordDateResponse.setDate(date);
                Set<Integer> questionIdSet = new HashSet<>();
                List<Integer> detailIdList = new ArrayList<>();
                for (UserExamPaperRecordDetail userExamPaperRecordDetail : timeUserExamPaperRecordsMap.get(date)) {
                    questionIdSet.add(userExamPaperRecordDetail.getQuestionId());
                    detailIdList.add(userExamPaperRecordDetail.getId());
                }
                myQuestionRecordDateResponse.setQuestionNum(detailIdList.size());
                //题目ID集合
                myQuestionRecordDateResponse.setQuestionIds(new ArrayList<>(questionIdSet));
                //做题记录详情id 集合
                myQuestionRecordDateResponse.setUserExamPaperRecordDetailIds(detailIdList);
                myQuestionRecordResponse.getMyQuestionRecordDateResponseList().add(myQuestionRecordDateResponse);
            }
        }
    }

    /**
     * 收藏记录、题目笔记按知识点展示封装
     *
     */
    private void queryUserExamPaperRecordByProjectItem(Integer projectId,MyQuestionRecordResponse myQuestionRecordResponse, List<Question> questions) {

        //分离出题目的科目id
        Set<Integer> projectItemIds = questions.stream().map(Question::getProjectItemId).collect(Collectors.toSet());
        //根据科目id查询科目信息
        List<ProjectItem> projectItemList = projectItemService.findByIds(new ArrayList<>(projectItemIds));
        //科目id为key 科目对象为value
        Map<Integer, ProjectItem> projectItemMap = projectItemList.stream().collect(Collectors.toMap(ProjectItem::getId, Function.identity()));
        //按科目分组
        Map<Integer, List<Question>> projectItemIdQuestionsMap = questions.stream().collect(Collectors.groupingBy(Question::getProjectItemId));

        for (Integer projectItemId : projectItemIdQuestionsMap.keySet()) {
            MyQuestionItemRecordResponse myQuestionItemRecordResponse = new MyQuestionItemRecordResponse();
            myQuestionItemRecordResponse.setProjectItemId(projectItemId);
            if (projectItemMap.containsKey(projectItemId)) {
                myQuestionItemRecordResponse.setProjectItemName(projectItemMap.get(projectItemId).getName());
            }
            //科目下的错题数量
            List<Question> projectItemQuestions = projectItemIdQuestionsMap.get(projectItemId);
            List<Integer> questionIds = projectItemQuestions.stream().map(Question::getId).collect(Collectors.toList());
            myQuestionItemRecordResponse.setQuestionNum(projectItemQuestions.size());
            myQuestionItemRecordResponse.setQuestionIds(questionIds);
            //科目下的章节树结构
            myQuestionItemRecordResponse.setQuestionChapterResponseList(new ArrayList<>());
            //章节包含的题目集合
            Map<Integer, List<Question>> chapterIdQuestionsMap = new HashMap<>();
            //章节包含的题目id集合
            Map<Integer, List<Integer>> chapterIdQuestionIdsMap = new HashMap<>();
            //科目下错题的章节id
            Set<Integer> questionChapterIdSet = new HashSet<>();
            for (Question question : projectItemQuestions) {
                questionChapterIdSet.add(question.getQuestionChapterId());
                if(chapterIdQuestionsMap.containsKey(question.getQuestionChapterId())){
                    chapterIdQuestionsMap.get(question.getQuestionChapterId()).add(question);
                }else{
                    List<Question> questionList = new ArrayList<>();
                    questionList.add(question);
                    chapterIdQuestionsMap.put(question.getQuestionChapterId(),questionList);
                }
                if(chapterIdQuestionIdsMap.containsKey(question.getQuestionChapterId())){
                    chapterIdQuestionIdsMap.get(question.getQuestionChapterId()).add(question.getId());
                }else{
                    List<Integer> questionIdList = new ArrayList<>();
                    questionIdList.add(question.getId());
                    chapterIdQuestionIdsMap.put(question.getQuestionChapterId(),questionIdList);
                }
            }
            //封装题目章节信息
            List<QuestionChapterResponse> questionChapterResponses = new ArrayList<>();

            if (!questionChapterIdSet.isEmpty()) {
                //查询所有章节信息
                QuestionChapterQuery questionChapterQuery = new QuestionChapterQuery();
                questionChapterQuery.setStatus(DataStatus.Y.getCode());
                questionChapterQuery.setProjectId(projectId);
                List<QuestionChapter> questionChapterAllList = questionChapterService.findAll(questionChapterQuery);
                //存储章节的父级信息
                List<QuestionChapter> parentQuestionChapter = new ArrayList<>();
                //查询章节信息
                List<QuestionChapter> questionChapterList = questionChapterService.findByIds(new ArrayList<>(questionChapterIdSet));
                //遍历获取章节父级信息
                for (QuestionChapter questionChapter : questionChapterList) {
                    getQuestionChapterParents(questionChapter,questionChapterAllList,parentQuestionChapter);
                }
                questionChapterList.addAll(parentQuestionChapter);
                //章节信息去重
                Map<Integer, QuestionChapter> questionChapterMap = new HashMap<>();
                for (QuestionChapter questionChapter : questionChapterList) {
                    questionChapterMap.put(questionChapter.getId(),questionChapter);
                }
                questionChapterList = new ArrayList<>(questionChapterMap.values());
                //封装章节信息
                for (QuestionChapter questionChapter : questionChapterList) {
                    QuestionChapterResponse response = new QuestionChapterResponse();
                    response.setId(questionChapter.getId());
                    response.setName(questionChapter.getName());
                    response.setCatalog(questionChapter.getCatalog());
                    response.setParentId(questionChapter.getParentId());
                    response.setSort(questionChapter.getSort());
                    if (chapterIdQuestionsMap.containsKey(questionChapter.getId())) {
                        response.setQuestionNum(chapterIdQuestionsMap.get(questionChapter.getId()).size());
                    } else {
                        response.setQuestionNum(0);
                    }
                    if (chapterIdQuestionIdsMap.containsKey(questionChapter.getId())) {
                        response.setQuestionIds(chapterIdQuestionIdsMap.get(questionChapter.getId()));
                    } else {
                        response.setQuestionIds(new ArrayList<>());
                    }
                    questionChapterResponses.add(response);
                }
            }

            //计算章节（如果有子级也包含子级）下的题目数量
            for (QuestionChapterResponse questionChapterResponse : questionChapterResponses) {
                //计算章节包含子级的题目总数
                List<QuestionChapterResponse> questionChapterResponseList = new ArrayList<>();
                getChapterQuestionResponseList(questionChapterResponse, questionChapterResponses, questionChapterResponseList);
                //计算章节下题目数量
                Integer chapterQuestionNum = questionChapterResponseList.stream().collect(Collectors.summingInt(QuestionChapterResponse::getQuestionNum));

                questionChapterResponse.setQuestionNum(chapterQuestionNum);
                //计算章节包含子级的所有题目的ID集合
                List<Integer> chapterQuestionIdList = new ArrayList<>();
                for (QuestionChapterResponse chapterResponse : questionChapterResponseList) {
                    chapterQuestionIdList.addAll(chapterResponse.getQuestionIds());
                }
                questionChapterResponse.setQuestionIds(chapterQuestionIdList);
            }

            //将科目下的错题按章节组成章节树结构
            List<QuestionChapterResponse> treeResponse = buildTree(questionChapterResponses, 0);
            myQuestionItemRecordResponse.getQuestionChapterResponseList().addAll(treeResponse);
            myQuestionRecordResponse.getMyQuestionItemRecordResponseList().add(myQuestionItemRecordResponse);
        }
    }
    /**
     * 错题档案按知识点展示封装
     *
     * @param myQuestionRecordResponse
     */
    private void queryUserExamPaperRecordByErrorProjectItemV2(Integer projectId, MyQuestionRecordResponse myQuestionRecordResponse, List<UserExamPaperRecordDetail> errorUserExamPaperRecordDetailList) {
        if(!errorUserExamPaperRecordDetailList.isEmpty()){
            Set<Integer> questionIdSet = new HashSet<>();
            for (UserExamPaperRecordDetail userExamPaperRecordDetail : errorUserExamPaperRecordDetailList) {
                questionIdSet.add(userExamPaperRecordDetail.getQuestionId());
            }
            //查询题目信息
            QuestionQuery questionQuery = new QuestionQuery();
            questionQuery.setIds(new ArrayList<>(questionIdSet));
            questionQuery.setStatus(DataStatus.Y.getCode());
            List<Question> questions = questionService.findAll(questionQuery);
            Map<Integer, Question> questionMap = new HashMap<>();
            Set<Integer> projectItemIds = new HashSet<>();
            for (Question question : questions) {
                questionMap.put(question.getId(),question);
                projectItemIds.add(question.getProjectItemId());
            }
            Map<Integer, ProjectItem> projectItemMap = new HashMap<>();
            if(!projectItemIds.isEmpty()){
                //根据科目id查询科目信息
                List<ProjectItem> projectItemList = projectItemService.findByIds(new ArrayList<>(projectItemIds));
                //科目id为key 科目对象为value
                projectItemMap = projectItemList.stream().collect(Collectors.toMap(ProjectItem::getId, Function.identity()));
            }

            //detail 装换为 detailResponse
            List<UserExamPaperRecordDetailResponse> userExamPaperRecordDetailResponseList = new ArrayList<>();
            for (UserExamPaperRecordDetail userExamPaperRecordDetail : errorUserExamPaperRecordDetailList) {
                UserExamPaperRecordDetailResponse userExamPaperRecordDetailResponse = new UserExamPaperRecordDetailResponse();
                userExamPaperRecordDetailResponse.setId(userExamPaperRecordDetail.getId());
                if(questionMap.containsKey(userExamPaperRecordDetail.getQuestionId())){
                    Question question = questionMap.get(userExamPaperRecordDetail.getQuestionId());
                    userExamPaperRecordDetailResponse.setQuestionResponse(getQuestionResponseForQuestion(question));
                }
                userExamPaperRecordDetailResponse.setQuestionId(userExamPaperRecordDetail.getQuestionId());
                userExamPaperRecordDetailResponseList.add(userExamPaperRecordDetailResponse);
            }
            Map<Integer, List<UserExamPaperRecordDetailResponse>> projectItemIdExamDetailResponseMap = userExamPaperRecordDetailResponseList.stream().collect(Collectors.groupingBy(response -> response.getQuestionResponse().getProjectItemId()));

            for (Integer projectItemId : projectItemIdExamDetailResponseMap.keySet()) {
                MyQuestionItemRecordResponse myQuestionItemRecordResponse = new MyQuestionItemRecordResponse();
                myQuestionItemRecordResponse.setProjectItemId(projectItemId);
                if (projectItemMap.containsKey(projectItemId)) {
                    myQuestionItemRecordResponse.setProjectItemName(projectItemMap.get(projectItemId).getName());
                }
                //科目下的错题记录
                List<UserExamPaperRecordDetailResponse> userExamPaperRecordDetailResponses = projectItemIdExamDetailResponseMap.get(projectItemId);
                List<Integer> questionIds = new ArrayList<>();
                List<Integer> userExamPaperRecordDetailIds = new ArrayList<>();
                for (UserExamPaperRecordDetailResponse userExamPaperRecordDetailResponse : userExamPaperRecordDetailResponses) {
                    questionIds.add(userExamPaperRecordDetailResponse.getQuestionId());
                    userExamPaperRecordDetailIds.add(userExamPaperRecordDetailResponse.getId());
                }
                //科目下包含的题目数量
                myQuestionItemRecordResponse.setQuestionNum(userExamPaperRecordDetailResponses.size());
                //科目下包含的题目id集合
                myQuestionItemRecordResponse.setQuestionIds(questionIds);
                //科目下包含的做题记录id集合
                myQuestionItemRecordResponse.setUserExamPaperRecordDetailIds(userExamPaperRecordDetailIds);
                //科目下的章节树结构
                myQuestionItemRecordResponse.setQuestionChapterResponseList(new ArrayList<>());
                //章节包含的做题记录详情集合
                //Map<Integer, List<UserExamPaperRecordDetailResponse>> chapterIdQuestionsMap = new HashMap<>();
                //章节包含的题目id集合
                Map<Integer, List<Integer>> chapterIdQuestionIdsMap = new HashMap<>();
                //章节包含的做题记录详情集合
                Map<Integer, List<Integer>> chapterIdDetailIdsMap = new HashMap<>();
                //科目下错题的章节id
                Set<Integer> questionChapterIdSet = new HashSet<>();
                for (UserExamPaperRecordDetailResponse userExamPaperRecordDetailResponse : userExamPaperRecordDetailResponses) {
                    questionChapterIdSet.add(userExamPaperRecordDetailResponse.getQuestionResponse().getQuestionChapterId());

                    /*if (chapterIdQuestionsMap.containsKey(userExamPaperRecordDetailResponse.getQuestionResponse().getQuestionChapterId())) {
                        chapterIdQuestionsMap.get(userExamPaperRecordDetailResponse.getQuestionResponse().getQuestionChapterId()).add(userExamPaperRecordDetailResponse);
                    } else {
                        List<UserExamPaperRecordDetailResponse> userExamPaperRecordDetails = new ArrayList<>();
                        userExamPaperRecordDetails.add(userExamPaperRecordDetailResponse);
                        chapterIdQuestionsMap.put(userExamPaperRecordDetailResponse.getQuestionResponse().getQuestionChapterId(), userExamPaperRecordDetails);
                    }*/

                    if (chapterIdDetailIdsMap.containsKey(userExamPaperRecordDetailResponse.getQuestionResponse().getQuestionChapterId())) {
                        chapterIdDetailIdsMap.get(userExamPaperRecordDetailResponse.getQuestionResponse().getQuestionChapterId()).add(userExamPaperRecordDetailResponse.getId());
                    } else {
                        List<Integer> userExamPaperRecordDetailIdList = new ArrayList<>();
                        userExamPaperRecordDetailIdList.add(userExamPaperRecordDetailResponse.getId());
                        chapterIdDetailIdsMap.put(userExamPaperRecordDetailResponse.getQuestionResponse().getQuestionChapterId(), userExamPaperRecordDetailIdList);
                    }

                    if (chapterIdQuestionIdsMap.containsKey(userExamPaperRecordDetailResponse.getQuestionResponse().getQuestionChapterId())) {
                        chapterIdQuestionIdsMap.get(userExamPaperRecordDetailResponse.getQuestionResponse().getQuestionChapterId()).add(userExamPaperRecordDetailResponse.getQuestionId());
                    } else {
                        List<Integer> questionIdList = new ArrayList<>();
                        questionIdList.add(userExamPaperRecordDetailResponse.getQuestionId());
                        chapterIdQuestionIdsMap.put(userExamPaperRecordDetailResponse.getQuestionResponse().getQuestionChapterId(), questionIdList);
                    }
                }
                //封装题目章节信息
                List<QuestionChapterResponse> questionChapterResponses = new ArrayList<>();
                if (!questionChapterIdSet.isEmpty()) {
                    //查询所有章节信息
                    QuestionChapterQuery questionChapterQuery = new QuestionChapterQuery();
                    questionChapterQuery.setStatus(DataStatus.Y.getCode());
                    questionChapterQuery.setProjectId(projectId);
                    List<QuestionChapter> questionChapterAllList = questionChapterService.findAll(questionChapterQuery);
                    //存储章节的父级信息
                    List<QuestionChapter> parentQuestionChapter = new ArrayList<>();
                    //查询章节信息
                    List<QuestionChapter> questionChapterList = questionChapterService.findByIds(new ArrayList<>(questionChapterIdSet));
                    //遍历获取章节父级信息
                    for (QuestionChapter questionChapter : questionChapterList) {
                        getQuestionChapterParents(questionChapter, questionChapterAllList, parentQuestionChapter);
                    }
                    questionChapterList.addAll(parentQuestionChapter);
                    //章节信息去重
                    Map<Integer, QuestionChapter> questionChapterMap = new HashMap<>();
                    for (QuestionChapter questionChapter : questionChapterList) {
                        questionChapterMap.put(questionChapter.getId(), questionChapter);
                    }
                    questionChapterList = new ArrayList<>(questionChapterMap.values());
                    //封装章节信息
                    for (QuestionChapter questionChapter : questionChapterList) {
                        QuestionChapterResponse response = new QuestionChapterResponse();
                        response.setId(questionChapter.getId());
                        response.setName(questionChapter.getName());
                        response.setCatalog(questionChapter.getCatalog());
                        response.setParentId(questionChapter.getParentId());
                        response.setSort(questionChapter.getSort());

                        if (chapterIdDetailIdsMap.containsKey(questionChapter.getId())) {
                            response.setQuestionNum(chapterIdDetailIdsMap.get(questionChapter.getId()).size());
                        } else {
                            response.setQuestionNum(0);
                        }
                        if (chapterIdDetailIdsMap.containsKey(questionChapter.getId())) {
                            response.setUserExamPaperRecordDetailIds(chapterIdDetailIdsMap.get(questionChapter.getId()));
                        } else {
                            response.setUserExamPaperRecordDetailIds(new ArrayList<>());
                        }

                        if (chapterIdQuestionIdsMap.containsKey(questionChapter.getId())) {
                            response.setQuestionIds(chapterIdQuestionIdsMap.get(questionChapter.getId()));
                        } else {
                            response.setQuestionIds(new ArrayList<>());
                        }

                        questionChapterResponses.add(response);
                    }
                }
                //计算章节包含子级的所有题目的ID集合

                //计算章节（如果有子级也包含子级）下的题目数量
                for (QuestionChapterResponse questionChapterResponse : questionChapterResponses) {
                    //计算章节包含子级的题目总数
                    List<QuestionChapterResponse> questionChapterResponseList = new ArrayList<>();
                    getChapterQuestionResponseList(questionChapterResponse, questionChapterResponses, questionChapterResponseList);
                    //计算章节下题目数量
                    //Integer chapterQuestionNum = questionChapterResponseList.stream().collect(Collectors.summingInt(QuestionChapterResponse::getQuestionNum));


                    Set<Integer> chapterQuestionIdList = new HashSet<>();
                    Set<Integer> chapterExamDetailIdList = new HashSet<>();
                    for (QuestionChapterResponse chapterResponse : questionChapterResponseList) {
                        chapterQuestionIdList.addAll(chapterResponse.getQuestionIds());
                        chapterExamDetailIdList.addAll(chapterResponse.getUserExamPaperRecordDetailIds());
                    }
                    questionChapterResponse.setQuestionNum(chapterExamDetailIdList.size());
                    questionChapterResponse.setQuestionIds(new ArrayList<>(chapterQuestionIdList));
                    questionChapterResponse.setUserExamPaperRecordDetailIds(new ArrayList<>(chapterExamDetailIdList));
                }
                //将科目下的错题按章节组成章节树结构
                List<QuestionChapterResponse> treeResponse = buildTree(questionChapterResponses, 0);
                myQuestionItemRecordResponse.getQuestionChapterResponseList().addAll(treeResponse);
                myQuestionRecordResponse.getMyQuestionItemRecordResponseList().add(myQuestionItemRecordResponse);
            }
        }
    }
    /**
     * QuestionResponse,他拥有当前question对象所拥有的所有试题基本属性(除纯文本字段)
     */
    private QuestionResponse getQuestionResponseForQuestion(Question question ){
        QuestionResponse result = new QuestionResponse();
        result.setId(question.getId());
        result.setOption(question.getOption());
        result.setQuestionLibraryId(question.getQuestionLibraryId());
        result.setProjectItemId(question.getProjectItemId());
        result.setQuestionTemplateItemId(question.getQuestionTemplateItemId());
        result.setQuestionChapterId(question.getQuestionChapterId());
        result.setType(question.getType());
        result.setQuestion(question.getQuestion());
        result.setAnswer(question.getAnswer());
        result.setComment(question.getComment());
        result.setCatalog(question.getCatalog());
        result.setQuestionLabelId(question.getQuestionLabelId());
        result.setFree(question.getFree());
        result.setDifficultyLevel(question.getDifficultyLevel());
        result.setScore(question.getScore());
        result.setSort(question.getSort());
        result.setSubExaminationPoint(question.getSubExaminationPoint());
        result.setYear(question.getYear());
        return result;
    }
  /**
     * 错题档案按知识点展示封装
     *
     * @param myQuestionRecordResponse
     */
    private void queryUserExamPaperRecordByErrorProjectItem(Integer projectId, MyQuestionRecordResponse myQuestionRecordResponse, Set<Integer> questionIdSet) {
        //查询题目信息
        QuestionQuery questionQuery = new QuestionQuery();
        questionQuery.setIds(new ArrayList<>(questionIdSet));
        questionQuery.setStatus(DataStatus.Y.getCode());
        List<Question> questions = questionService.findAll(questionQuery);
        if (!isEmpty(questions) && !questions.isEmpty()) {

            //分离出题目的科目id
            Set<Integer> projectItemIds = questions.stream().map(Question::getProjectItemId).collect(Collectors.toSet());
            //根据科目id查询科目信息
            List<ProjectItem> projectItemList = projectItemService.findByIds(new ArrayList<>(projectItemIds));
            //科目id为key 科目对象为value
            Map<Integer, ProjectItem> projectItemMap = projectItemList.stream().collect(Collectors.toMap(ProjectItem::getId, Function.identity()));
            //按科目分组
            Map<Integer, List<Question>> projectItemIdQuestionsMap = questions.stream().collect(Collectors.groupingBy(Question::getProjectItemId));

            for (Integer projectItemId : projectItemIdQuestionsMap.keySet()) {
                MyQuestionItemRecordResponse myQuestionItemRecordResponse = new MyQuestionItemRecordResponse();
                myQuestionItemRecordResponse.setProjectItemId(projectItemId);
                if (projectItemMap.containsKey(projectItemId)) {
                    myQuestionItemRecordResponse.setProjectItemName(projectItemMap.get(projectItemId).getName());
                }
                //科目下的错题数量
                List<Question> projectItemQuestions = projectItemIdQuestionsMap.get(projectItemId);

                List<Integer> questionIds = projectItemQuestions.stream().map(Question::getId).collect(Collectors.toList());

                myQuestionItemRecordResponse.setQuestionNum(projectItemQuestions.size());
                myQuestionItemRecordResponse.setQuestionIds(questionIds);
                //科目下的章节树结构
                myQuestionItemRecordResponse.setQuestionChapterResponseList(new ArrayList<>());
                //章节包含的题目集合
                Map<Integer, List<Question>> chapterIdQuestionsMap = new HashMap<>();
                //章节包含的题目id集合
                Map<Integer, List<Integer>> chapterIdQuestionIdsMap = new HashMap<>();
                //科目下错题的章节id
                Set<Integer> questionChapterIdSet = new HashSet<>();
                for (Question question : projectItemQuestions) {
                    questionChapterIdSet.add(question.getQuestionChapterId());
                    if (chapterIdQuestionsMap.containsKey(question.getQuestionChapterId())) {
                        chapterIdQuestionsMap.get(question.getQuestionChapterId()).add(question);
                    } else {
                        List<Question> questionList = new ArrayList<>();
                        questionList.add(question);
                        chapterIdQuestionsMap.put(question.getQuestionChapterId(), questionList);
                    }
                    if (chapterIdQuestionIdsMap.containsKey(question.getQuestionChapterId())) {
                        chapterIdQuestionIdsMap.get(question.getQuestionChapterId()).add(question.getId());
                    } else {
                        List<Integer> questionIdList = new ArrayList<>();
                        questionIdList.add(question.getId());
                        chapterIdQuestionIdsMap.put(question.getQuestionChapterId(), questionIdList);
                    }
                }
                //封装题目章节信息
                List<QuestionChapterResponse> questionChapterResponses = new ArrayList<>();

                if (!questionChapterIdSet.isEmpty()) {
                    //查询所有章节信息
                    QuestionChapterQuery questionChapterQuery = new QuestionChapterQuery();
                    questionChapterQuery.setStatus(DataStatus.Y.getCode());
                    questionChapterQuery.setProjectId(projectId);
                    List<QuestionChapter> questionChapterAllList = questionChapterService.findAll(questionChapterQuery);
                    //存储章节的父级信息
                    List<QuestionChapter> parentQuestionChapter = new ArrayList<>();
                    //查询章节信息
                    List<QuestionChapter> questionChapterList = questionChapterService.findByIds(new ArrayList<>(questionChapterIdSet));
                    //遍历获取章节父级信息
                    for (QuestionChapter questionChapter : questionChapterList) {
                        getQuestionChapterParents(questionChapter, questionChapterAllList, parentQuestionChapter);
                    }
                    questionChapterList.addAll(parentQuestionChapter);
                    //章节信息去重
                    Map<Integer, QuestionChapter> questionChapterMap = new HashMap<>();
                    for (QuestionChapter questionChapter : questionChapterList) {
                        questionChapterMap.put(questionChapter.getId(), questionChapter);
                    }
                    questionChapterList = new ArrayList<>(questionChapterMap.values());
                    //封装章节信息
                    for (QuestionChapter questionChapter : questionChapterList) {
                        QuestionChapterResponse response = new QuestionChapterResponse();
                        response.setId(questionChapter.getId());
                        response.setName(questionChapter.getName());
                        response.setCatalog(questionChapter.getCatalog());
                        response.setParentId(questionChapter.getParentId());
                        response.setSort(questionChapter.getSort());
                        if (chapterIdQuestionsMap.containsKey(questionChapter.getId())) {
                            response.setQuestionNum(chapterIdQuestionsMap.get(questionChapter.getId()).size());
                        } else {
                            response.setQuestionNum(0);
                        }
                        if (chapterIdQuestionIdsMap.containsKey(questionChapter.getId())) {
                            response.setQuestionIds(chapterIdQuestionIdsMap.get(questionChapter.getId()));
                        } else {
                            response.setQuestionIds(new ArrayList<>());
                        }
                        questionChapterResponses.add(response);
                    }
                }

                //计算章节（如果有子级也包含子级）下的题目数量
                for (QuestionChapterResponse questionChapterResponse : questionChapterResponses) {
                    //计算章节包含子级的题目总数
                    List<QuestionChapterResponse> questionChapterResponseList = new ArrayList<>();
                    getChapterQuestionResponseList(questionChapterResponse, questionChapterResponses, questionChapterResponseList);
                    //计算章节下题目数量
                    Integer chapterQuestionNum = questionChapterResponseList.stream().collect(Collectors.summingInt(QuestionChapterResponse::getQuestionNum));

                    questionChapterResponse.setQuestionNum(chapterQuestionNum);
                    //计算章节包含子级的所有题目的ID集合
                    List<Integer> chapterQuestionIdList = new ArrayList<>();
                    for (QuestionChapterResponse chapterResponse : questionChapterResponseList) {
                        chapterQuestionIdList.addAll(chapterResponse.getQuestionIds());
                    }
                    questionChapterResponse.setQuestionIds(chapterQuestionIdList);
                }

                //将科目下的错题按章节组成章节树结构
                List<QuestionChapterResponse> treeResponse = buildTree(questionChapterResponses, 0);
                myQuestionItemRecordResponse.getQuestionChapterResponseList().addAll(treeResponse);
                myQuestionRecordResponse.getMyQuestionItemRecordResponseList().add(myQuestionItemRecordResponse);
            }
        }
    }

    /**
     * 递归获取章节（如果有子级也包含子级）下的题目ID集合
     *
     * @param questionChapterResponse
     * @param questionChapterResponses
     * @return
     */
    private void getChapterQuestionResponseList(QuestionChapterResponse questionChapterResponse, List<QuestionChapterResponse> questionChapterResponses, List<QuestionChapterResponse> questionChapterResponseList) {
        if (questionChapterResponse != null && !isEmpty(questionChapterResponse.getQuestionNum())) {
            questionChapterResponseList.add(questionChapterResponse);
        }
        findChildQuestionChapterResponse(questionChapterResponse, questionChapterResponses, questionChapterResponseList);
        //return questionChapterResponseList;

    }

    /**
     * 获取章节子集
     */
    private void findChildQuestionChapterResponse(QuestionChapterResponse questionChapterResponse, List<QuestionChapterResponse> questionChapterResponses, List<QuestionChapterResponse> questionChapterResponseList) {
        if (questionChapterResponses != null && !questionChapterResponses.isEmpty()) {
            for (QuestionChapterResponse chapterResponse : questionChapterResponses) {
                if (chapterResponse.getParentId() != null && chapterResponse.getParentId().equals(questionChapterResponse.getId())) {
                    questionChapterResponseList.add(chapterResponse);
                    this.findChildQuestionChapterResponse(chapterResponse, questionChapterResponses, questionChapterResponseList);
                }
            }
        }
    }


    /**
     * 构建树结构
     */
    private List<QuestionChapterResponse> buildTree(List<QuestionChapterResponse> list, Integer pid) {
        List<QuestionChapterResponse> tree = new ArrayList<>();
        for (QuestionChapterResponse node : list) {
            if (pid.equals(node.getParentId())) {
                tree.add(findChild(node, list));
            }
        }
        tree.sort(Comparator.comparingInt(QuestionChapterResponse::getSort));
        return tree;
    }

    /**
     * 递归构建子级树结构
     */
    private QuestionChapterResponse findChild(QuestionChapterResponse node, List<QuestionChapterResponse> list) {
        node.setChildList(new ArrayList<>());

        for (QuestionChapterResponse item : list) {
            if (node.getId().equals(item.getParentId())) {
                node.getChildList().add(findChild(item, list));
            }
        }
        if (node.getChildList() != null && !node.getChildList().isEmpty()) {
            node.getChildList().sort(Comparator.comparingInt(QuestionChapterResponse::getSort));
        }
        return node;
    }

    /**
     * 用户端-今日做题数据
     */
    @Token
    @RequestMapping("/v1/user/exam/paper/record/today/query")
    public Response<UserExamQuestionHistoryResponse> queryTodayUserExamPaperRecord(@RequestBody UserExamPaperRecordRequest request) {
        try {
            UserToken userToken = this.getUserToken();
            Date serverTime = this.getServerTime();
            UserExamQuestionHistoryResponse response = new UserExamQuestionHistoryResponse();
            response.setAccuracy(BigDecimal.ZERO);
            response.setQuestionNumber(0);
            //当天的开始时间
            Date dayStartTime = DateUtil.getDayStartTime(serverTime);
            //当天的结束时间
            Date dayEndTime = DateUtil.getDayEndTime(serverTime);
            //查询当天的做题记录
            UserExamPaperRecordQuery userExamPaperRecordQuery = new UserExamPaperRecordQuery();
            userExamPaperRecordQuery.setCustomerId(userToken.getCustomerId());
            userExamPaperRecordQuery.setDayStartTime(dayStartTime);
            userExamPaperRecordQuery.setDayEndTime(dayEndTime);
            userExamPaperRecordQuery.setStatus(DataStatus.Y.getCode());
            List<UserExamPaperRecord> userExamPaperRecordList = userExamPaperRecordService.findAll(userExamPaperRecordQuery);
            if (!isEmpty(userExamPaperRecordList) && !userExamPaperRecordList.isEmpty()) {
                List<Integer> userExamPaperRecordIdList = userExamPaperRecordList.stream().map(UserExamPaperRecord::getId).collect(Collectors.toList());
                UserExamPaperRecordDetailQuery userExamPaperRecordDetailQuery = new UserExamPaperRecordDetailQuery();
                userExamPaperRecordDetailQuery.setUserExamPaperRecordIds(userExamPaperRecordIdList);
                userExamPaperRecordDetailQuery.setStatus(DataStatus.Y.getCode());
                List<UserExamPaperRecordDetail> userExamPaperRecordDetailList = userExamPaperRecordDetailService.findAll(userExamPaperRecordDetailQuery);
                if (!isEmpty(userExamPaperRecordDetailList) && !userExamPaperRecordDetailList.isEmpty()) {
                    //分离出做题记录的题目id集合
                    Set<Integer> questionIdList = userExamPaperRecordDetailList.stream().map(UserExamPaperRecordDetail::getQuestionId).collect(Collectors.toSet());
                    //查询题目信息
                    QuestionQuery questionQuery = new QuestionQuery();
                    questionQuery.setIds(new ArrayList<>(questionIdList));
                    questionQuery.setStatus(DataStatus.Y.getCode());
                    List<Question> questionList = questionService.findAll(questionQuery);
                    Map<Integer, Question> questionMap = questionList.stream().collect(Collectors.toMap(Question::getId, Function.identity()));
                    int totalQuestionNum = 0;
                    int rightQuestionNum = 0;
                    //只判断选择题
                    for (UserExamPaperRecordDetail userExamPaperRecordDetail : userExamPaperRecordDetailList) {
                        if (questionMap.containsKey(userExamPaperRecordDetail.getQuestionId()) &&
                                (QuestionArguments.CAN_DETERMINE_ANSWER_RIGHT_OR_WRONG.contains(questionMap.get(userExamPaperRecordDetail.getQuestionId())))) {
                            if (!isEmpty(userExamPaperRecordDetail.getAnswer()) && questionMap.get(userExamPaperRecordDetail.getQuestionId()).getAnswer().equals(userExamPaperRecordDetail.getAnswer())) {
                                rightQuestionNum = rightQuestionNum + 1;
                            }
                            totalQuestionNum = totalQuestionNum + 1;
                        }
                    }
                    //计算正确率
                    if (totalQuestionNum == 0 || rightQuestionNum == 0) {
                        response.setAccuracy(BigDecimal.ZERO);
                    } else {
                        BigDecimal totalQuestionBigDecimal = new BigDecimal(totalQuestionNum);
                        BigDecimal rightQuestionBigDecimal = new BigDecimal(rightQuestionNum);
                        BigDecimal accuracy = rightQuestionBigDecimal.divide(totalQuestionBigDecimal, 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)).setScale(1, BigDecimal.ROUND_HALF_UP);
                        response.setAccuracy(accuracy);
                    }
                    //做题总数
                    response.setQuestionNumber(totalQuestionNum);
                }
            }
            return new Response<>(OK, SUCCESS, response);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }

    /**
     * 我的题库(做题记录)——用户端
     * 未购课学员试用题库到期后，或已购课学员全部课程已到期，并且后台设置到期后不能刷题，那么在到期后，
     * 点击之前的【智能组卷】【快速练习】记录和付费【模拟考试】【历年真题】【科目练习】【课后练习】记录弹窗提示已过期。
     * <AUTHOR>
     */
    @RequestMapping(value = "/v1/user/exam/paper/record/detail/my/query")
    @Token
    public Response<?> userExamPaperRecordDetail(@RequestBody MyQuestionRecordRequest request) {
        UserToken userToken = this.getUserToken();
        Date serverTime = getServerTime();
        UserExamPaperRecordValidator validator = new UserExamPaperRecordValidator();
        try {
            if (!validator.onProjectId(request.getProjectId()).onCatalog(request.getCatalog()).result()) {
                return new Response<>(ERROR, validator.getErrorMessage());
            }
           /* if(checkCustomerExamPaperExpire(request.getProjectId(),userToken.getCustomerId(),serverTime)){
                return new Response<>(ERROR, "已过期");
            }*/
            NewPageResponse<UserExamPaperRecordResponse> response = new NewPageResponse<>();

            UserExamPaperRecordQuery userExamPaperRecordQuery = new UserExamPaperRecordQuery();
            //2025-06-17 李明洋 增加单题练习和星级组卷之后,增加做题记录区分字段筛选:playType
            userExamPaperRecordQuery.setPlayType(UserExamPaperRecordPlayType.C0.getCode());
            userExamPaperRecordQuery.setProjectId(request.getProjectId());
            userExamPaperRecordQuery.setCustomerId(userToken.getCustomerId());
            if(UserExamPaperRecordCatalog.C5.getCode().equals(request.getCatalog())){
                List<String> catalogList = Arrays.asList(UserExamPaperRecordCatalog.C5.getCode(), UserExamPaperRecordCatalog.C6.getCode());
                userExamPaperRecordQuery.setCatalogs(catalogList);
            }else if(UserExamPaperRecordCatalog.C3.getCode().equals(request.getCatalog())){
                List<String> catalogList = Arrays.asList(UserExamPaperRecordCatalog.C3.getCode(), UserExamPaperRecordCatalog.C7.getCode());
                userExamPaperRecordQuery.setCatalogs(catalogList);
            }else{
                userExamPaperRecordQuery.setCatalog(request.getCatalog());
            }
            userExamPaperRecordQuery.setStatus(DataStatus.Y.getCode());
            Integer total = userExamPaperRecordService.count(userExamPaperRecordQuery);
            response.setTotal(total);
            response.setItems(new ArrayList<>());
            if(total == 0){
                return new Response<>(OK, SUCCESS,response);
            }
            Pager pager = new Pager(total, request.getPage(), request.getLimit());
            userExamPaperRecordQuery.setStart(pager.getOffset());
            userExamPaperRecordQuery.setLimit(pager.getLimit());

            List<UserExamPaperRecord> userExamPaperRecordList = userExamPaperRecordService.find(userExamPaperRecordQuery);

            if(!isEmpty(userExamPaperRecordList) && !userExamPaperRecordList.isEmpty()){
                //做题记录id集合
                List<Integer> userExamPaperRecordIds = userExamPaperRecordList.stream().map(UserExamPaperRecord::getId).collect(Collectors.toList());
                //查询做题记录详情
                UserExamPaperRecordDetailQuery userExamPaperRecordDetailQuery = new UserExamPaperRecordDetailQuery();
                userExamPaperRecordDetailQuery.setUserExamPaperRecordIds(userExamPaperRecordIds);
                userExamPaperRecordDetailQuery.setStatus(DataStatus.Y.getCode());
                List<UserExamPaperRecordDetail> userExamPaperRecordDetailList = userExamPaperRecordDetailService.findAll(userExamPaperRecordDetailQuery);

                Map<Integer, List<UserExamPaperRecordDetail>> userExamPaperRecordIdDetailsMap = new HashMap<>();
                Set<Integer> questionIdSet = new HashSet<>();
                Map<Integer, List<QuestionResponse>>userExamPaperRecordIdQuestionResponsesMap = new HashMap<>();
                for (UserExamPaperRecordDetail userExamPaperRecordDetail : userExamPaperRecordDetailList) {
                    if(userExamPaperRecordIdDetailsMap.containsKey(userExamPaperRecordDetail.getUserExamPaperRecordId())){
                        userExamPaperRecordIdDetailsMap.get(userExamPaperRecordDetail.getUserExamPaperRecordId()).add(userExamPaperRecordDetail);
                    }else{
                        List<UserExamPaperRecordDetail> userExamPaperRecordDetails = new ArrayList<>();
                        userExamPaperRecordDetails.add(userExamPaperRecordDetail);
                        userExamPaperRecordIdDetailsMap.put(userExamPaperRecordDetail.getUserExamPaperRecordId(),userExamPaperRecordDetails);
                    }
                    questionIdSet.add(userExamPaperRecordDetail.getQuestionId());
                }
                Map<Integer, QuestionResponse> questionResponseMap = new HashMap<>();
                if(!questionIdSet.isEmpty()){
                    QuestionQuery questionQuery = new QuestionQuery();
                    questionQuery.setStatus(DataStatus.Y.getCode());
                    questionQuery.setIds(new ArrayList<>(questionIdSet));
                    List<Question> questionList = questionService.findAll(questionQuery);
                    List<QuestionResponse> questionResponseList = new ArrayList<>();
                    for (Question question : questionList) {
                        QuestionResponse questionResponse = new QuestionResponse();
                        questionResponse.setId(question.getId());
                        questionResponse.setParentId(question.getParentId());
                        questionResponse.setType(question.getType());
                        questionResponseList.add(questionResponse);
                    }
                    //递归组装试卷题目关联关系的父子级结构
                    List<QuestionResponse> questionResponses = buildQuestionTree(questionResponseList, 0);
                    questionResponseMap = questionResponses.stream().collect(Collectors.toMap(QuestionResponse::getId,Function.identity()));
                }
                for (Integer userExamPaperRecordId : userExamPaperRecordIdDetailsMap.keySet()) {
                    List<UserExamPaperRecordDetail> userExamPaperRecordDetails = userExamPaperRecordIdDetailsMap.get(userExamPaperRecordId);
                    List<QuestionResponse> questionResponses = new ArrayList<>();
                    for (UserExamPaperRecordDetail userExamPaperRecordDetail : userExamPaperRecordDetails) {
                        if(questionResponseMap.containsKey(userExamPaperRecordDetail.getQuestionId())){
                            questionResponses.add(questionResponseMap.get(userExamPaperRecordDetail.getQuestionId()));
                        }
                    }
                    userExamPaperRecordIdQuestionResponsesMap.put(userExamPaperRecordId,questionResponses);
                }

                Map<String, String> examIdNameMap = new HashMap<>();
                Map<Integer, ExamPaper> examPaperMap = new HashMap<>();
                //题目标签编号
                Set<Integer> questionLabelIdSet = new HashSet<>();
                //按做题记录类型分组
                Map<String, List<UserExamPaperRecord>> catalogUserExamPaperRecordMap = userExamPaperRecordList.stream().collect(Collectors.groupingBy(UserExamPaperRecord::getCatalog));
                for (String catalog : catalogUserExamPaperRecordMap.keySet()) {
                    List<Integer> examPaperIds = catalogUserExamPaperRecordMap.get(catalog).stream().map(UserExamPaperRecord::getExamPaperId).collect(Collectors.toList());
                    //查询试卷信息 当是智能组卷、快速练习、课后练习-题目 是查询 tb_user_exam_paper 表 其他查询tbexam_paper表
                    if(QuestionArguments.USER_EXAM_PAPER_CATALOG_CODE_LIST.contains(catalog)){
                        UserExamPaperQuery userExamPaperQuery = new UserExamPaperQuery();
                        userExamPaperQuery.setIds(examPaperIds);
                        userExamPaperQuery.setStatus(DataStatus.Y.getCode());
                        List<UserExamPaper> userExamPaperList = userExamPaperService.findAll(userExamPaperQuery);
                        for (UserExamPaper userExamPaper : userExamPaperList) {
                            examIdNameMap.put(userExamPaper.getId() + catalog ,userExamPaper.getName() );
                        }
                    }else{
                        ExamPaperQuery examPaperQuery = new ExamPaperQuery();
                        examPaperQuery.setIds(examPaperIds);
                        List<ExamPaper> examPaperList = examPaperService.findAll(examPaperQuery);
                        for (ExamPaper examPaper : examPaperList) {
                            examIdNameMap.put(examPaper.getId() + catalog ,examPaper.getName() );
                            examPaperMap.put(examPaper.getId(),examPaper);
                            Integer questionLabelId = examPaper.getQuestionLabelId();
                            if (questionLabelId != null){
                                questionLabelIdSet.add(questionLabelId);
                            }
                        }
                    }
                }

                Map<Integer, QuestionLabel> questionLabelMap = new HashMap<>();
                if (!questionLabelIdSet.isEmpty()){
                    questionLabelMap = questionLabelService.findMapByIds(new ArrayList<>(questionLabelIdSet));
                }

                for (UserExamPaperRecord userExamPaperRecord : userExamPaperRecordList) {
                    UserExamPaperRecordResponse userExamPaperRecordResponse = new UserExamPaperRecordResponse();

                    userExamPaperRecordResponse.setId(userExamPaperRecord.getId());
                    userExamPaperRecordResponse.setExamPaperId(userExamPaperRecord.getExamPaperId());
                    userExamPaperRecordResponse.setProjectId(userExamPaperRecord.getProjectId());
                    userExamPaperRecordResponse.setCatalog(userExamPaperRecord.getCatalog());
                    //是否完成
                    userExamPaperRecordResponse.setOver(userExamPaperRecord.getOver());
                    //名称
                    if(examIdNameMap.containsKey(userExamPaperRecord.getExamPaperId() + userExamPaperRecord.getCatalog())){
                        userExamPaperRecordResponse.setExamPaperName(examIdNameMap.get(userExamPaperRecord.getExamPaperId() + userExamPaperRecord.getCatalog()));
                    }
                    //做题时间
                    userExamPaperRecordResponse.setModifyTime(DateUtil.format(userExamPaperRecord.getModifyTime(),DATETIME_FORMAT));
                    userExamPaperRecordResponse.setCreateTime(DateUtil.format(userExamPaperRecord.getCreateTime(),DATETIME_FORMAT));

                    int questionNumber = 0;
                    int playQuestionNumber = 0;
                    if(userExamPaperRecordIdDetailsMap.containsKey(userExamPaperRecord.getId()) && userExamPaperRecordIdQuestionResponsesMap.containsKey(userExamPaperRecord.getId())){
                        List<UserExamPaperRecordDetail> userExamPaperRecordDetails = userExamPaperRecordIdDetailsMap.get(userExamPaperRecord.getId());
                        List<QuestionResponse> questionResponses = userExamPaperRecordIdQuestionResponsesMap.get(userExamPaperRecord.getId());
                        List<Integer> questionIdList = questionResponses.stream().map(QuestionResponse::getId).collect(Collectors.toList());
                        for (UserExamPaperRecordDetail userExamPaperRecordDetail : userExamPaperRecordDetails) {
                            if(questionIdList.contains(userExamPaperRecordDetail.getQuestionId())){
                                questionNumber = questionNumber + 1;
                            }
                        }
                        Map<Integer, UserExamPaperRecordDetail> questionIdUserExamPaperRecordDetailMap = userExamPaperRecordDetails.stream().collect(Collectors.toMap(UserExamPaperRecordDetail::getQuestionId, Function.identity(),(t1,t2) ->t1));

                        for (QuestionResponse questionResponse : questionResponses) {
                            //材料题
                            if (QuestionBaseType.C6.getCode().equals(questionResponse.getType())) {
                                List<QuestionResponse> questionResponseChildren = questionResponse.getQuestionResponseChildren();
                                int userExamPaperRecordDetailNotAnswer = 0;
                                if(!isEmpty(questionResponseChildren)){
                                    for (QuestionResponse questionResponseChild : questionResponseChildren) {
                                        if (questionIdUserExamPaperRecordDetailMap.containsKey(questionResponseChild.getId())) {
                                            UserExamPaperRecordDetail userExamPaperRecordDetail = questionIdUserExamPaperRecordDetailMap.get(questionResponseChild.getId());
                                            if (!isEmpty(userExamPaperRecordDetail.getAnswer())) {
                                                userExamPaperRecordDetailNotAnswer = userExamPaperRecordDetailNotAnswer + 1;
                                            }
                                        }
                                    }
                                }
                                if(userExamPaperRecordDetailNotAnswer > 0 ){
                                    playQuestionNumber = playQuestionNumber + 1;
                                }
                            } else {
                                if (questionIdUserExamPaperRecordDetailMap.containsKey(questionResponse.getId())) {
                                    UserExamPaperRecordDetail userExamPaperRecordDetail = questionIdUserExamPaperRecordDetailMap.get(questionResponse.getId());
                                    if (!isEmpty(userExamPaperRecordDetail.getAnswer())) {
                                        playQuestionNumber = playQuestionNumber + 1;
                                    }
                                }

                            }
                        }
                    }
                    userExamPaperRecordResponse.setQuestionNumber(questionNumber);
                    userExamPaperRecordResponse.setPlayQuestionNumber(playQuestionNumber);
                    userExamPaperRecordResponse.setRightRate(userExamPaperRecord.getRightRate());
                    userExamPaperRecordResponse.setScoreRate(userExamPaperRecord.getScoreRate());
                    userExamPaperRecordResponse.setLastExerciseQuestion(userExamPaperRecord.getLastExerciseQuestion());
                    userExamPaperRecordResponse.setMode(userExamPaperRecord.getMode());
                    //模拟考试试卷在交卷状态下并且是需要阅卷的情况下返回试卷的阅卷状态
                    if(UserExamPaperRecordCatalog.C2.getCode().equals(userExamPaperRecord.getCatalog()) &&
                            UserExamPaperRecordOver.Y.getCode().equals(userExamPaperRecord.getOver()) ){
                        if(examPaperMap.containsKey(userExamPaperRecord.getExamPaperId())){
                            String correctSwitch = examPaperMap.get(userExamPaperRecord.getExamPaperId()).getCorrectSwitch();
                            if(ExamPaperCorrectSwitch.S0.getCode().equals(correctSwitch)){
                                userExamPaperRecordResponse.setReadOver(userExamPaperRecord.getReadOver());
                            }
                        }
                    }

                    //是否需要批阅 继续做题的时候使用
                    boolean needAutoRead = false;
                    if (UserExamPaperRecordCatalog.C2.getCode().equals(userExamPaperRecord.getCatalog())){
                        needAutoRead = true;
                    } else if (Arrays.asList(UserExamPaperRecordCatalog.C3.getCode(),UserExamPaperRecordCatalog.C4.getCode()).contains(userExamPaperRecord.getCatalog())){
                        if(examPaperMap.containsKey(userExamPaperRecord.getExamPaperId())){
                            ExamPaper examPaper = examPaperMap.get(userExamPaperRecord.getExamPaperId());
                            Integer questionLabelId = examPaper.getQuestionLabelId();
                            if (questionLabelMap.containsKey(questionLabelId)){
                                QuestionLabel questionLabel = questionLabelMap.get(questionLabelId);
                                needAutoRead = !isEmpty(questionLabel.getName()) && questionLabel.getName().contains(QuestionArguments.EXAM_ALLOW_SINGLE_QUESTION_LABEL_NAME);
                            }
                        }
                    }
                    userExamPaperRecordResponse.setNeedAutoRead(needAutoRead);
                    response.getItems().add(userExamPaperRecordResponse);
                }
            }
            return new Response<>(OK, SUCCESS,response);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }

    /**
     * 做题记录(分页)(分类练习:题目练习,星级练习)
     * @date 2025-06-17
     * <AUTHOR>
     */
    @RequestMapping(value = "/v1/user/exam/paper/record/my/type/query")
    @Token
    public Response<?> userExamPaperRecordDetailForType(@RequestBody MyQuestionRecordRequest request) {
        UserToken userToken = this.getUserToken();
        UserExamPaperRecordValidator validator = new UserExamPaperRecordValidator();
        try {
            if (!validator.onProjectId(request.getProjectId()).onCatalog(request.getCatalog()).result()) {
                return new Response<>(ERROR, validator.getErrorMessage());
            }
            //这里只会查询科目练习 历年真题 模拟考试下的 星级练习或单题练习; 别的功能没有这种区分
            if (!UserExamPaperRecordCatalog.C2.getCode().equals(request.getCatalog()) && !UserExamPaperRecordCatalog.C3.getCode().equals(request.getCatalog()) && !UserExamPaperRecordCatalog.C4.getCode().equals(request.getCatalog())){
                return new Response<>(ERROR, "做题记录分类参数有误");
            }

            NewPageResponse<QuestionUserExamPaperRecordResponse> response = new NewPageResponse<>();
            UserExamPaperRecordQuery userExamPaperRecordQuery = new UserExamPaperRecordQuery();
            userExamPaperRecordQuery.setProjectId(request.getProjectId());
            userExamPaperRecordQuery.setCustomerId(userToken.getCustomerId());
            userExamPaperRecordQuery.setCatalog(request.getCatalog());
            userExamPaperRecordQuery.setPlayTypes(Arrays.asList(UserExamPaperRecordPlayType.C1.getCode(), UserExamPaperRecordPlayType.C2.getCode()));
            userExamPaperRecordQuery.setStatus(DataStatus.Y.getCode());
            Integer total = userExamPaperRecordService.count(userExamPaperRecordQuery);
            response.setTotal(total);
            response.setItems(new ArrayList<>());
            if(total == 0){
                return new Response<>(OK, SUCCESS,response);
            }
            Pager pager = new Pager(total, request.getPage(), request.getLimit());
            userExamPaperRecordQuery.setStart(pager.getOffset());
            userExamPaperRecordQuery.setLimit(pager.getLimit());

            List<UserExamPaperRecord> userExamPaperRecordList = userExamPaperRecordService.find(userExamPaperRecordQuery);
            if(isEmpty(userExamPaperRecordList) || userExamPaperRecordList.isEmpty()){
                return new Response<>(OK, SUCCESS, response);
            }

            //查询做题记录详情 和 试题详情
            Map<Integer, List<UserExamPaperRecordDetail>> userExamPaperRecordDetailListMap = new HashMap<>();
            Map<Integer, Question> questionMap = new HashMap<>();
            List<Integer> projectItemIdList = null;
            //这里的做题记录ID只是那些单题练习的ID,其他的因为不需要进一步查询试题信息,所以不需要,可以大大减少查询做卷详情的数据量
            List<Integer> userExamPaperRecordIds = userExamPaperRecordList.stream().filter(a->UserExamPaperRecordPlayType.C1.getCode().equals(a.getPlayType())).map(UserExamPaperRecord::getId).collect(Collectors.toList());
            if (!userExamPaperRecordIds.isEmpty()){
                UserExamPaperRecordDetailQuery userExamPaperRecordDetailQuery = new UserExamPaperRecordDetailQuery();
                userExamPaperRecordDetailQuery.setUserExamPaperRecordIds(userExamPaperRecordIds);
                userExamPaperRecordDetailQuery.setStatus(DataStatus.Y.getCode());
                List<UserExamPaperRecordDetail> userExamPaperRecordDetailList = userExamPaperRecordDetailService.findAll(userExamPaperRecordDetailQuery);
                if (!isEmpty(userExamPaperRecordDetailList) && !userExamPaperRecordDetailList.isEmpty()){
                    userExamPaperRecordDetailListMap = userExamPaperRecordDetailList.stream().collect(Collectors.groupingBy(UserExamPaperRecordDetail::getUserExamPaperRecordId));
                    //这里获取到的questionId列表包括大题的和小题的,在搜集结果的时候只需要小题的,通过查询试题的时候做区分
                    List<Integer> questionIdList = userExamPaperRecordDetailList.stream().filter(a -> !isEmpty(a.getQuestionId())).map(UserExamPaperRecordDetail::getQuestionId).collect(Collectors.toList());
                    QuestionQuery questionQuery = new QuestionQuery();
                    questionQuery.setStatus(DataStatus.Y.getCode());
                    questionQuery.setIds(questionIdList);
                    questionQuery.setParentId(0);
                    List<Question> questionList = questionService.findAll(questionQuery);
                    if (!isEmpty(questionList) && !questionList.isEmpty()){
                        questionMap = questionList.stream().collect(Collectors.toMap(Question::getId, Function.identity()));
                        projectItemIdList = questionList.stream().filter(a -> !isEmpty(a.getProjectItemId())).map(Question::getProjectItemId).distinct().collect(Collectors.toList());
                    }

                }
            }

            //查询试题科目
            Map<Integer, ProjectItem> projectItemMap = new HashMap<>();
            if (projectItemIdList != null && !projectItemIdList.isEmpty()){
                ProjectItemQuery projectItemQuery = new ProjectItemQuery();
                projectItemQuery.setStatus(DataStatus.Y.getCode());
                projectItemQuery.setStage(PublicStage.Y.getCode());
                projectItemQuery.setIds(projectItemIdList);
                List<ProjectItem> projectItemList = projectItemService.findAll(projectItemQuery);
                if (!isEmpty(projectItemList) && !projectItemList.isEmpty()){
                    projectItemMap = projectItemList.stream().collect(Collectors.toMap(ProjectItem::getId, Function.identity()));
                }
            }

            //查询试卷详情(这个接口查询的做题记录所有试卷均来自用户卷)
            Map<Integer, UserExamPaper> userExamPaperMap = new HashMap<>();
            List<Integer> examPaperIdList = userExamPaperRecordList.stream().filter(a -> !isEmpty(a.getExamPaperId())).map(UserExamPaperRecord::getExamPaperId).distinct().collect(Collectors.toList());
            if (!examPaperIdList.isEmpty()){
                UserExamPaperQuery userExamPaperQuery = new UserExamPaperQuery();
                userExamPaperQuery.setStatus(DataStatus.Y.getCode());
                userExamPaperQuery.setIds(examPaperIdList);
                List<UserExamPaper> userExamPaperList = userExamPaperService.findAll(userExamPaperQuery);
                if (!isEmpty(userExamPaperList) && !userExamPaperList.isEmpty()){
                    userExamPaperMap = userExamPaperList.stream().collect(Collectors.toMap(UserExamPaper::getId, Function.identity()));
                }
            }

            for (UserExamPaperRecord userExamPaperRecord : userExamPaperRecordList) {
                QuestionUserExamPaperRecordResponse questionUserExamPaperRecordResponse = new QuestionUserExamPaperRecordResponse();
                //练习方式(0/null:试卷练习 1:单题练习 2:星级分类)
                questionUserExamPaperRecordResponse.setPlayType(userExamPaperRecord.getPlayType());
                //是否自动批阅: 目前这个列表只有单题目的需要自动批阅
                questionUserExamPaperRecordResponse.setNeedAutoRead(UserExamPaperRecordPlayType.C1.getCode().equals(userExamPaperRecord.getPlayType()));

                if (UserExamPaperRecordPlayType.C1.getCode().equals(userExamPaperRecord.getPlayType())){
                    //单题练习数据补充
                    if (userExamPaperRecordDetailListMap.containsKey(userExamPaperRecord.getId())){
                        List<UserExamPaperRecordDetail> userExamPaperRecordDetailList = userExamPaperRecordDetailListMap.get(userExamPaperRecord.getId());
                        //这里有可能取出多条记录,是因为材料题的小题会出现在详情中,只要找到一个大题就行了
                        for (UserExamPaperRecordDetail userExamPaperRecordDetail : userExamPaperRecordDetailList) {
                            //这条详情中的试题ID出现在了大题map中,就认为做的是这个大题
                            if (questionMap.containsKey(userExamPaperRecordDetail.getQuestionId())){
                                Question question = questionMap.get(userExamPaperRecordDetail.getQuestionId());
                                //试题ID
                                questionUserExamPaperRecordResponse.setQuestionId(question.getId());
                                //试题问题
                                questionUserExamPaperRecordResponse.setQuestion(question.getQuestion());
                                //材料
                                questionUserExamPaperRecordResponse.setQuestionOption(question.getOption());
                                //试题类型(区分材料题和简单题)
                                questionUserExamPaperRecordResponse.setQuestionType(question.getType());
                                //年份
                                questionUserExamPaperRecordResponse.setQuestionYear(question.getYear());
                                //题目分类
                                questionUserExamPaperRecordResponse.setQuestionCatalogName(QuestionCatalogV3.getName(question.getCatalog()));
                                //题目科目名称
                                if (projectItemMap.containsKey(question.getProjectItemId())){
                                    questionUserExamPaperRecordResponse.setQuestionProjectItemName(projectItemMap.get(question.getProjectItemId()).getName());
                                }
                                //难度
                                questionUserExamPaperRecordResponse.setQuestionDifficultyLevelName(QuestionDifficultyLevel.getName(question.getDifficultyLevel()));
                                BigDecimal questionScore = userExamPaperRecordDetail.getQuestionScore();
                                BigDecimal userScore = userExamPaperRecordDetail.getScore();
                                if (!isEmpty(questionScore)){
                                    //试题分值
                                    questionUserExamPaperRecordResponse.setQuestionScore(questionScore.stripTrailingZeros().toPlainString());
                                    //用户得分
                                    if (!isEmpty(userScore)){
                                        //用户得分理论上不会大于试题分值,实际走到这里可不一定
                                        userScore = userScore.compareTo(questionScore) > 0 ? questionScore : userScore;
                                        questionUserExamPaperRecordResponse.setQuestionUserScore(userScore.stripTrailingZeros().toPlainString());
                                    }
                                }
                                break;
                            }
                        }
                    }
                }

                //做卷记录ID
                questionUserExamPaperRecordResponse.setId(userExamPaperRecord.getId());
                //试卷ID
                questionUserExamPaperRecordResponse.setExamPaperId(userExamPaperRecord.getExamPaperId());
                //项目编号
                questionUserExamPaperRecordResponse.setProjectId(userExamPaperRecord.getProjectId());
                //做题记录分类
                questionUserExamPaperRecordResponse.setCatalog(userExamPaperRecord.getCatalog());
                //是否完成
                questionUserExamPaperRecordResponse.setOver(userExamPaperRecord.getOver());
                //试卷名称
                if (userExamPaperMap.containsKey(userExamPaperRecord.getExamPaperId())){
                    UserExamPaper userExamPaper = userExamPaperMap.get(userExamPaperRecord.getExamPaperId());
                    questionUserExamPaperRecordResponse.setExamPaperName(userExamPaper.getName());
                    //星级
                    String starLevel = userExamPaper.getStarLevel();
                    if (!isEmpty(starLevel)){
                        questionUserExamPaperRecordResponse.setStarLevel(starLevel);
                        questionUserExamPaperRecordResponse.setStarLevelName(QuestionStarLevel.getName(starLevel));
                        questionUserExamPaperRecordResponse.setStarLevelDescribe(QuestionArguments.getStarLevelDescribe(starLevel));
                    }
                }
                //做题时间
                questionUserExamPaperRecordResponse.setModifyTime(DateUtil.format(userExamPaperRecord.getModifyTime(),DATETIME_FORMAT));
                questionUserExamPaperRecordResponse.setCreateTime(DateUtil.format(userExamPaperRecord.getCreateTime(),DATETIME_FORMAT));
                //试题数量
                questionUserExamPaperRecordResponse.setQuestionNumber(userExamPaperRecord.getQuestionNumber());
                //做题数量
                questionUserExamPaperRecordResponse.setPlayQuestionNumber(userExamPaperRecord.getPlayQuestionNumber());
                //正确率
                questionUserExamPaperRecordResponse.setRightRate(userExamPaperRecord.getRightRate());
                //得分率
                questionUserExamPaperRecordResponse.setScoreRate(userExamPaperRecord.getScoreRate());
                //上次做题位置
                questionUserExamPaperRecordResponse.setLastExerciseQuestion(userExamPaperRecord.getLastExerciseQuestion());
                //做题模式 0:考试模式 1:练习模式 2:背题模式
                questionUserExamPaperRecordResponse.setMode(userExamPaperRecord.getMode());

                response.getItems().add(questionUserExamPaperRecordResponse);
            }
            return new Response<>(OK, SUCCESS,response);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }


    /**
     * 交卷后-做题解析
     *
     * <AUTHOR>
     */
    @RequestMapping(value = "/v1/user/exam/paper/record/analytic/query",method = RequestMethod.POST)
    @Token
    public Response<?> queryAnalytic(@RequestBody UserExamPaperRecordRequest request) {
        UserToken userToken = getUserToken();
        try {
            UserExamPaperRecordValidator validator = new UserExamPaperRecordValidator();
            if (!validator.onId(request.getId()).onAnalyticType(request.getAnalyticType()).result()) {
                return new Response<>(ERROR, validator.getErrorMessage());
            }
            ExamPaperQuestionAnalysisResponse response = new ExamPaperQuestionAnalysisResponse();
            //查询用户做题记录
            UserExamPaperRecord userExamPaperRecord = userExamPaperRecordService.findById(request.getId());
            if(userExamPaperRecord == null || DataStatus.N.getCode().equals(userExamPaperRecord.getStatus()) || UserExamPaperRecordOver.N.getCode().equals(userExamPaperRecord.getOver())){
                return new Response<>(ERROR, "做题记录被删除,无法查看");
            }
            //查询项目下的章节考点信息
            //查询章节考点
            Map<Integer, QuestionChapter> questionChapterMap = new HashMap<>();
            QuestionChapterQuery questionChapterQuery = new QuestionChapterQuery();
            questionChapterQuery.setStatus(DataStatus.Y.getCode());
            //项目编号和科目编号有可能为空
            questionChapterQuery.setProjectId(userExamPaperRecord.getProjectId());
            List<QuestionChapter> questionChapterList = questionChapterService.findAll(questionChapterQuery);
            if (!isEmpty(questionChapterList) && !questionChapterList.isEmpty()){
                questionChapterMap = questionChapterList.stream().collect(Collectors.toMap(QuestionChapter::getId, Function.identity()));
            }

            //试卷总分
            BigDecimal examTotalScore =BigDecimal.ZERO;
            //耗时(单位：分钟)
            response.setDuration(isEmpty(userExamPaperRecord.getDuration()) ? 0 : userExamPaperRecord.getDuration() / 60);

            //做题记录包含的题目id
            List<Integer> questionIdList = new ArrayList<>();
            Map<Integer, ExamPaperQuestion> examPaperQuestionMap = new HashMap<>();
            //查询试卷信息区分智能组卷、快速练习、课后练习题目和其他
            if(QuestionArguments.USER_EXAM_PAPER_CATALOG_CODE_LIST.contains(userExamPaperRecord.getCatalog())){
                UserExamPaper userExamPaper = userExamPaperService.findById(userExamPaperRecord.getExamPaperId());
                if(userExamPaper == null ){
                    return new Response<>(ERROR, "试卷被删除,无法查看");
                }
                response.setExamPaperId(userExamPaper.getId());
                response.setExamPaperName(userExamPaper.getName());
                //查询试卷绑定的题目
                UserExamPaperQuestionQuery userExamPaperQuestionQuery = new UserExamPaperQuestionQuery();
                userExamPaperQuestionQuery.setExamPaperId(userExamPaper.getId());
                userExamPaperQuestionQuery.setStatus(DataStatus.Y.getCode());
                List<UserExamPaperQuestion> userExamPaperQuestionList = userExamPaperQuestionService.findAll(userExamPaperQuestionQuery);
                //试卷绑定试题总分
                examTotalScore = userExamPaperQuestionList.stream().filter(userExamPaperQuestion -> !isEmpty(userExamPaperQuestion.getScore())).map(UserExamPaperQuestion::getScore).reduce(BigDecimal.ZERO, BigDecimal::add);

                questionIdList = userExamPaperQuestionList.stream().map(UserExamPaperQuestion::getQuestionId).collect(Collectors.toList());
            }else{
                ExamPaper examPaper = examPaperService.findById(userExamPaperRecord.getExamPaperId());
                if(examPaper == null ){
                    return new Response<>(ERROR, "试卷被删除,无法查看");
                }
                if(!isEmpty(examPaper.getProjectItemId())){
                    ProjectItem projectItem = projectItemService.findById(examPaper.getProjectItemId());
                    response.setProjectItemName(!isEmpty(projectItem) ? projectItem.getName() : null);
                }
                //书卷id
                response.setExamPaperId(examPaper.getId());
                //试卷名称
                response.setExamPaperName(examPaper.getName());
                //是否需要批阅
                response.setCorrectSwitch(examPaper.getCorrectSwitch());
                //查询试卷绑定的题目
                ExamPaperQuestionQuery examPaperQuestionQuery = new ExamPaperQuestionQuery();
                examPaperQuestionQuery.setExamPaperId(examPaper.getId());
                examPaperQuestionQuery.setStatus(DataStatus.Y.getCode());
                List<ExamPaperQuestion> examPaperQuestionList = examPaperQuestionService.findAll(examPaperQuestionQuery);
                //试卷绑定试题总分
                examTotalScore = examPaperQuestionList.stream()
                        .filter(examPaperQuestion -> examPaperQuestion.getParentId() == 0)
                        .filter(examPaperQuestion -> !isEmpty(examPaperQuestion.getScore()))
                        .map(ExamPaperQuestion::getScore)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);

                questionIdList = examPaperQuestionList.stream().map(ExamPaperQuestion::getQuestionId).collect(Collectors.toList());
                examPaperQuestionMap = examPaperQuestionList.stream().collect(Collectors.toMap(ExamPaperQuestion::getQuestionId, Function.identity()));
            }
            //examTotalScore;
            String examTotalScoreString = examTotalScore.setScale(1, BigDecimal.ROUND_HALF_UP).stripTrailingZeros().toPlainString();
            response.setTotalScore(examTotalScoreString);
            if(questionIdList.isEmpty()){
                return new Response<>(ERROR,"未查询到题目信息,无法查看");
            }
            QuestionQuery questionQuery = new QuestionQuery();
            questionQuery.setStatus(DataStatus.Y.getCode());
            questionQuery.setIds(questionIdList);
            //查询题目信息
            List<Question> questionList = questionService.findAll(questionQuery);

            //如果做题记录为智能组卷或快速联系时、找出题目中的材料题
            if(UserExamPaperRecordCatalog.C0.getCode().equals( userExamPaperRecord.getCatalog()) ||
                    UserExamPaperRecordCatalog.C1.getCode().equals( userExamPaperRecord.getCatalog()) ||
                    UserExamPaperRecordCatalog.C6.getCode().equals( userExamPaperRecord.getCatalog()) ||
                    UserExamPaperRecordCatalog.C7.getCode().equals( userExamPaperRecord.getCatalog())
            ) {
                List<Integer> questionC6IdList = questionList.stream().filter(question -> QuestionBaseType.C6.getCode().equals(question.getType())).map(Question::getId).collect(Collectors.toList());
                if (!questionC6IdList.isEmpty()) {
                    QuestionQuery questionChildQuery = new QuestionQuery();
                    questionChildQuery.setStatus(DataStatus.Y.getCode());
                    questionChildQuery.setParentIds(questionC6IdList);
                    List<Question> questionChildList = questionService.findAll(questionChildQuery);
                    if (!isEmpty(questionChildList) && !questionChildList.isEmpty()) {
                        questionList.addAll(questionChildList);
                    }
                }
            }
            if(questionList.isEmpty()){
                return new Response<>(ERROR,"未查询到题目信息,无法查看");
            }

            List<QuestionResponse> questionResponseList = new ArrayList<>();
            for (Question question : questionList) {
                QuestionResponse questionResponse = new QuestionResponse();
                questionResponse.setId(question.getId());
                questionResponse.setParentId(question.getParentId());
                questionResponse.setType(question.getType());
                questionResponseList.add(questionResponse);
            }
            //递归组装试卷题目关联关系的父子级结构
            List<QuestionResponse> questionResponses = buildQuestionTree(questionResponseList, 0);

            Map<Integer, QuestionResponse> questionResponseMap = questionResponses.stream().collect(Collectors.toMap(QuestionResponse::getId, Function.identity()));

            // 查询收藏的题
            UserExamPaperQuestionFavoriteQuery favoriteQuery = new UserExamPaperQuestionFavoriteQuery();
            favoriteQuery.setProjectId(userExamPaperRecord.getProjectId());
            favoriteQuery.setCustomerId(userToken.getCustomerId());
            favoriteQuery.setStatus(DataStatus.Y.getCode());

            List<UserExamPaperQuestionFavorite> questionFavoriteList = userExamPaperQuestionFavoriteService.findAll(favoriteQuery);
            Map<Integer, UserExamPaperQuestionFavorite> questionFavoriteMap = questionFavoriteList.stream().collect(Collectors.toMap(UserExamPaperQuestionFavorite::getQuestionId, Function.identity(), (a, b) ->a));
            //查询笔记
            UserExamPaperQuestionNoteQuery userExamPaperQuestionNoteQuery = new UserExamPaperQuestionNoteQuery();
            userExamPaperQuestionNoteQuery.setProjectId(userExamPaperRecord.getProjectId());
            userExamPaperQuestionNoteQuery.setCustomerId(userToken.getCustomerId());
            userExamPaperQuestionNoteQuery.setQuestionIds(questionIdList);
            List<UserExamPaperQuestionNote> userExamPaperQuestionNotes = userExamPaperQuestionNoteService.findAll(userExamPaperQuestionNoteQuery);
            LinkedHashMap<Integer, UserExamPaperQuestionNote> examNoteMap = userExamPaperQuestionNotes.stream().collect(Collectors.toMap(UserExamPaperQuestionNote::getQuestionId, Function.identity(), (a, b) ->a,LinkedHashMap::new));
            //查询题目详情
            UserExamPaperRecordDetailQuery userExamPaperRecordDetailQuery = new UserExamPaperRecordDetailQuery();
            userExamPaperRecordDetailQuery.setUserExamPaperRecordId(userExamPaperRecord.getId());
            userExamPaperRecordDetailQuery.setStatus(DataStatus.Y.getCode());
            List<UserExamPaperRecordDetail> userExamPaperRecordDetailList = userExamPaperRecordDetailService.findAll(userExamPaperRecordDetailQuery);
            BigDecimal userScore = BigDecimal.ZERO;

            int questionNumber = 0;
            int playQuestionNumber = 0;

            //题目数量
            if(!isEmpty(userExamPaperRecordDetailList) && !userExamPaperRecordDetailList.isEmpty()){
                for (UserExamPaperRecordDetail userExamPaperRecordDetail : userExamPaperRecordDetailList) {
                    if(questionResponseMap.containsKey(userExamPaperRecordDetail.getQuestionId())){
                        questionNumber = questionNumber + 1;
                    }
                }
                //用户最终得分
                userScore = userExamPaperRecordDetailList.stream().filter(detail -> !isEmpty(detail.getScore())).map(UserExamPaperRecordDetail::getScore).reduce(BigDecimal.ZERO, BigDecimal::add);
                String userScoreString = userScore.setScale(1, BigDecimal.ROUND_HALF_UP).stripTrailingZeros().toPlainString();
                //试卷得分
                response.setScore(userScoreString);

                Map<Integer, UserExamPaperRecordDetail> questionIdUserExamPaperRecordDetailMap = userExamPaperRecordDetailList.stream().collect(Collectors.toMap(UserExamPaperRecordDetail::getQuestionId, Function.identity(),(t1,t2) -> t1));

                for (QuestionResponse questionResponse : questionResponses) {
                    //材料题
                    if (QuestionBaseType.C6.getCode().equals(questionResponse.getType())) {
                        List<QuestionResponse> questionResponseChildren = questionResponse.getQuestionResponseChildren();
                        int userExamPaperRecordDetailAnswerRightNum = 0;
                        if(!isEmpty(questionResponseChildren)){
                            for (QuestionResponse questionResponseChild : questionResponseChildren) {
                                if (questionIdUserExamPaperRecordDetailMap.containsKey(questionResponseChild.getId())) {
                                    UserExamPaperRecordDetail userExamPaperRecordDetail = questionIdUserExamPaperRecordDetailMap.get(questionResponseChild.getId());
                                    if(UserExamPaperRecordDetailQuestionResult.Y.getCode().equals(userExamPaperRecordDetail.getQuestionResult())){
                                        userExamPaperRecordDetailAnswerRightNum = userExamPaperRecordDetailAnswerRightNum + 1;
                                    }
                                }
                            }
                        }
                        if(userExamPaperRecordDetailAnswerRightNum == questionResponseChildren.size() ){
                            playQuestionNumber = playQuestionNumber + 1;
                        }
                    } else {
                        if (questionIdUserExamPaperRecordDetailMap.containsKey(questionResponse.getId())) {
                            UserExamPaperRecordDetail userExamPaperRecordDetail = questionIdUserExamPaperRecordDetailMap.get(questionResponse.getId());
                            if(UserExamPaperRecordDetailQuestionResult.Y.getCode().equals(userExamPaperRecordDetail.getQuestionResult())){
                                playQuestionNumber = playQuestionNumber + 1;
                            }
                        }

                    }
                }
            }

            response.setQuestionNumber(questionNumber);
            //已做题目数量
            response.setPlayQuestionNumber(playQuestionNumber);

            //做题详情Map,题目id为key 区分 全部解析 和 错题解析两种情况
            Map<Integer, UserExamPaperRecordDetail> userExamRecordDetailMap = new HashMap<>();
            if (!isEmpty(userExamPaperRecordDetailList) && !userExamPaperRecordDetailList.isEmpty()) {
                if(ExamAnalysisStatus.N.getCode().equals(request.getAnalyticType())){
                    //只查错题
                    userExamRecordDetailMap = userExamPaperRecordDetailList.stream()
                            .filter(item -> !isEmpty(item.getQuestionResult()) && UserExamPaperRecordDetailQuestionResult.N.getCode().equals(item.getQuestionResult())).collect(Collectors.toMap(UserExamPaperRecordDetail::getQuestionId, Function.identity(), (a, b) -> b));
                    //无法判断对错
                    Map<Integer, UserExamPaperRecordDetail> userExamRecordDetailNotDetermineMap = userExamPaperRecordDetailList.stream()
                            .filter(item -> isEmpty(item.getQuestionResult())).collect(Collectors.toMap(UserExamPaperRecordDetail::getQuestionId, Function.identity(), (a, b) -> b));

                    userExamRecordDetailMap.putAll(userExamRecordDetailNotDetermineMap);

                    Set<Integer> questionWrongSet = userExamRecordDetailMap.keySet();
                    questionList  = questionList.stream().filter(e -> questionWrongSet.stream().anyMatch(id -> e.getId().equals(id))).collect(Collectors.toList());
                }else{
                    //查全部
                    userExamRecordDetailMap = userExamPaperRecordDetailList.stream().collect(Collectors.toMap(UserExamPaperRecordDetail::getQuestionId, Function.identity(), (a, b) -> b));
                }
            }

            //获取本套试卷包含的题型
            Map<Integer, List<Question>> questionTemplateItemIdMap = questionList.stream().filter(question -> question.getParentId() == 0).collect(Collectors.groupingBy(Question::getQuestionTemplateItemId));

            //分离出试题集合的题型模板条目id
            Set<Integer> questionTemplateItemIdSet = questionList.stream().map(Question::getQuestionTemplateItemId).collect(Collectors.toSet());

            //查询题型模板条目信息
            Map<Integer, QuestionTemplateItem> questionTemplateItemNameMap = new HashMap<>();
            if(!questionTemplateItemIdSet.isEmpty()){
                List<QuestionTemplateItem> questionTemplateItemList = questionTemplateItemService.findByIds(new ArrayList<>(questionTemplateItemIdSet));
                questionTemplateItemNameMap = questionTemplateItemList.stream().collect(Collectors.toMap(QuestionTemplateItem::getId,Function.identity()));
            }
            //获取本套试卷包含的题型
            List<ExamPaperTypeResponse> typeResponses = new ArrayList<>();
            for (Integer questionTemplateItemId : questionTemplateItemIdMap.keySet()) {
                ExamPaperTypeResponse examPaperTypeResponse = new ExamPaperTypeResponse();
                examPaperTypeResponse.setQuestionTemplateItemId(questionTemplateItemId);
                if(questionTemplateItemNameMap.containsKey(questionTemplateItemId)){
                    QuestionTemplateItem questionTemplateItem = questionTemplateItemNameMap.get(questionTemplateItemId);
                    examPaperTypeResponse.setType(questionTemplateItem.getQuestionType());
                    examPaperTypeResponse.setTypeName(questionTemplateItem.getName());
                    examPaperTypeResponse.setSort(questionTemplateItem.getSort());
                    Set<Integer> questionIdSet = questionTemplateItemIdMap.get(questionTemplateItemId).stream().map(Question::getId).collect(Collectors.toSet());
                    examPaperTypeResponse.setQuestionIds(new ArrayList<>(questionIdSet));
                }
                typeResponses.add(examPaperTypeResponse);
            }

            //按题型模板配置的sort字段降序排序
            typeResponses = typeResponses.stream().sorted(Comparator.comparing(ExamPaperTypeResponse::getSort)).collect(Collectors.toList());


            List<QuestionAnalyticResponse> questionAnalyticResponseList = new ArrayList<>();
            for (Question question : questionList) {
                questionAnalyticResponseList.add(beanToResponse(null,question,questionTemplateItemNameMap,questionFavoriteMap,
                        examPaperQuestionMap,userExamRecordDetailMap,userExamPaperRecord
                        ,questionChapterMap,questionChapterList,examNoteMap,true,true,null));
            }

            //递归组装试卷题目关联关系的父子级结构
            List<QuestionAnalyticResponse> questionAnalyticResponsesTree = new ArrayList<>();
            if(UserExamPaperRecordCatalog.C0.getCode().equals( userExamPaperRecord.getCatalog())
                    || UserExamPaperRecordCatalog.C1.getCode().equals( userExamPaperRecord.getCatalog())
                    || UserExamPaperRecordCatalog.C6.getCode().equals( userExamPaperRecord.getCatalog())
                    || UserExamPaperRecordCatalog.C7.getCode().equals( userExamPaperRecord.getCatalog())
            ) {
                questionAnalyticResponsesTree = buildQuestionTree(questionAnalyticResponseList, 0,1);
            }else{
                questionAnalyticResponsesTree = buildQuestionTree(questionAnalyticResponseList, 0,0);
            }
            List<QuestionAnalyticResponse> questionAnalyticResponses = new ArrayList<>();
            for (QuestionAnalyticResponse questionAnalyticResponse : questionAnalyticResponsesTree) {
                if(!QuestionBaseType.C6.getCode().equals(questionAnalyticResponse.getType())){
                    questionAnalyticResponses.add(questionAnalyticResponse);
                }else if(!isEmpty(questionAnalyticResponse.getQuestionResponseChildren()) && !questionAnalyticResponse.getQuestionResponseChildren().isEmpty()){
                    questionAnalyticResponses.add(questionAnalyticResponse);
                }
            }
            Map<Integer, QuestionAnalyticResponse> analyticResponseMap = questionAnalyticResponsesTree.stream().collect(Collectors.toMap(QuestionAnalyticResponse::getId, Function.identity()));
            //题目类型
            for (ExamPaperTypeResponse examPaperType : typeResponses) {
                //每个题目类型下所有的题的总分
                BigDecimal totalScore = BigDecimal.ZERO;
                //每个题目类型下所有题的用户总得分
                BigDecimal totalUserScore = BigDecimal.ZERO;
                //只有答题的题目ID，不包含子题
                List<Integer> questionIds = examPaperType.getQuestionIds();
                for (Integer questionId : questionIds) {
                    if(analyticResponseMap.containsKey(questionId)){
                        QuestionAnalyticResponse questionAnalyticResponse = analyticResponseMap.get(questionId);
                        //累加，总分 和得分
                        if(QuestionBaseType.C6.getCode().equals(questionAnalyticResponse.getType())){
                            List<QuestionAnalyticResponse> questionResponseChildren = questionAnalyticResponse.getQuestionResponseChildren();
                            for (QuestionAnalyticResponse questionResponseChild : questionResponseChildren) {
                                totalScore = totalScore.add(isEmpty(questionResponseChild.getScore()) ? BigDecimal.ZERO : questionResponseChild.getScore());
                                totalUserScore = totalUserScore.add(isEmpty(questionResponseChild.getUserScore()) ? BigDecimal.ZERO : questionResponseChild.getUserScore());
                            }
                        }else{
                            totalScore = totalScore.add(isEmpty(questionAnalyticResponse.getScore()) ? BigDecimal.ZERO :questionAnalyticResponse.getScore());
                            totalUserScore = totalUserScore.add(isEmpty(questionAnalyticResponse.getUserScore()) ? BigDecimal.ZERO : questionAnalyticResponse.getUserScore());
                        }
                    }
                }
                examPaperType.setTotalScore(totalScore);
                examPaperType.setTotalUserScore(totalUserScore);
            }
            response.setExamPaperQuestionTypeList(typeResponses);

            //题目数组
            response.setQuestionList(questionAnalyticResponses);
            return new Response<>(response);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }

    /**
     * 交卷后-做题解析(V2)
     * 从V1复制过来的,仅修改了试卷来源问题,兼容了单题练习和星级组卷功能
     *
     * <AUTHOR>
     */
    @RequestMapping(value = "/v2/user/exam/paper/record/analytic/query",method = RequestMethod.POST)
    @Token
    public Response<?> queryAnalyticV2(@RequestBody UserExamPaperRecordRequest request) {
        UserToken userToken = getUserToken();
        try {
            UserExamPaperRecordValidator validator = new UserExamPaperRecordValidator();
            if (!validator.onId(request.getId()).onAnalyticType(request.getAnalyticType()).result()) {
                return new Response<>(ERROR, validator.getErrorMessage());
            }
            ExamPaperQuestionAnalysisResponse response = new ExamPaperQuestionAnalysisResponse();
            //查询用户做题记录
            UserExamPaperRecord userExamPaperRecord = userExamPaperRecordService.findById(request.getId());
            if(userExamPaperRecord == null || DataStatus.N.getCode().equals(userExamPaperRecord.getStatus()) || UserExamPaperRecordOver.N.getCode().equals(userExamPaperRecord.getOver())){
                return new Response<>(ERROR, "做题记录被删除,无法查看");
            }
            //查询项目下的章节考点信息
            //查询章节考点
            Map<Integer, QuestionChapter> questionChapterMap = new HashMap<>();
            QuestionChapterQuery questionChapterQuery = new QuestionChapterQuery();
            questionChapterQuery.setStatus(DataStatus.Y.getCode());
            //项目编号和科目编号有可能为空
            questionChapterQuery.setProjectId(userExamPaperRecord.getProjectId());
            List<QuestionChapter> questionChapterList = questionChapterService.findAll(questionChapterQuery);
            if (!isEmpty(questionChapterList) && !questionChapterList.isEmpty()){
                questionChapterMap = questionChapterList.stream().collect(Collectors.toMap(QuestionChapter::getId, Function.identity()));
            }

            //试卷总分
            BigDecimal examTotalScore =BigDecimal.ZERO;
            //耗时(单位：分钟)
            response.setDuration(isEmpty(userExamPaperRecord.getDuration()) ? 0 : userExamPaperRecord.getDuration() / 60);

            //做题记录包含的题目id
            List<Integer> questionIdList = new ArrayList<>();
            Map<Integer, ExamPaperQuestion> examPaperQuestionMap = new HashMap<>();

            //由于现在单题练习和星级组卷在交卷的时候存入的catalog是2,3,4(系统卷)类型,导致本该查询用户卷却去查了系统卷这样的错误,这里专门定义一个标识试卷类型的变量,true:系统卷
            boolean sysUserExamPaper = true;
            String catalog = userExamPaperRecord.getCatalog();
            String playType = userExamPaperRecord.getPlayType();
            //用户卷: 用户卷  || 单题练习 || 星级练习
            if (QuestionArguments.USER_EXAM_PAPER_CATALOG_CODE_LIST.contains(catalog)){
                sysUserExamPaper = false;
            }
            if (UserExamPaperRecordPlayType.C1.getCode().equals(playType) || UserExamPaperRecordPlayType.C2.getCode().equals(playType)){
                sysUserExamPaper = false;
            }

            if(!sysUserExamPaper){
                UserExamPaper userExamPaper = userExamPaperService.findById(userExamPaperRecord.getExamPaperId());
                if(userExamPaper == null ){
                    return new Response<>(ERROR, "试卷被删除,无法查看");
                }
                response.setExamPaperId(userExamPaper.getId());
                response.setExamPaperName(userExamPaper.getName());
                //查询试卷绑定的题目
                UserExamPaperQuestionQuery userExamPaperQuestionQuery = new UserExamPaperQuestionQuery();
                userExamPaperQuestionQuery.setExamPaperId(userExamPaper.getId());
                userExamPaperQuestionQuery.setStatus(DataStatus.Y.getCode());
                List<UserExamPaperQuestion> userExamPaperQuestionList = userExamPaperQuestionService.findAll(userExamPaperQuestionQuery);
                //试卷绑定试题总分
                examTotalScore = userExamPaperQuestionList.stream().filter(userExamPaperQuestion -> !isEmpty(userExamPaperQuestion.getScore())).map(UserExamPaperQuestion::getScore).reduce(BigDecimal.ZERO, BigDecimal::add);

                questionIdList = userExamPaperQuestionList.stream().map(UserExamPaperQuestion::getQuestionId).collect(Collectors.toList());
            }else{
                ExamPaper examPaper = examPaperService.findById(userExamPaperRecord.getExamPaperId());
                if(examPaper == null ){
                    return new Response<>(ERROR, "试卷被删除,无法查看");
                }
                if(!isEmpty(examPaper.getProjectItemId())){
                    ProjectItem projectItem = projectItemService.findById(examPaper.getProjectItemId());
                    response.setProjectItemName(!isEmpty(projectItem) ? projectItem.getName() : null);
                }
                //书卷id
                response.setExamPaperId(examPaper.getId());
                //试卷名称
                response.setExamPaperName(examPaper.getName());
                //是否需要批阅
                response.setCorrectSwitch(examPaper.getCorrectSwitch());
                //查询试卷绑定的题目
                ExamPaperQuestionQuery examPaperQuestionQuery = new ExamPaperQuestionQuery();
                examPaperQuestionQuery.setExamPaperId(examPaper.getId());
                examPaperQuestionQuery.setStatus(DataStatus.Y.getCode());
                List<ExamPaperQuestion> examPaperQuestionList = examPaperQuestionService.findAll(examPaperQuestionQuery);
                //试卷绑定试题总分
                examTotalScore = examPaperQuestionList.stream()
                        .filter(examPaperQuestion -> examPaperQuestion.getParentId() == 0)
                        .filter(examPaperQuestion -> !isEmpty(examPaperQuestion.getScore()))
                        .map(ExamPaperQuestion::getScore)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);

                questionIdList = examPaperQuestionList.stream().map(ExamPaperQuestion::getQuestionId).collect(Collectors.toList());
                examPaperQuestionMap = examPaperQuestionList.stream().collect(Collectors.toMap(ExamPaperQuestion::getQuestionId, Function.identity()));
            }
            //examTotalScore;
            String examTotalScoreString = examTotalScore.setScale(1, BigDecimal.ROUND_HALF_UP).stripTrailingZeros().toPlainString();
            response.setTotalScore(examTotalScoreString);
            if(questionIdList.isEmpty()){
                return new Response<>(ERROR,"未查询到题目信息,无法查看");
            }
            QuestionQuery questionQuery = new QuestionQuery();
            questionQuery.setStatus(DataStatus.Y.getCode());
            questionQuery.setIds(questionIdList);
            //查询题目信息
            List<Question> questionList = questionService.findAll(questionQuery);

            //如果做题记录为智能组卷或快速联系时、找出题目中的材料题
            if(!sysUserExamPaper) {
                List<Integer> questionC6IdList = questionList.stream().filter(question -> QuestionBaseType.C6.getCode().equals(question.getType())).map(Question::getId).collect(Collectors.toList());
                if (!questionC6IdList.isEmpty()) {
                    QuestionQuery questionChildQuery = new QuestionQuery();
                    questionChildQuery.setStatus(DataStatus.Y.getCode());
                    questionChildQuery.setParentIds(questionC6IdList);
                    List<Question> questionChildList = questionService.findAll(questionChildQuery);
                    if (!isEmpty(questionChildList) && !questionChildList.isEmpty()) {
                        questionList.addAll(questionChildList);
                    }
                }
            }
            if(questionList.isEmpty()){
                return new Response<>(ERROR,"未查询到题目信息,无法查看");
            }

            List<QuestionResponse> questionResponseList = new ArrayList<>();
            for (Question question : questionList) {
                QuestionResponse questionResponse = new QuestionResponse();
                questionResponse.setId(question.getId());
                questionResponse.setParentId(question.getParentId());
                questionResponse.setType(question.getType());
                questionResponseList.add(questionResponse);
            }
            //递归组装试卷题目关联关系的父子级结构
            List<QuestionResponse> questionResponses = buildQuestionTree(questionResponseList, 0);

            Map<Integer, QuestionResponse> questionResponseMap = questionResponses.stream().collect(Collectors.toMap(QuestionResponse::getId, Function.identity()));

            // 查询收藏的题
            UserExamPaperQuestionFavoriteQuery favoriteQuery = new UserExamPaperQuestionFavoriteQuery();
            favoriteQuery.setProjectId(userExamPaperRecord.getProjectId());
            favoriteQuery.setCustomerId(userToken.getCustomerId());
            favoriteQuery.setStatus(DataStatus.Y.getCode());

            List<UserExamPaperQuestionFavorite> questionFavoriteList = userExamPaperQuestionFavoriteService.findAll(favoriteQuery);
            Map<Integer, UserExamPaperQuestionFavorite> questionFavoriteMap = questionFavoriteList.stream().collect(Collectors.toMap(UserExamPaperQuestionFavorite::getQuestionId, Function.identity(), (a, b) ->a));
            //查询笔记
            UserExamPaperQuestionNoteQuery userExamPaperQuestionNoteQuery = new UserExamPaperQuestionNoteQuery();
            userExamPaperQuestionNoteQuery.setProjectId(userExamPaperRecord.getProjectId());
            userExamPaperQuestionNoteQuery.setCustomerId(userToken.getCustomerId());
            userExamPaperQuestionNoteQuery.setQuestionIds(questionIdList);
            List<UserExamPaperQuestionNote> userExamPaperQuestionNotes = userExamPaperQuestionNoteService.findAll(userExamPaperQuestionNoteQuery);
            LinkedHashMap<Integer, UserExamPaperQuestionNote> examNoteMap = userExamPaperQuestionNotes.stream().collect(Collectors.toMap(UserExamPaperQuestionNote::getQuestionId, Function.identity(), (a, b) ->a,LinkedHashMap::new));
            //查询题目详情
            UserExamPaperRecordDetailQuery userExamPaperRecordDetailQuery = new UserExamPaperRecordDetailQuery();
            userExamPaperRecordDetailQuery.setUserExamPaperRecordId(userExamPaperRecord.getId());
            userExamPaperRecordDetailQuery.setStatus(DataStatus.Y.getCode());
            List<UserExamPaperRecordDetail> userExamPaperRecordDetailList = userExamPaperRecordDetailService.findAll(userExamPaperRecordDetailQuery);
            BigDecimal userScore = BigDecimal.ZERO;

            int questionNumber = 0;
            int playQuestionNumber = 0;

            //题目数量
            if(!isEmpty(userExamPaperRecordDetailList) && !userExamPaperRecordDetailList.isEmpty()){
                for (UserExamPaperRecordDetail userExamPaperRecordDetail : userExamPaperRecordDetailList) {
                    if(questionResponseMap.containsKey(userExamPaperRecordDetail.getQuestionId())){
                        questionNumber = questionNumber + 1;
                    }
                }
                //用户最终得分
                userScore = userExamPaperRecordDetailList.stream().filter(detail -> !isEmpty(detail.getScore())).map(UserExamPaperRecordDetail::getScore).reduce(BigDecimal.ZERO, BigDecimal::add);
                String userScoreString = userScore.setScale(1, BigDecimal.ROUND_HALF_UP).stripTrailingZeros().toPlainString();
                //试卷得分
                response.setScore(userScoreString);

                Map<Integer, UserExamPaperRecordDetail> questionIdUserExamPaperRecordDetailMap = userExamPaperRecordDetailList.stream().collect(Collectors.toMap(UserExamPaperRecordDetail::getQuestionId, Function.identity(),(t1,t2) -> t1));

                for (QuestionResponse questionResponse : questionResponses) {
                    //材料题
                    if (QuestionBaseType.C6.getCode().equals(questionResponse.getType())) {
                        List<QuestionResponse> questionResponseChildren = questionResponse.getQuestionResponseChildren();
                        int userExamPaperRecordDetailAnswerRightNum = 0;
                        if(!isEmpty(questionResponseChildren)){
                            for (QuestionResponse questionResponseChild : questionResponseChildren) {
                                if (questionIdUserExamPaperRecordDetailMap.containsKey(questionResponseChild.getId())) {
                                    UserExamPaperRecordDetail userExamPaperRecordDetail = questionIdUserExamPaperRecordDetailMap.get(questionResponseChild.getId());
                                    if(UserExamPaperRecordDetailQuestionResult.Y.getCode().equals(userExamPaperRecordDetail.getQuestionResult())){
                                        userExamPaperRecordDetailAnswerRightNum = userExamPaperRecordDetailAnswerRightNum + 1;
                                    }
                                }
                            }
                        }
                        if(userExamPaperRecordDetailAnswerRightNum == questionResponseChildren.size() ){
                            playQuestionNumber = playQuestionNumber + 1;
                        }
                    } else {
                        if (questionIdUserExamPaperRecordDetailMap.containsKey(questionResponse.getId())) {
                            UserExamPaperRecordDetail userExamPaperRecordDetail = questionIdUserExamPaperRecordDetailMap.get(questionResponse.getId());
                            if(UserExamPaperRecordDetailQuestionResult.Y.getCode().equals(userExamPaperRecordDetail.getQuestionResult())){
                                playQuestionNumber = playQuestionNumber + 1;
                            }
                        }

                    }
                }
            }

            response.setQuestionNumber(questionNumber);
            //已做题目数量
            response.setPlayQuestionNumber(playQuestionNumber);

            //做题详情Map,题目id为key 区分 全部解析 和 错题解析两种情况
            Map<Integer, UserExamPaperRecordDetail> userExamRecordDetailMap = new HashMap<>();
            if (!isEmpty(userExamPaperRecordDetailList) && !userExamPaperRecordDetailList.isEmpty()) {
                if(ExamAnalysisStatus.N.getCode().equals(request.getAnalyticType())){
                    //只查错题
                    userExamRecordDetailMap = userExamPaperRecordDetailList.stream()
                            .filter(item -> !isEmpty(item.getQuestionResult()) && UserExamPaperRecordDetailQuestionResult.N.getCode().equals(item.getQuestionResult())).collect(Collectors.toMap(UserExamPaperRecordDetail::getQuestionId, Function.identity(), (a, b) -> b));
                    //无法判断对错
                    Map<Integer, UserExamPaperRecordDetail> userExamRecordDetailNotDetermineMap = userExamPaperRecordDetailList.stream()
                            .filter(item -> isEmpty(item.getQuestionResult())).collect(Collectors.toMap(UserExamPaperRecordDetail::getQuestionId, Function.identity(), (a, b) -> b));

                    userExamRecordDetailMap.putAll(userExamRecordDetailNotDetermineMap);

                    Set<Integer> questionWrongSet = userExamRecordDetailMap.keySet();
                    questionList  = questionList.stream().filter(e -> questionWrongSet.stream().anyMatch(id -> e.getId().equals(id))).collect(Collectors.toList());
                }else{
                    //查全部
                    userExamRecordDetailMap = userExamPaperRecordDetailList.stream().collect(Collectors.toMap(UserExamPaperRecordDetail::getQuestionId, Function.identity(), (a, b) -> b));
                }
            }

            //获取本套试卷包含的题型
            Map<Integer, List<Question>> questionTemplateItemIdMap = questionList.stream().filter(question -> question.getParentId() == 0).collect(Collectors.groupingBy(Question::getQuestionTemplateItemId));

            //分离出试题集合的题型模板条目id
            Set<Integer> questionTemplateItemIdSet = questionList.stream().map(Question::getQuestionTemplateItemId).collect(Collectors.toSet());

            //查询题型模板条目信息
            Map<Integer, QuestionTemplateItem> questionTemplateItemNameMap = new HashMap<>();
            if(!questionTemplateItemIdSet.isEmpty()){
                List<QuestionTemplateItem> questionTemplateItemList = questionTemplateItemService.findByIds(new ArrayList<>(questionTemplateItemIdSet));
                questionTemplateItemNameMap = questionTemplateItemList.stream().collect(Collectors.toMap(QuestionTemplateItem::getId,Function.identity()));
            }
            //获取本套试卷包含的题型
            List<ExamPaperTypeResponse> typeResponses = new ArrayList<>();
            for (Integer questionTemplateItemId : questionTemplateItemIdMap.keySet()) {
                ExamPaperTypeResponse examPaperTypeResponse = new ExamPaperTypeResponse();
                examPaperTypeResponse.setQuestionTemplateItemId(questionTemplateItemId);
                if(questionTemplateItemNameMap.containsKey(questionTemplateItemId)){
                    QuestionTemplateItem questionTemplateItem = questionTemplateItemNameMap.get(questionTemplateItemId);
                    examPaperTypeResponse.setType(questionTemplateItem.getQuestionType());
                    examPaperTypeResponse.setTypeName(questionTemplateItem.getName());
                    examPaperTypeResponse.setSort(questionTemplateItem.getSort());
                    Set<Integer> questionIdSet = questionTemplateItemIdMap.get(questionTemplateItemId).stream().map(Question::getId).collect(Collectors.toSet());
                    examPaperTypeResponse.setQuestionIds(new ArrayList<>(questionIdSet));
                }
                typeResponses.add(examPaperTypeResponse);
            }

            //按题型模板配置的sort字段降序排序
            typeResponses = typeResponses.stream().sorted(Comparator.comparing(ExamPaperTypeResponse::getSort)).collect(Collectors.toList());


            List<QuestionAnalyticResponse> questionAnalyticResponseList = new ArrayList<>();
            for (Question question : questionList) {
                questionAnalyticResponseList.add(beanToResponse(null,question,questionTemplateItemNameMap,questionFavoriteMap,
                        examPaperQuestionMap,userExamRecordDetailMap,userExamPaperRecord
                        ,questionChapterMap,questionChapterList,examNoteMap,true,true,null));
            }

            //递归组装试卷题目关联关系的父子级结构
            List<QuestionAnalyticResponse> questionAnalyticResponsesTree = new ArrayList<>();
            if(!sysUserExamPaper) {
                questionAnalyticResponsesTree = buildQuestionTree(questionAnalyticResponseList, 0,1);
            }else{
                questionAnalyticResponsesTree = buildQuestionTree(questionAnalyticResponseList, 0,0);
            }
            List<QuestionAnalyticResponse> questionAnalyticResponses = new ArrayList<>();
            for (QuestionAnalyticResponse questionAnalyticResponse : questionAnalyticResponsesTree) {
                if(!QuestionBaseType.C6.getCode().equals(questionAnalyticResponse.getType())){
                    questionAnalyticResponses.add(questionAnalyticResponse);
                }else if(!isEmpty(questionAnalyticResponse.getQuestionResponseChildren()) && !questionAnalyticResponse.getQuestionResponseChildren().isEmpty()){
                    questionAnalyticResponses.add(questionAnalyticResponse);
                }
            }
            Map<Integer, QuestionAnalyticResponse> analyticResponseMap = questionAnalyticResponsesTree.stream().collect(Collectors.toMap(QuestionAnalyticResponse::getId, Function.identity()));
            //题目类型
            for (ExamPaperTypeResponse examPaperType : typeResponses) {
                //每个题目类型下所有的题的总分
                BigDecimal totalScore = BigDecimal.ZERO;
                //每个题目类型下所有题的用户总得分
                BigDecimal totalUserScore = BigDecimal.ZERO;
                //只有答题的题目ID，不包含子题
                List<Integer> questionIds = examPaperType.getQuestionIds();
                for (Integer questionId : questionIds) {
                    if(analyticResponseMap.containsKey(questionId)){
                        QuestionAnalyticResponse questionAnalyticResponse = analyticResponseMap.get(questionId);
                        //累加，总分 和得分
                        if(QuestionBaseType.C6.getCode().equals(questionAnalyticResponse.getType())){
                            List<QuestionAnalyticResponse> questionResponseChildren = questionAnalyticResponse.getQuestionResponseChildren();
                            for (QuestionAnalyticResponse questionResponseChild : questionResponseChildren) {
                                totalScore = totalScore.add(isEmpty(questionResponseChild.getScore()) ? BigDecimal.ZERO : questionResponseChild.getScore());
                                totalUserScore = totalUserScore.add(isEmpty(questionResponseChild.getUserScore()) ? BigDecimal.ZERO : questionResponseChild.getUserScore());
                            }
                        }else{
                            totalScore = totalScore.add(isEmpty(questionAnalyticResponse.getScore()) ? BigDecimal.ZERO :questionAnalyticResponse.getScore());
                            totalUserScore = totalUserScore.add(isEmpty(questionAnalyticResponse.getUserScore()) ? BigDecimal.ZERO : questionAnalyticResponse.getUserScore());
                        }
                    }
                }
                examPaperType.setTotalScore(totalScore);
                examPaperType.setTotalUserScore(totalUserScore);
            }
            response.setExamPaperQuestionTypeList(typeResponses);

            //题目数组
            response.setQuestionList(questionAnalyticResponses);
            return new Response<>(response);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }
    /**
     * 构建树结构
     */
    private List<QuestionResponse> buildQuestionTree(List<QuestionResponse> list, Integer pid) {
        List<QuestionResponse> tree = new ArrayList<>();
        for (QuestionResponse node : list) {
            if (pid.equals(node.getParentId())) {
                tree.add(findChild(node, list));
            }
        }
        return tree;
    }

    /**
     * 递归构建子级树结构
     */
    private QuestionResponse findChild(QuestionResponse node, List<QuestionResponse> list) {
        node.setQuestionResponseChildren(new ArrayList<>());
        for (QuestionResponse item : list) {
            if (node.getId().equals(item.getParentId())) {
                node.getQuestionResponseChildren().add(findChild(item, list));
            }
        }
        return node;
    }
    /**
     * 错题档案、收藏、笔记 查看题目详情(解析)
     *
     * <AUTHOR>
     */
    @RequestMapping(value = "/v1/user/exam/paper/record/analytic/ids/query",method = RequestMethod.POST)
    @Token
    public Response<?> queryAnalyticIds(@RequestBody QuestionRequest request) {
        UserToken userToken = getUserToken();
        try {
            ExamPaperQuestionAnalysisResponse response = new ExamPaperQuestionAnalysisResponse();
            response.setExamPaperQuestionTypeList(new ArrayList<>());
            response.setQuestionList(new ArrayList<>());
            //查询用户做题记录
            if(!isEmpty(request.getIds()) && !request.getIds().isEmpty()){
                //查询题目信息
                List<Question> questionList = questionService.findByIds(request.getIds());
                if(!isEmpty(questionList) && !questionList.isEmpty()){
                    Integer questionLibraryId = questionList.get(App.ROOT).getQuestionLibraryId();
                    //查询题库信息
                    QuestionLibrary questionLibrary = questionLibraryService.findById(questionLibraryId);
                    response.setProjectId(questionLibrary.getProjectId());
                }
                //找出题目中的材料题
                List<Integer> questionC6IdList = questionList.stream().filter(question -> QuestionBaseType.C6.getCode().equals(question.getType())).map(Question::getId).collect(Collectors.toList());
                if (!questionC6IdList.isEmpty()) {
                    QuestionQuery questionQuery = new QuestionQuery();
                    questionQuery.setStatus(com.api.constant.DataStatus.Y.getCode());
                    questionQuery.setParentIds(questionC6IdList);
                    List<Question> questionChildList = questionService.findAll(questionQuery);
                    if (!isEmpty(questionChildList) && !questionChildList.isEmpty()) {
                        questionList.addAll(questionChildList);
                    }
                }

                // 查询收藏的题
                UserExamPaperQuestionFavoriteQuery favoriteQuery = new UserExamPaperQuestionFavoriteQuery();
                favoriteQuery.setCustomerId(userToken.getCustomerId());
                favoriteQuery.setStatus(DataStatus.Y.getCode());

                List<UserExamPaperQuestionFavorite> questionFavoriteList = userExamPaperQuestionFavoriteService.findAll(favoriteQuery);
                Map<Integer, UserExamPaperQuestionFavorite> questionFavoriteMap = questionFavoriteList.stream().collect(Collectors.toMap(UserExamPaperQuestionFavorite::getQuestionId, Function.identity(),(a, b) ->a));
                //查询笔记
                UserExamPaperQuestionNoteQuery userExamPaperQuestionNoteQuery = new UserExamPaperQuestionNoteQuery();
                userExamPaperQuestionNoteQuery.setCustomerId(userToken.getCustomerId());
                userExamPaperQuestionNoteQuery.setQuestionIds(request.getIds());
                List<UserExamPaperQuestionNote> userExamPaperQuestionNotes = userExamPaperQuestionNoteService.findAll(userExamPaperQuestionNoteQuery);
                Map<Integer, UserExamPaperQuestionNote> examNoteMap = userExamPaperQuestionNotes.stream().collect(Collectors.toMap(UserExamPaperQuestionNote::getQuestionId, Function.identity(), (a, b) ->a,LinkedHashMap::new));


                //查询题目详情
                UserExamPaperRecordDetailQuery userExamPaperRecordDetailQuery = new UserExamPaperRecordDetailQuery();
                userExamPaperRecordDetailQuery.setCustomerId(userToken.getCustomerId());
                userExamPaperRecordDetailQuery.setQuestionIdList(request.getIds());
                userExamPaperRecordDetailQuery.setStatus(DataStatus.Y.getCode());
                List<UserExamPaperRecordDetail> userExamPaperRecordDetailList = userExamPaperRecordDetailService.findAll(userExamPaperRecordDetailQuery);
                //过滤掉退出保存状态的做题记录详情
                List<Integer> userExamPaperRecordIdList = userExamPaperRecordDetailList.stream().map(UserExamPaperRecordDetail::getUserExamPaperRecordId).collect(Collectors.toList());
                Map<Integer, UserExamPaperRecord> userExamPaperRecordMap = new HashMap<>();
                List<Integer> userExamPaperRecordOverNoList = new ArrayList<>();
                if(!userExamPaperRecordIdList.isEmpty()){
                    UserExamPaperRecordQuery userExamPaperRecordQuery = new UserExamPaperRecordQuery();
                    userExamPaperRecordQuery.setIds(userExamPaperRecordIdList);
                    userExamPaperRecordQuery.setStatus(DataStatus.Y.getCode());
                    List<UserExamPaperRecord> userExamPaperRecordList = userExamPaperRecordService.findAll(userExamPaperRecordQuery);

                    for (UserExamPaperRecord userExamPaperRecord : userExamPaperRecordList) {
                        if(UserExamPaperRecordOver.N.getCode().equals(userExamPaperRecord.getOver())){
                            userExamPaperRecordOverNoList.add(userExamPaperRecord.getId());
                        }
                        userExamPaperRecordMap.put(userExamPaperRecord.getId(),userExamPaperRecord);
                    }
                }
                List<UserExamPaperRecordDetail> userExamPaperRecordDetailFinalList = new ArrayList<>();
                for (UserExamPaperRecordDetail userExamPaperRecordDetail : userExamPaperRecordDetailList) {
                    if( UserExamPaperRecordDetailQuestionResult.N.getCode().equals(userExamPaperRecordDetail.getQuestionResult()) && !userExamPaperRecordOverNoList.contains(userExamPaperRecordDetail.getUserExamPaperRecordId())){
                        userExamPaperRecordDetailFinalList.add(userExamPaperRecordDetail);
                    }
                }

                //做题详情Map,题目id为key
                Map<Integer, UserExamPaperRecordDetail> userExamRecordDetailMap = new HashMap<>();
                if (!isEmpty(userExamPaperRecordDetailFinalList) && !userExamPaperRecordDetailFinalList.isEmpty()) {
                    //获取每道题的最新的做题记录详情
                    userExamRecordDetailMap = userExamPaperRecordDetailFinalList.stream().collect(
                            Collectors.groupingBy(UserExamPaperRecordDetail::getQuestionId, Collectors.collectingAndThen(Collectors.reducing(
                                    (t1, t2) -> t1.getCreateTime().after(t2.getCreateTime()) ? t1 : t2), Optional::get
                            )));
                }

                //查询项目下的章节考点信息
                //查询章节考点
                Map<Integer, QuestionChapter> questionChapterMap = new HashMap<>();
                QuestionChapterQuery questionChapterQuery = new QuestionChapterQuery();
                questionChapterQuery.setStatus(DataStatus.Y.getCode());
                //项目编号
                questionChapterQuery.setProjectId(request.getProjectId());
                List<QuestionChapter> questionChapterList = questionChapterService.findAll(questionChapterQuery);
                if (!isEmpty(questionChapterList) && !questionChapterList.isEmpty()){
                    questionChapterMap = questionChapterList.stream().collect(Collectors.toMap(QuestionChapter::getId, Function.identity()));
                }

                //获取本套试卷包含的题型
                Map<Integer, List<Question>> questionTemplateItemIdMap = questionList.stream().filter(question -> question.getParentId() == 0).collect(Collectors.groupingBy(Question::getQuestionTemplateItemId));

                //分离出试题集合的题型模板条目id
                Set<Integer> questionTemplateItemIdSet = questionList.stream().map(Question::getQuestionTemplateItemId).collect(Collectors.toSet());
                //查询题型模板条目信息
                Map<Integer, QuestionTemplateItem> questionTemplateItemNameMap = new HashMap<>();
                if(!questionTemplateItemIdSet.isEmpty()){
                    List<QuestionTemplateItem> questionTemplateItemList = questionTemplateItemService.findByIds(new ArrayList<>(questionTemplateItemIdSet));
                    questionTemplateItemNameMap = questionTemplateItemList.stream().collect(Collectors.toMap(QuestionTemplateItem::getId,Function.identity()));
                }
                //获取本套试卷包含的题型
                List<ExamPaperTypeResponse> typeResponses = new ArrayList<>();
                for (Integer questionTemplateItemId : questionTemplateItemIdMap.keySet()) {
                    ExamPaperTypeResponse examPaperTypeResponse = new ExamPaperTypeResponse();
                    examPaperTypeResponse.setQuestionTemplateItemId(questionTemplateItemId);
                    if(questionTemplateItemNameMap.containsKey(questionTemplateItemId)){
                        QuestionTemplateItem questionTemplateItem = questionTemplateItemNameMap.get(questionTemplateItemId);
                        examPaperTypeResponse.setType(questionTemplateItem.getQuestionType());
                        examPaperTypeResponse.setTypeName(questionTemplateItem.getName());
                        examPaperTypeResponse.setSort(questionTemplateItem.getSort());
                    }
                    typeResponses.add(examPaperTypeResponse);
                }

                //按题型模板配置的sort字段降序排序
                typeResponses = typeResponses.stream().sorted(Comparator.comparing(ExamPaperTypeResponse::getSort)).collect(Collectors.toList());


                List<QuestionAnalyticResponse> questionAnalyticResponseList = new ArrayList<>();
                for (Question question : questionList) {
                    questionAnalyticResponseList.add(beanToResponse(userExamPaperRecordMap,question,questionTemplateItemNameMap,questionFavoriteMap,null
                            ,userExamRecordDetailMap,null,questionChapterMap,questionChapterList,examNoteMap,false,false,null));
                }
                //递归组装试卷题目关联关系的父子级结构
                List<QuestionAnalyticResponse> questionAnalyticResponses = buildQuestionTree(questionAnalyticResponseList, 0,1);
                response.setExamPaperQuestionTypeList(typeResponses);
                response.setQuestionList(questionAnalyticResponses);
                response.setQuestionNumber(questionAnalyticResponses.size());
            }
            return new Response<>(response);
        }catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
}
    /**
     * 错题档案查看题目详情(解析)
     *
     * <AUTHOR>
     */
    @RequestMapping(value = "/v1/user/exam/paper/record/analytic/detail/ids/query",method = RequestMethod.POST)
    @Token
    public com.common.bean.Response<?> queryAnalyticDetailIds(@RequestBody UserExamPaperRecordDetailRequest request) {
        UserToken userToken = getUserToken();
        try {
            ExamPaperQuestionAnalysisResponse response = new ExamPaperQuestionAnalysisResponse();
            response.setExamPaperQuestionTypeList(new ArrayList<>());
            response.setQuestionList(new ArrayList<>());
            //做题记录详情不为空
            if (!isEmpty(request.getIds()) && !request.getIds().isEmpty()) {
                //查询做题记录详情
                UserExamPaperRecordDetailQuery userExamPaperRecordDetailQuery = new UserExamPaperRecordDetailQuery();
                userExamPaperRecordDetailQuery.setIds(request.getIds());
                userExamPaperRecordDetailQuery.setStatus(DataStatus.Y.getCode());
                List<UserExamPaperRecordDetail> userExamPaperRecordDetails = userExamPaperRecordDetailService.findAll(userExamPaperRecordDetailQuery);
                if (!isEmpty(userExamPaperRecordDetails) && !userExamPaperRecordDetails.isEmpty()) {
                    Set<Integer> questionIdSet = new HashSet<>();
                    Map<Integer, UserExamPaperRecordDetail> userExamPaperRecordDetailMap = new HashMap<>();
                    Set<Integer> userExamPaperRecordIdList = new HashSet<>();
                    for (UserExamPaperRecordDetail userExamPaperRecordDetail : userExamPaperRecordDetails) {
                        questionIdSet.add(userExamPaperRecordDetail.getQuestionId());
                        userExamPaperRecordDetailMap.put(userExamPaperRecordDetail.getId(),userExamPaperRecordDetail);
                        userExamPaperRecordIdList.add(userExamPaperRecordDetail.getUserExamPaperRecordId());
                    }
                    Map<Integer, UserExamPaperRecord> userExamPaperRecordMap = new HashMap<>();
                    if(!userExamPaperRecordIdList.isEmpty()){
                        UserExamPaperRecordQuery userExamPaperRecordQuery = new UserExamPaperRecordQuery();
                        userExamPaperRecordQuery.setIds(new ArrayList<>(userExamPaperRecordIdList));
                        userExamPaperRecordQuery.setStatus(DataStatus.Y.getCode());
                        List<UserExamPaperRecord> userExamPaperRecordList = userExamPaperRecordService.findAll(userExamPaperRecordQuery);

                        for (UserExamPaperRecord userExamPaperRecord : userExamPaperRecordList) {
                            userExamPaperRecordMap.put(userExamPaperRecord.getId(),userExamPaperRecord);
                        }
                    }

                    //查询题目信息
                    List<Question> questionList = questionService.findByIds(new ArrayList<>(questionIdSet));
                    //如果题目是材料题的小题 需需要查出小题的大题（材料题）
                    List<Integer> questionC6IdList = questionList.stream().
                            filter(question -> 0 != (question.getParentId())).map(Question::getParentId).collect(Collectors.toList());
                    //找出题目中的材料题
                    if (!questionC6IdList.isEmpty()) {
                        QuestionQuery questionQuery = new QuestionQuery();
                        questionQuery.setStatus(com.api.constant.DataStatus.Y.getCode());
                        questionQuery.setIds(questionC6IdList);
                        List<Question> questionChildList = questionService.findAll(questionQuery);
                        if (!isEmpty(questionChildList) && !questionChildList.isEmpty()) {
                            questionList.addAll(questionChildList);
                        }
                    }

                    Map<Integer, Question> questionMap = questionList.stream().collect(Collectors.toMap(Question::getId, Function.identity()));

                    // 查询收藏的题
                    UserExamPaperQuestionFavoriteQuery favoriteQuery = new UserExamPaperQuestionFavoriteQuery();
                    favoriteQuery.setCustomerId(userToken.getCustomerId());
                    favoriteQuery.setStatus(DataStatus.Y.getCode());

                    List<UserExamPaperQuestionFavorite> questionFavoriteList = userExamPaperQuestionFavoriteService.findAll(favoriteQuery);
                    Map<Integer, UserExamPaperQuestionFavorite> questionFavoriteMap = questionFavoriteList.stream().collect(Collectors.toMap(UserExamPaperQuestionFavorite::getQuestionId, Function.identity(),(a, b) -> a));
                    //查询笔记
                    UserExamPaperQuestionNoteQuery userExamPaperQuestionNoteQuery = new UserExamPaperQuestionNoteQuery();
                    userExamPaperQuestionNoteQuery.setCustomerId(userToken.getCustomerId());
                    userExamPaperQuestionNoteQuery.setQuestionIds(new ArrayList<>(questionIdSet));
                    List<UserExamPaperQuestionNote> userExamPaperQuestionNotes = userExamPaperQuestionNoteService.findAll(userExamPaperQuestionNoteQuery);
                    Map<Integer, UserExamPaperQuestionNote> examNoteMap = userExamPaperQuestionNotes.stream().collect(Collectors.toMap(UserExamPaperQuestionNote::getQuestionId, Function.identity(), (a, b) -> a, LinkedHashMap::new));


                    //查询项目下的章节考点信息
                    //查询章节考点
                    Map<Integer, QuestionChapter> questionChapterMap = new HashMap<>();
                    QuestionChapterQuery questionChapterQuery = new QuestionChapterQuery();
                    questionChapterQuery.setStatus(DataStatus.Y.getCode());
                    //项目编号
                    questionChapterQuery.setProjectId(request.getProjectId());
                    List<QuestionChapter> questionChapterList = questionChapterService.findAll(questionChapterQuery);
                    if (!isEmpty(questionChapterList) && !questionChapterList.isEmpty()){
                        questionChapterMap = questionChapterList.stream().collect(Collectors.toMap(QuestionChapter::getId, Function.identity()));
                    }

                    //获取本套试卷包含的题型
                    Map<Integer, List<Question>> questionTemplateItemIdMap = questionList.stream().filter(question -> question.getParentId() == 0).collect(Collectors.groupingBy(Question::getQuestionTemplateItemId));

                    //分离出试题集合的题型模板条目id
                    Set<Integer> questionTemplateItemIdSet = questionList.stream().map(Question::getQuestionTemplateItemId).collect(Collectors.toSet());
                    //查询题型模板条目信息
                    Map<Integer, QuestionTemplateItem> questionTemplateItemNameMap = new HashMap<>();
                    if(!questionTemplateItemIdSet.isEmpty()){
                        List<QuestionTemplateItem> questionTemplateItemList = questionTemplateItemService.findByIds(new ArrayList<>(questionTemplateItemIdSet));
                        questionTemplateItemNameMap = questionTemplateItemList.stream().collect(Collectors.toMap(QuestionTemplateItem::getId,Function.identity()));
                    }
                    //获取本套试卷包含的题型
                    List<ExamPaperTypeResponse> typeResponses = new ArrayList<>();
                    for (Integer questionTemplateItemId : questionTemplateItemIdMap.keySet()) {
                        ExamPaperTypeResponse examPaperTypeResponse = new ExamPaperTypeResponse();
                        examPaperTypeResponse.setQuestionTemplateItemId(questionTemplateItemId);
                        if(questionTemplateItemNameMap.containsKey(questionTemplateItemId)){
                            QuestionTemplateItem questionTemplateItem = questionTemplateItemNameMap.get(questionTemplateItemId);
                            examPaperTypeResponse.setType(questionTemplateItem.getQuestionType());
                            examPaperTypeResponse.setTypeName(questionTemplateItem.getName());
                            examPaperTypeResponse.setSort(questionTemplateItem.getSort());
                        }
                        typeResponses.add(examPaperTypeResponse);
                    }

                    //按题型模板配置的sort字段降序排序
                    typeResponses = typeResponses.stream().sorted(Comparator.comparing(ExamPaperTypeResponse::getSort)).collect(Collectors.toList());

                    List<QuestionAnalyticResponse> questionAnalyticResponseList = new ArrayList<>();

                    for (UserExamPaperRecordDetail userExamPaperRecordDetail : userExamPaperRecordDetails) {
                        if(questionMap.containsKey(userExamPaperRecordDetail.getQuestionId())){
                            Question question = questionMap.get(userExamPaperRecordDetail.getQuestionId());
                            if(0 != question.getParentId()){
                                question = questionMap.get(question.getParentId());
                            }
                            QuestionAnalyticResponse questionAnalyticResponse = beanToResponse(userExamPaperRecordMap, question, questionTemplateItemNameMap, questionFavoriteMap, null
                                    , userExamPaperRecordDetailMap, null, questionChapterMap, questionChapterList, examNoteMap, false, false, request.getShowUserAnswer());

                            //存入已提交答案
                            questionAnalyticResponse.setUserAnswer(userExamPaperRecordDetail.getAnswer());
                            if (!isEmpty(request.getShowUserAnswer()) && PublicShow.N.getCode().equals(request.getShowUserAnswer())) {
                                questionAnalyticResponse.setUserAnswer(null);
                            }
                            questionAnalyticResponse.setQuestionResult(userExamPaperRecordDetail.getQuestionResult());
                            questionAnalyticResponse.setUserExamPaperRecordDetailId(userExamPaperRecordDetail.getId());
                            questionAnalyticResponse.setUserScore(userExamPaperRecordDetail.getScore());
                            questionAnalyticResponse.setCustomerImgUrls(userExamPaperRecordDetail.getCustomerImgUrls());
                            questionAnalyticResponse.setCommentImgUrls(userExamPaperRecordDetail.getCommentImgUrls());
                            questionAnalyticResponse.setTeacherComment(userExamPaperRecordDetail.getTeacherComment());
                            if (userExamPaperRecordMap != null && userExamPaperRecordMap.containsKey(userExamPaperRecordDetail.getUserExamPaperRecordId())) {
                                questionAnalyticResponse.setExamPaperId(userExamPaperRecordMap.get(userExamPaperRecordDetail.getUserExamPaperRecordId()).getExamPaperId());
                            }
                            questionAnalyticResponse.setUserExamPaperRecordDetailId(userExamPaperRecordDetail.getId());
                            questionAnalyticResponseList.add(questionAnalyticResponse);
                        }
                    }
                    //递归组装试卷题目关联关系的父子级结构
                    List<QuestionAnalyticResponse> questionAnalyticResponses = buildQuestionTree(questionAnalyticResponseList, 0,1);

                    response.setExamPaperQuestionTypeList(typeResponses);

                    //错题解析集合
                    response.setQuestionList(questionAnalyticResponses);

                    //错题数量
                    response.setQuestionNumber(userExamPaperRecordDetails.size());

                }
            }
            return new com.common.bean.Response<>(response);

        }catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new com.common.bean.Response<>(ERROR, FAILURE);
        }
    }
    /**
     * 错题档案按时间查看题目详情(解析)
     *
     * <AUTHOR>
     */
    @RequestMapping(value = "/v1/user/exam/paper/record/analytic/date/query",method = RequestMethod.POST)
    @Token
    public Response<?> queryAnalyticByDate(@RequestBody UserExamPaperRecordDetailRequest request) {
        UserToken userToken = getUserToken();
        try {
            if(isEmpty(request.getDisplayDate())){
                return new Response<>(ERROR,"时间日期不能为空");
            }
            if(isEmpty(request.getProjectId())){
                return new Response<>(ERROR,"项目编号不能为空");
            }
            Date displayDate = DateUtil.parse(request.getDisplayDate(), DATE_FORMAT);
            Date displayDateStart = DateUtil.getDayStartTime(displayDate);
            Date displayDateEnd = DateUtil.getDayEndTime(displayDate);
            ExamPaperQuestionAnalysisResponse response = new ExamPaperQuestionAnalysisResponse();
            response.setExamPaperQuestionTypeList(new ArrayList<>());
            response.setQuestionList(new ArrayList<>());

            //查询用户的错题记录
            UserExamPaperRecordQuery userExamPaperRecordQuery = new UserExamPaperRecordQuery();
            userExamPaperRecordQuery.setProjectId(request.getProjectId());
            userExamPaperRecordQuery.setMinEndTime(displayDateStart);
            userExamPaperRecordQuery.setMaxEndTime(displayDateEnd);
            userExamPaperRecordQuery.setCustomerId(userToken.getCustomerId());
            userExamPaperRecordQuery.setOver(UserExamRecordOver.Y.getCode());
            userExamPaperRecordQuery.setStatus(DataStatus.Y.getCode());
            List<UserExamPaperRecord> userExamPaperRecordList = userExamPaperRecordService.findAll(userExamPaperRecordQuery);

            //同一个试卷多次做题记录只取最新的一次
            List<UserExamPaperRecord> userExamPaperRecords = new ArrayList<>();
            //存储 智能组卷、快速练习 课后练习-题目 组建的虚拟卷
            List<UserExamPaperRecord> userExamPaperRecordFictitious = new ArrayList<>();
            //存储 模拟考试 历年真题 科目练习 课后练习-试卷系统卷
            List<UserExamPaperRecord> userExamPaperRecordOther = new ArrayList<>();
            for (UserExamPaperRecord userExamPaperRecord : userExamPaperRecordList) {
                if(QuestionArguments.USER_EXAM_PAPER_CATALOG_CODE_LIST.contains(userExamPaperRecord.getCatalog())){
                    userExamPaperRecordFictitious.add(userExamPaperRecord);
                }else{
                    userExamPaperRecordOther.add(userExamPaperRecord);
                }
            }
            //虚拟卷取最新做题记录
            Map<Integer, List<UserExamPaperRecord>> examPaperIdRecordFictitiousListMap = userExamPaperRecordFictitious.stream().collect(Collectors.groupingBy(UserExamPaperRecord::getExamPaperId,
                    Collectors.collectingAndThen(
                            Collectors.toList(),
                            list -> list.stream()
                                    .sorted(Comparator.comparing(UserExamPaperRecord::getModifyTime).reversed())
                                    .collect(Collectors.toList())
                    )
            ));
            for (List<UserExamPaperRecord> value : examPaperIdRecordFictitiousListMap.values()) {
                userExamPaperRecords.add(value.get(0));
            }
            //系统卷取最新做题记录
            Map<Integer, List<UserExamPaperRecord>> examPaperIdRecordOtherListMap = userExamPaperRecordOther.stream().collect(Collectors.groupingBy(UserExamPaperRecord::getExamPaperId,
                    Collectors.collectingAndThen(
                            Collectors.toList(),
                            list -> list.stream()
                                    .sorted(Comparator.comparing(UserExamPaperRecord::getEndTime).reversed())
                                    .collect(Collectors.toList())
                    )
            ));
            for (List<UserExamPaperRecord> value : examPaperIdRecordOtherListMap.values()) {
                userExamPaperRecords.add(value.get(0));
            }

            //考卷记录编号
            Map<Integer, UserExamPaperRecord> examPaperRecordMap = userExamPaperRecords.stream().collect(Collectors.toMap(UserExamPaperRecord::getId, Function.identity()));

            Set<Integer> userExamPaperRecordIds = userExamPaperRecords.stream().map(UserExamPaperRecord::getId).collect(Collectors.toSet());
            //2.查询用户已经做过的题
            //存储做错的题的记录详情
            List<Integer> errorUserExamPaperRecordQuestionIdList = new ArrayList<>();
            List<UserExamPaperRecordDetail> errorUserExamPaperRecordDetailList = new ArrayList<>();

            //存储做错的题
            List<Question> questions = new ArrayList<>();
            if(!isEmpty(userExamPaperRecordIds) && !userExamPaperRecordIds.isEmpty()){
                UserExamPaperRecordDetailQuery userExamPaperRecordDetailQuery = new UserExamPaperRecordDetailQuery();
                userExamPaperRecordDetailQuery.setUserExamPaperRecordIds(new ArrayList<>(userExamPaperRecordIds));
                userExamPaperRecordDetailQuery.setStatus(DataStatus.Y.getCode());
                List<UserExamPaperRecordDetail> userExamPaperRecordDetailList = userExamPaperRecordDetailService.findAll(userExamPaperRecordDetailQuery);
                //用户已经做过的题的id
                Set<Integer> questionSet = userExamPaperRecordDetailList.stream().map(UserExamPaperRecordDetail::getQuestionId).collect(Collectors.toSet());
                //查询题目信息（不包含材料题的小题）
                QuestionQuery questionQuery = new QuestionQuery();
                questionQuery.setParentId(0);
                questionQuery.setIds(new ArrayList<>(questionSet));
                questionQuery.setStatus(DataStatus.Y.getCode());
                List<Question> questionList = questionService.findAll(questionQuery);

                //3.找出做错的题 (只判断选择题 和判断题)
                Map<Integer, Question> questionMap = questionList.stream().collect(Collectors.toMap(Question::getId, Function.identity()));
                for (UserExamPaperRecordDetail userExamPaperRecordDetail : userExamPaperRecordDetailList) {
                    if(questionMap.containsKey(userExamPaperRecordDetail.getQuestionId()) &&
                            QuestionArguments.CAN_DETERMINE_ANSWER_RIGHT_OR_WRONG.contains(questionMap.get(userExamPaperRecordDetail.getQuestionId()).getType())){
                        Question question = questionMap.get(userExamPaperRecordDetail.getQuestionId());
                        if (!question.getAnswer().equals(userExamPaperRecordDetail.getAnswer())) {
                            questions.add(question);
                            errorUserExamPaperRecordQuestionIdList.add(userExamPaperRecordDetail.getQuestionId());
                            errorUserExamPaperRecordDetailList.add(userExamPaperRecordDetail);
                        }
                    }
                }
            }

            if(!isEmpty(errorUserExamPaperRecordDetailList) && !errorUserExamPaperRecordDetailList.isEmpty()){

                //获取本套试卷包含的题型
                Map<Integer, List<Question>> questionTemplateItemIdMap = questions.stream().filter(question -> question.getParentId() == 0).collect(Collectors.groupingBy(Question::getQuestionTemplateItemId));

                //分离出试题集合的题型模板条目id
                Set<Integer> questionTemplateItemIdSet = questions.stream().map(Question::getQuestionTemplateItemId).collect(Collectors.toSet());
                //查询题型模板条目信息
                Map<Integer, QuestionTemplateItem> questionTemplateItemNameMap = new HashMap<>();
                if(!questionTemplateItemIdSet.isEmpty()){
                    List<QuestionTemplateItem> questionTemplateItemList = questionTemplateItemService.findByIds(new ArrayList<>(questionTemplateItemIdSet));
                    questionTemplateItemNameMap = questionTemplateItemList.stream().collect(Collectors.toMap(QuestionTemplateItem::getId,Function.identity()));
                }
                //获取本套试卷包含的题型
                List<ExamPaperTypeResponse> typeResponses = new ArrayList<>();
                for (Integer questionTemplateItemId : questionTemplateItemIdMap.keySet()) {
                    ExamPaperTypeResponse examPaperTypeResponse = new ExamPaperTypeResponse();
                    examPaperTypeResponse.setQuestionTemplateItemId(questionTemplateItemId);
                    if(questionTemplateItemNameMap.containsKey(questionTemplateItemId)){
                        QuestionTemplateItem questionTemplateItem = questionTemplateItemNameMap.get(questionTemplateItemId);
                        examPaperTypeResponse.setType(questionTemplateItem.getQuestionType());
                        examPaperTypeResponse.setTypeName(questionTemplateItem.getName());
                        examPaperTypeResponse.setSort(questionTemplateItem.getSort());
                    }
                    typeResponses.add(examPaperTypeResponse);
                }

                //按题型模板配置的sort字段降序排序
                typeResponses = typeResponses.stream().sorted(Comparator.comparing(ExamPaperTypeResponse::getSort)).collect(Collectors.toList());

                List<QuestionAnalyticResponse> questionAnalyticResponseList = queryQuestionAnalyticByQuestionIds(examPaperRecordMap,errorUserExamPaperRecordDetailList, userToken, request.getProjectId(),request.getShowUserAnswer());

                //递归组装试卷题目关联关系的父子级结构
                List<QuestionAnalyticResponse> questionAnalyticResponses = buildQuestionTree(questionAnalyticResponseList, 0,1);
                response.setExamPaperQuestionTypeList(typeResponses);
                response.setQuestionList(questionAnalyticResponses);
                response.setQuestionNumber(questionAnalyticResponses.size());
            }
            return new Response<>(response);

        }catch (Exception e){
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }
    public List<QuestionAnalyticResponse> queryQuestionAnalyticByQuestionIds( Map<Integer, UserExamPaperRecord> examPaperRecordMap,
                                                                              List<UserExamPaperRecordDetail> errorUserExamPaperRecordDetailList,
                                                                              UserToken userToken,
                                                                              Integer projectId,
                                                                              String showUserAnswer){
        List<Integer> questionIds = errorUserExamPaperRecordDetailList.stream().map(UserExamPaperRecordDetail::getQuestionId).collect(Collectors.toList());
        //获取本套试卷包含的题型
        Map<Integer, List<Question>> questionTemplateItemIdMap = new HashMap<>();
        //分离出试题集合的题型模板条目id
        Set<Integer> questionTemplateItemIdSet = new HashSet<>();

        Map<Integer, Question> questionMap = new HashMap<>();

        if(!isEmpty(questionIds) && !questionIds.isEmpty()){
             //查询题目信息
             List<Question> questionList = questionService.findByIds(questionIds);
            List<Integer> questionC6IdList = questionList.stream().filter(question -> QuestionBaseType.C6.getCode().equals(question.getType())).map(Question::getId).collect(Collectors.toList());
            if (!questionC6IdList.isEmpty()) {
                QuestionQuery questionQuery = new QuestionQuery();
                questionQuery.setStatus(com.api.constant.DataStatus.Y.getCode());
                questionQuery.setParentIds(questionC6IdList);
                List<Question> questionChildList = questionService.findAll(questionQuery);
                if (!isEmpty(questionChildList) && !questionChildList.isEmpty()) {
                    questionList.addAll(questionChildList);
                }
            }

            for (Question question : questionList) {
                 if(question.getParentId() == 0){
                     if(questionTemplateItemIdMap.containsKey(question.getQuestionTemplateItemId())){
                         questionTemplateItemIdMap.get(question.getQuestionTemplateItemId()).add(question);
                     }else{
                         List<Question> questions = new ArrayList<>();
                         questions.add(question);
                         questionTemplateItemIdMap.put(question.getQuestionTemplateItemId(),questions);
                     }
                 }
                 if(!questionMap.containsKey(question.getId())){
                     questionMap.put(question.getId(),question);
                 }
                 questionTemplateItemIdSet.add(question.getQuestionTemplateItemId());
             }
         }


        // 查询收藏的题
        UserExamPaperQuestionFavoriteQuery favoriteQuery = new UserExamPaperQuestionFavoriteQuery();
        favoriteQuery.setCustomerId(userToken.getCustomerId());
        favoriteQuery.setStatus(DataStatus.Y.getCode());

        List<UserExamPaperQuestionFavorite> questionFavoriteList = userExamPaperQuestionFavoriteService.findAll(favoriteQuery);
        Map<Integer, UserExamPaperQuestionFavorite> questionFavoriteMap = questionFavoriteList.stream().collect(Collectors.toMap(UserExamPaperQuestionFavorite::getQuestionId, Function.identity(),(a, b) ->a));
        //查询笔记
        UserExamPaperQuestionNoteQuery userExamPaperQuestionNoteQuery = new UserExamPaperQuestionNoteQuery();
        userExamPaperQuestionNoteQuery.setCustomerId(userToken.getCustomerId());
        userExamPaperQuestionNoteQuery.setQuestionIds(questionIds);
        List<UserExamPaperQuestionNote> userExamPaperQuestionNotes = userExamPaperQuestionNoteService.findAll(userExamPaperQuestionNoteQuery);
        Map<Integer, UserExamPaperQuestionNote> examNoteMap = userExamPaperQuestionNotes.stream().collect(Collectors.toMap(UserExamPaperQuestionNote::getQuestionId, Function.identity(), (a, b) ->a,LinkedHashMap::new));

        //过滤掉退出保存状态的做题记录详情
        List<Integer> userExamPaperRecordIdList = errorUserExamPaperRecordDetailList.stream().map(UserExamPaperRecordDetail::getUserExamPaperRecordId).collect(Collectors.toList());
        if(!userExamPaperRecordIdList.isEmpty()){
            UserExamPaperRecordQuery userExamPaperRecordQuery = new UserExamPaperRecordQuery();
            userExamPaperRecordQuery.setExamPaperIds(userExamPaperRecordIdList);
            userExamPaperRecordQuery.setStatus(DataStatus.Y.getCode());
            List<UserExamPaperRecord> userExamPaperRecordList = userExamPaperRecordService.findAll(userExamPaperRecordQuery);
            userExamPaperRecordIdList = userExamPaperRecordList.stream().filter(userExamPaperRecord -> UserExamPaperRecordOver.N.getCode().equals(userExamPaperRecord.getOver()))
                    .map(UserExamPaperRecord::getId).collect(Collectors.toList());

        }
        List<UserExamPaperRecordDetail> userExamPaperRecordDetailFinalList = new ArrayList<>();
        for (UserExamPaperRecordDetail userExamPaperRecordDetail : errorUserExamPaperRecordDetailList) {
            if( UserExamPaperRecordDetailQuestionResult.N.getCode().equals(userExamPaperRecordDetail.getQuestionResult()) && !userExamPaperRecordIdList.contains(userExamPaperRecordDetail.getUserExamPaperRecordId())){
                userExamPaperRecordDetailFinalList.add(userExamPaperRecordDetail);
            }
        }

        //做题详情Map,题目id为key 区分 全部解析 和 错题解析两种情况
        Map<Integer, UserExamPaperRecordDetail> userExamRecordDetailMap = new HashMap<>();
        if (!isEmpty(userExamPaperRecordDetailFinalList) && !userExamPaperRecordDetailFinalList.isEmpty()) {
            //获取每道题的最新的做题记录详情
            userExamRecordDetailMap = userExamPaperRecordDetailFinalList.stream().collect(
                    Collectors.groupingBy(UserExamPaperRecordDetail::getQuestionId, Collectors.collectingAndThen(Collectors.reducing(
                            (t1, t2) -> t1.getCreateTime().after(t2.getCreateTime()) ? t1 : t2), Optional::get
                    )));
        }
        //查询项目下的章节考点信息
        //查询章节考点
        Map<Integer, QuestionChapter> questionChapterMap = new HashMap<>();
        QuestionChapterQuery questionChapterQuery = new QuestionChapterQuery();
        questionChapterQuery.setStatus(DataStatus.Y.getCode());
        //项目编号
        questionChapterQuery.setProjectId(projectId);
        List<QuestionChapter> questionChapterList = questionChapterService.findAll(questionChapterQuery);
        if (!isEmpty(questionChapterList) && !questionChapterList.isEmpty()){
            questionChapterMap = questionChapterList.stream().collect(Collectors.toMap(QuestionChapter::getId, Function.identity()));
        }


        //查询题型模板条目信息
        Map<Integer, QuestionTemplateItem> questionTemplateItemNameMap = new HashMap<>();
        if(!questionTemplateItemIdSet.isEmpty()){
            List<QuestionTemplateItem> questionTemplateItemList = questionTemplateItemService.findByIds(new ArrayList<>(questionTemplateItemIdSet));
            questionTemplateItemNameMap = questionTemplateItemList.stream().collect(Collectors.toMap(QuestionTemplateItem::getId,Function.identity()));
        }
        List<QuestionAnalyticResponse> questionAnalyticResponseList = new ArrayList<>();
        for (Integer userExamRecordDetailId : userExamRecordDetailMap.keySet()) {
            Integer questionId = userExamRecordDetailMap.get(userExamRecordDetailId).getQuestionId();
            if(questionMap.containsKey(questionId)){
                questionAnalyticResponseList.add(beanToResponse(examPaperRecordMap,questionMap.get(questionId),questionTemplateItemNameMap,questionFavoriteMap,null
                        ,userExamRecordDetailMap,null,questionChapterMap,questionChapterList,examNoteMap,false,false,showUserAnswer));
            }
        }
        return questionAnalyticResponseList;
    }

    /**
     * 数据库中题目实体转换response对象通用方法,以下参数除了question本身,其他的均可以为空,根据当前需求填充参数即可
     *
     */
    private QuestionAnalyticResponse beanToResponse(Map<Integer, UserExamPaperRecord> examPaperRecordMap,Question question, Map<Integer, QuestionTemplateItem> questionTemplateItemNameMap,
                                                    Map<Integer, UserExamPaperQuestionFavorite> questionFavoriteMap, Map<Integer, ExamPaperQuestion> examPaperQuestionMap,
                                                    Map<Integer, UserExamPaperRecordDetail> userExamPaperRecordDetailMap,
                                                    UserExamPaperRecord userExamPaperRecord,Map<Integer, QuestionChapter> questionChapterMap,
                                                    List<QuestionChapter> questionChapterList,Map<Integer, UserExamPaperQuestionNote> examNoteMap,
                                                    boolean showMark,boolean makeScore,String showUserAnswer ) {
        QuestionAnalyticResponse response = new QuestionAnalyticResponse();
        //星级
        if (question != null && !isEmpty(question.getStarLevel())){
            response.setStarLevel(question.getStarLevel());
            response.setStarLevelName(QuestionStarLevel.getName(question.getStarLevel()));
        }
        //题目ID
        response.setId(question.getId());
        //题目父id
        response.setParentId(question.getParentId());
        //科目id
        response.setProjectItemId(question.getProjectItemId());
        //题库ID
        response.setQuestionLibraryId(question.getQuestionLibraryId());
        //题目类型
        response.setType(question.getType());
        //章节编号
        response.setQuestionChapterId(question.getQuestionChapterId());
        //章 节 知识点
        if (!isEmpty(questionChapterMap) && questionChapterMap.containsKey(question.getQuestionChapterId())){
            QuestionChapter questionChapter = questionChapterMap.get(question.getQuestionChapterId());
            if (QuestionChapterCatalog.C1000.getCode().equals(questionChapter.getCatalog())){
                response.setQuestionChapterC1000Name(questionChapter.getName());
            }else if (QuestionChapterCatalog.C1001.getCode().equals(questionChapter.getCatalog())){
                response.setQuestionChapterC1001Name(questionChapter.getName());
                //递归查询章的名称
                List<QuestionChapter> parentList = new ArrayList<>();
                getQuestionChapterParents(questionChapter,questionChapterList,parentList);

                //因为取最后一个章，递归是从下往上查，所以最近的一个为集合的第一个
                for (QuestionChapter parent : parentList) {
                    if (QuestionChapterCatalog.C1000.getCode().equals(parent.getCatalog())){
                        response.setQuestionChapterC1000Name(parent.getName());
                        break;
                    }
                }
            } else if (QuestionChapterCatalog.C1002.getCode().equals(questionChapter.getCatalog())){
                response.setQuestionChapterC1002Name(questionChapter.getName());
                //递归查询章/节的名称
                List<QuestionChapter> parentList = new ArrayList<>();
                getQuestionChapterParents(questionChapter,questionChapterList,parentList);

                //因为取最后一个章/节，递归是从下往上查，所以最近的一个为集合的第一个
                for (QuestionChapter parent : parentList) {
                    if (QuestionChapterCatalog.C1000.getCode().equals(parent.getCatalog()) && this.isEmpty(response.getQuestionChapterC1000Name())){
                        response.setQuestionChapterC1000Name(parent.getName());
                    }
                    if (QuestionChapterCatalog.C1001.getCode().equals(parent.getCatalog()) && this.isEmpty(response.getQuestionChapterC1001Name())){
                        response.setQuestionChapterC1001Name(parent.getName());
                    }
                }
            } else if (QuestionChapterCatalog.C1003.getCode().equals(questionChapter.getCatalog())){
                response.setQuestionChapterC1003Name(questionChapter.getName());
            }
            if(QuestionChapterCatalog.C1003.getCode().equals(questionChapter.getCatalog())){
                response.setQuestionChapterName(questionChapter.getName());
            }else{
                List<String> questionChapterNameList = Arrays.asList(response.getQuestionChapterC1000Name(),response.getQuestionChapterC1001Name(),response.getQuestionChapterC1002Name());
                String questionChapterName = String.join("-", questionChapterNameList.stream().filter(chapterName -> !isEmpty(chapterName)).collect(Collectors.toList()));
                response.setQuestionChapterName(questionChapterName);
            }

        }
        //题目类型名称
        if(questionTemplateItemNameMap.containsKey(question.getQuestionTemplateItemId())){
            response.setTypeName(questionTemplateItemNameMap.get(question.getQuestionTemplateItemId()).getName());
        }else{
            response.setTypeName(QuestionBaseType.getName(question.getType()));
        }
        //题型模板
        response.setQuestionTemplateItemId(question.getQuestionTemplateItemId());
        if (questionTemplateItemNameMap != null && questionTemplateItemNameMap.containsKey(question.getQuestionTemplateItemId())) {
            String questionTemplateItemName = questionTemplateItemNameMap.get(question.getQuestionTemplateItemId()).getName();
            //自定义题型模板名称
            response.setQuestionTemplateItemName(questionTemplateItemName);
        }

        //题目标题
        response.setQuestion(question.getQuestion());
        //选项/材料
        response.setOption(question.getOption());
        //选项(只有选择题才有)
        if (QuestionArguments.QUESTION_TYPE_FOR_SELECT_CODE_LIST.contains(question.getType())) {
            List<QuestionOptionResponse> questionOptionResponseList = null;
            if (!isEmpty(question.getOption())) {
                questionOptionResponseList = QuestionArguments.analysisOptionList(question.getOption());
            }
            response.setQuestionOptionResponseList(questionOptionResponseList);
        }
        //答案
        response.setAnswer(question.getAnswer());
        //分数
        response.setScore(question.getScore());
        //解析
        response.setComment(question.getComment());
        //难度(0:简单 1:中等 2:困难)
        response.setDifficultyLevel(question.getDifficultyLevel());
        if(!isEmpty(question.getDifficultyLevel())){
            response.setDifficultyLevelName(QuestionDifficultyLevel.getName(question.getDifficultyLevel()));
        }
        response.setCatalog(question.getCatalog());
        if(!isEmpty(question.getCatalog())){
            response.setCatalogName(QuestionCatalogV3.getName(question.getCatalog()));
        }
        if(examPaperQuestionMap != null && examPaperQuestionMap.containsKey(question.getId())){
            if(!isEmpty(examPaperQuestionMap.get(question.getId()).getSort())){
                response.setSort(examPaperQuestionMap.get(question.getId()).getSort());
            }else{
                response.setSort(question.getSort());
            }
            response.setScore(examPaperQuestionMap.get(question.getId()).getScore());
        }else{
            response.setSort(question.getSort());
            response.setScore(question.getScore());
        }
        response.setMark(UserExamPaperRecordDetailMark.N.getCode());
        //如果该试卷该提已做过
        if (userExamPaperRecordDetailMap != null && userExamPaperRecordDetailMap.containsKey(question.getId())) {
            UserExamPaperRecordDetail userExamPaperRecordDetail = userExamPaperRecordDetailMap.get(question.getId());
            //存入已提交答案
            response.setUserAnswer(userExamPaperRecordDetail.getAnswer());
            if(!isEmpty(showUserAnswer) && PublicShow.N.getCode().equals(showUserAnswer)){
                response.setUserAnswer(null);
            }
            //标记状态
            if(showMark){
                response.setMark(userExamPaperRecordDetail.getMark());
            }
            response.setQuestionResult(userExamPaperRecordDetail.getQuestionResult());
            response.setUserExamPaperRecordDetailId(userExamPaperRecordDetail.getId());
            response.setUserScore(userExamPaperRecordDetail.getScore());
            response.setCustomerImgUrls(userExamPaperRecordDetail.getCustomerImgUrls());
            response.setCommentImgUrls(userExamPaperRecordDetail.getCommentImgUrls());
            response.setTeacherComment(userExamPaperRecordDetail.getTeacherComment());
            if(examPaperRecordMap != null && examPaperRecordMap.containsKey(userExamPaperRecordDetail.getUserExamPaperRecordId())){
                response.setExamPaperId(examPaperRecordMap.get(userExamPaperRecordDetail.getUserExamPaperRecordId()).getExamPaperId());
            }
            response.setUserExamPaperRecordDetailId(userExamPaperRecordDetail.getId());
        }
        if(isEmpty(response.getExamPaperId())){
            if(questionFavoriteMap.containsKey(question.getId())){
                response.setExamPaperId(questionFavoriteMap.get(question.getId()).getExamPaperId());
            }else if(examNoteMap.containsKey(question.getId())){
                response.setExamPaperId(examNoteMap.get(question.getId()).getExamPaperId());
            }
        }
        //判断是否收藏(0:收藏 1:未收藏)
        if (questionFavoriteMap.containsKey(question.getId())) {
            response.setCollectState(PublicSwitch.Y.getCode());
        } else {
            response.setCollectState(PublicSwitch.N.getCode());
        }
        //笔记
        UserExamPaperQuestionNoteResponse userExamPaperQuestionNoteResponse = new UserExamPaperQuestionNoteResponse();
        if(examNoteMap.containsKey(question.getId())){
            UserExamPaperQuestionNote userExamPaperQuestionNote = examNoteMap.get(question.getId());
            userExamPaperQuestionNoteResponse.setId(userExamPaperQuestionNote.getId());
            userExamPaperQuestionNoteResponse.setQuestionId(userExamPaperQuestionNote.getQuestionId());
            userExamPaperQuestionNoteResponse.setContent(userExamPaperQuestionNote.getContent());
            userExamPaperQuestionNoteResponse.setSyncCommunityStage(userExamPaperQuestionNote.getSyncCommunityStage());
            if(!isEmpty(userExamPaperQuestionNote.getCreateTime())){
                userExamPaperQuestionNoteResponse.setCreateTime(DateUtil.format(userExamPaperQuestionNote.getCreateTime(),DATETIME_FORMAT));
            }
        }
        response.setExamNoteResponse(userExamPaperQuestionNoteResponse);

        //当做题类型不是模拟考试时 可以自己对简答题评分
        if(makeScore){
            response.setMakeScore(PublicSwitch.Y.getCode());
        }else{
            response.setMakeScore(PublicSwitch.N.getCode());
        }
        if(userExamPaperRecord != null && UserExamPaperRecordCatalog.C2.getCode().equals(userExamPaperRecord.getCatalog())){
            ExamPaper examPaper = examPaperService.findById(userExamPaperRecord.getExamPaperId());
            if(ExamPaperCorrectSwitch.S0.getCode().equals(examPaper.getCorrectSwitch())){
                response.setMakeScore(PublicSwitch.N.getCode());
            }
        }
        //年份
        response.setYear(question.getYear());
        //副考点
        response.setSubExaminationPoint(question.getSubExaminationPoint());
        return response;
    }

    /**
     * 构建树结构
     */
    private List<QuestionAnalyticResponse> buildQuestionTree(List<QuestionAnalyticResponse> list, Integer pid,Integer sortType) {
        List<QuestionAnalyticResponse> tree = new ArrayList<>();
        for (QuestionAnalyticResponse node : list) {
            if (pid.equals(node.getParentId())) {
                tree.add(findChild(node, list));
            }
        }


        if(sortType == 0){
            tree.sort(Comparator.nullsLast(Comparator.comparingInt(QuestionAnalyticResponse::getSort)).thenComparing(QuestionAnalyticResponse::getId));
        }else{
            tree.sort(Comparator.nullsLast(Comparator.comparing(QuestionAnalyticResponse::getType)).thenComparing(QuestionAnalyticResponse::getId));
        }
        return tree;
    }

    /**
     * 递归构建子级树结构
     */
    private QuestionAnalyticResponse findChild(QuestionAnalyticResponse node, List<QuestionAnalyticResponse> list) {
        node.setQuestionResponseChildren(new ArrayList<>());

        for (QuestionAnalyticResponse item : list) {
            if (node.getId().equals(item.getParentId())) {
                node.getQuestionResponseChildren().add(findChild(item, list));
            }
        }
        if (node.getQuestionResponseChildren() != null && !node.getQuestionResponseChildren().isEmpty()) {
            node.getQuestionResponseChildren().sort(Comparator.nullsLast(Comparator.comparing(QuestionAnalyticResponse::getSort)).thenComparing(QuestionAnalyticResponse::getId));
            BigDecimal userScore = BigDecimal.ZERO;
            for (QuestionAnalyticResponse questionResponseChild : node.getQuestionResponseChildren()) {
                userScore = userScore.add(isEmpty(questionResponseChild.getUserScore()) ? BigDecimal.ZERO : questionResponseChild.getUserScore());
            }
            node.setUserScore(userScore);
        }
        return node;
    }
    /**
     * 查询上次做题练习
     *
     * <AUTHOR>
     */
    @RequestMapping(value = "/v1/user/exam/paper/record/last/query",method = RequestMethod.POST)
    @Token
    public Response<?> queryLastUserExamPaperRecord(@RequestBody ExamPaperRequest request) {
        try {
            UserToken userToken = getUserToken();
            if(isEmpty(request.getProjectId())){
                return new Response<>(ERROR,"项目编号不能为空");
            }
            UserExamPaperRecordResponse response = new UserExamPaperRecordResponse();
            //科目编号不为空 或试卷类型编号不为空
            List<Integer> examPaperIds = new ArrayList<>();
            if(!isEmpty(request.getProjectItemId()) || !isEmpty(request.getExamTypeId())){
                ExamPaperQuery examPaperQuery = new ExamPaperQuery();
                examPaperQuery.setStatus(DataStatus.Y.getCode());
                examPaperQuery.setExamTypeId(request.getExamTypeId());
                examPaperQuery.setProjectItemId(request.getProjectItemId());
                List<ExamPaper> examPaperList = examPaperService.findAll(examPaperQuery);
                examPaperIds = examPaperList.stream().map(ExamPaper::getId).collect(Collectors.toList());
            }

            //查询做题记录
            UserExamPaperRecordQuery userExamPaperRecordQuery = new UserExamPaperRecordQuery();
            userExamPaperRecordQuery.setCustomerId(userToken.getCustomerId());
            userExamPaperRecordQuery.setProjectId(request.getProjectId());
            userExamPaperRecordQuery.setOver(UserExamPaperRecordOver.N.getCode());
            if(examPaperIds.size() > 0){
                userExamPaperRecordQuery.setExamPaperIds(examPaperIds);
                userExamPaperRecordQuery.setCatalogs(QuestionArguments.SYS_EXAM_PAPER_CATALOG_CODE_LIST);
            }
            userExamPaperRecordQuery.setStatus(DataStatus.Y.getCode());
            List<UserExamPaperRecord> userExamPaperRecordList = userExamPaperRecordService.findAll(userExamPaperRecordQuery);
            if(!isEmpty(userExamPaperRecordList) && !userExamPaperRecordList.isEmpty()){
                userExamPaperRecordList = userExamPaperRecordList.stream().filter(userExamPaperRecord -> {
                    String catalog = userExamPaperRecord.getCatalog();
                    //历年真题-标签组卷不再返回
                    if (UserExamPaperRecordCatalog.C7.getCode().equals(catalog)){
                        return false;
                    }
                    //单体练习||星级组卷 不再返回
                    String playType = userExamPaperRecord.getPlayType();
                    if (UserExamPaperRecordPlayType.C1.getCode().equals(playType) || UserExamPaperRecordPlayType.C2.getCode().equals(playType)){
                        return false;
                    }
                    return true;
                }).collect(Collectors.toList());
                if (userExamPaperRecordList.isEmpty()){
                    return new Response<>(OK, SUCCESS);
                }
                //做题记录按更新时间降序排列后取第一条记录
                UserExamPaperRecord userExamPaperRecord = userExamPaperRecordList.stream().sorted(Comparator.comparing(UserExamPaperRecord::getModifyTime).reversed()).collect(Collectors.toList()).get(0);
                String examPaperName = null;
                //自己组的试卷
                if(QuestionArguments.USER_EXAM_PAPER_CATALOG_CODE_LIST.contains(userExamPaperRecord.getCatalog())){
                    UserExamPaper userExamPaper = userExamPaperService.findById(userExamPaperRecord.getExamPaperId());
                    examPaperName = isEmpty(userExamPaper) ? null : userExamPaper.getName();
                }
                //是否需要自动批改 模拟考试全部走新的批阅接口,其他的根据试卷的标签去判断,标签(或者父标签)名字中包含"主观题"的,也走新的交卷接口,其他的都走老接口
                boolean needAutoRead = false;
                if(QuestionArguments.SYS_EXAM_PAPER_CATALOG_CODE_LIST.contains(userExamPaperRecord.getCatalog())){
                    ExamPaper examPaper = examPaperService.findById(userExamPaperRecord.getExamPaperId());
                    examPaperName = isEmpty(examPaper) ? null : examPaper.getName();
                    if (UserExamPaperRecordCatalog.C2.getCode().equals(userExamPaperRecord.getCatalog())){
                        needAutoRead = true;
                    } else {
                        if (!isEmpty(examPaper) && !isEmpty(examPaper.getQuestionLabelId())){
                            QuestionLabel questionLabel = questionLabelService.findById(examPaper.getQuestionLabelId());
                            //当前关联的标签名字中包含"主观题"
                            if (!isEmpty(questionLabel) && !DataStatus.N.getCode().equals(questionLabel.getStatus()) && !isEmpty(questionLabel.getName()) && questionLabel.getName().contains(QuestionArguments.EXAM_ALLOW_SINGLE_QUESTION_LABEL_NAME)){
                                needAutoRead = true;
                            } else {
                                //当前标签不是根根标签,他父级标签的名字中包含"主观题"
                                if (!isEmpty(questionLabel.getParentId()) && 0 != questionLabel.getParentId()){
                                    QuestionLabel questionLabelRoot = questionLabelService.findById(questionLabel.getParentId());
                                    if (!isEmpty(questionLabelRoot) && !DataStatus.N.getCode().equals(questionLabelRoot.getStatus()) && !isEmpty(questionLabelRoot.getName()) && questionLabelRoot.getName().contains(QuestionArguments.EXAM_ALLOW_SINGLE_QUESTION_LABEL_NAME)){
                                        needAutoRead = true;
                                    }
                                }
                            }
                        }
                    }
                }
                //false:走老接口(submit)   true:走新接口(create),批改试卷
                response.setNeedAutoRead(needAutoRead);
                //封装返会结果
                response.setId(userExamPaperRecord.getId());
                response.setExamPaperId(userExamPaperRecord.getExamPaperId());
                response.setExamPaperName(examPaperName);
                response.setCatalog(userExamPaperRecord.getCatalog());
                if(!isEmpty(userExamPaperRecord.getCatalog())){
                    response.setCatalogName(UserExamPaperRecordCatalog.getName(userExamPaperRecord.getCatalog()));
                }
                response.setLastExerciseQuestion(userExamPaperRecord.getLastExerciseQuestion());
                response.setMode(userExamPaperRecord.getMode());
                response.setModifyTime(DateUtil.format(userExamPaperRecord.getModifyTime(),DATETIME_FORMAT));
            }
            return new Response<>(response);
        }catch (Exception e){
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }
    /**
     * 批阅记录导出
     *
     * <AUTHOR>
     */
    @RequestMapping(value = "/v1/user/exam/paper/record/report",method = RequestMethod.POST)
    @Token
    public Response<?> queryLastUserExamPaperRecordReport(@RequestBody UserExamPaperRecordRequest request) {
        try {
            Date datetime = this.getServerTime();
            UserExamPaperRecordExport userExamPaperRecordExport = new UserExamPaperRecordExport();

            String code = ExportSequence.get();

            //记录导出日志
            ExportLog exportLog = new ExportLog();
            exportLog.setName("CRM系统_" + ExportType.C45.getName());
            exportLog.setCode(code);
            exportLog.setAction(ExportAction.E.getCode());
            exportLog.setExpireTime(DateUtil.getFutureMonth(datetime, 1));
            exportLog.setStage(ExportStage.WAIT.getCode());
            exportLog.setSysUserId(this.getUserToken().getId());
            exportLog.setStatus(com.api.constant.DataStatus.Y.getCode());
            exportLog.setCreateTime(datetime);
            exportLog.setModifyTime(datetime);
            exportLogService.create(exportLog);

            //是否批阅(0:是 1:否)
            userExamPaperRecordExport.setReadOver(request.getReadOver());

            //是否批阅(0:是 1:否)
            userExamPaperRecordExport.setReadOver(request.getReadOver());
            //项目编号
            userExamPaperRecordExport.setProjectId(request.getProjectId());
            //考卷编号
            userExamPaperRecordExport.setExamPaperId(request.getExamPaperId());
            //批阅人编号
            userExamPaperRecordExport.setReadOverSysUserId(request.getReadOverSysUserId());
            //学员名称
            if(!isEmpty(request.getKeyword())){
                Customer customer = customerService.findByUsername(request.getKeyword());
                if(customer != null){
                    userExamPaperRecordExport.setCustomerId(customer.getId());
                }
            }
            // 交卷时间
            if (request.getSubmitTime() != null && request.getSubmitTime().size() > 0) {
                userExamPaperRecordExport.setMinSubmitTime((DateUtil.parse(request.getSubmitTime().get(0), DATETIME_FORMAT)));
                userExamPaperRecordExport.setMaxSubmitTime((DateUtil.parse(request.getSubmitTime().get(1), DATETIME_FORMAT)));
            }
            userExamPaperRecordExport.setExportLogId(exportLog.getId());
            userExamPaperRecordExport.setCode(code);
            userExamPaperRecordExport.setType(ExportType.C45.getCode());

            String key = CacheKey.FILE_DOWNLOAD_URL + code;
            redisTemplate.opsForValue().set(key, ExportStage.WAIT.getCode(), 1, TimeUnit.DAYS);
            MQProducer.INSTANCE.sendOneway(code, MQTopic.LXFK_API, MQTag.LXFK_API_EXPORT_MESSAGE, this.getJSON(userExamPaperRecordExport));
            return new Response<>(code);

        }catch (Exception e){
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }
    /**
     * 做题记录错题重做
     *
     * <AUTHOR>
     */
    @RequestMapping(value = "/v1/exam/paper/record/error/question/redo")
    @Token
    public Response<?> redoExamPaperRecordErrorQuestion(@RequestBody UserExamPaperRecordRequest request){
        try{
            //1.1 校验 交卷基础信息
            UserExamPaperRecordValidator validator = new UserExamPaperRecordValidator();
            if (!validator.onUserExamRecordDetailRequests(request.getUserExamPaperRecordDetailRequests()).result()){
                return new Response<>(ERROR,validator.getErrorMessage());
            }
            List<UserExamPaperRecordDetailRequest> userExamPaperRecordDetailRequests = request.getUserExamPaperRecordDetailRequests();
            UserExamPaperRecordDetailValidator detailValidator;
            for (UserExamPaperRecordDetailRequest userExamPaperRecordDetailRequest : userExamPaperRecordDetailRequests) {
                detailValidator = new UserExamPaperRecordDetailValidator();
                if (!detailValidator
                        .onId(userExamPaperRecordDetailRequest.getId())
                        .onQuestionId(userExamPaperRecordDetailRequest.getQuestionId())
                        .result()){
                    return new Response<>(ERROR, detailValidator.getErrorMessage());
                }
            }
            Set<Integer> questionIds = new HashSet<>();
            List<Integer> userExamPaperRecordDetailIds = new ArrayList<>();
            Map<Integer,UserExamPaperRecordDetailRequest> userExamPaperRecordDetailRequestMap = new HashMap<>();
            for (UserExamPaperRecordDetailRequest userExamPaperRecordDetailRequest : userExamPaperRecordDetailRequests) {
                questionIds.add(userExamPaperRecordDetailRequest.getQuestionId());
                userExamPaperRecordDetailIds.add(userExamPaperRecordDetailRequest.getId());
                userExamPaperRecordDetailRequestMap.put(userExamPaperRecordDetailRequest.getId(),userExamPaperRecordDetailRequest);
            }
            //查询做题记录详情
            UserExamPaperRecordDetailQuery userExamPaperRecordDetailQuery = new UserExamPaperRecordDetailQuery();
            userExamPaperRecordDetailQuery.setIds(userExamPaperRecordDetailIds);
            userExamPaperRecordDetailQuery.setStatus(DataStatus.Y.getCode());
            List<UserExamPaperRecordDetail> userExamPaperRecordDetails = userExamPaperRecordDetailService.findAll(userExamPaperRecordDetailQuery);
            Set<Integer> userExamPaperRecordIds = new HashSet<>();
            Map<Integer, UserExamPaperRecordDetail> userExamPaperRecordDetailMap = new HashMap<>();
            Map<Integer, List<UserExamPaperRecordDetail>> userExamPaperRecordDetailGroupMap = new HashMap<>();

            for (UserExamPaperRecordDetail userExamPaperRecordDetail : userExamPaperRecordDetails) {
                userExamPaperRecordIds.add(userExamPaperRecordDetail.getUserExamPaperRecordId());
                userExamPaperRecordDetailMap.put(userExamPaperRecordDetail.getId(),userExamPaperRecordDetail);
                if(userExamPaperRecordDetailGroupMap.containsKey(userExamPaperRecordDetail.getUserExamPaperRecordId())){
                    userExamPaperRecordDetailGroupMap.get(userExamPaperRecordDetail.getUserExamPaperRecordId()).add(userExamPaperRecordDetail);
                }else{
                    List<UserExamPaperRecordDetail> userExamPaperRecordDetailList = new ArrayList<>();
                    userExamPaperRecordDetailList.add(userExamPaperRecordDetail);
                    userExamPaperRecordDetailGroupMap.put(userExamPaperRecordDetail.getUserExamPaperRecordId(),userExamPaperRecordDetailList);
                }
            }

            //查询题目信息
            List<Question> questionList = questionService.findByIds(new ArrayList<>(questionIds));
            Map<Integer, Question> questionMap = questionList.stream().collect(Collectors.toMap(Question::getId, Function.identity()));

            //查询做题记录信息
            UserExamPaperRecordQuery userExamPaperRecordQuery = new UserExamPaperRecordQuery();
            userExamPaperRecordQuery.setStatus(DataStatus.Y.getCode());
            userExamPaperRecordQuery.setIds(new ArrayList<>(userExamPaperRecordIds));
            List<UserExamPaperRecord> userExamPaperRecordList = userExamPaperRecordService.findAll(userExamPaperRecordQuery);

            //用户组卷的做题记录id
            List<Integer> combinationExamPaperRecordIds = new ArrayList<>();
            //系统卷的做题记录id
            List<Integer> sysExamPaperRecordIds = new ArrayList<>();
            //用户组卷id
            List<Integer> combinationExamPaperIds = new ArrayList<>();
            //系统卷的id
            List<Integer> sysExamPaperIds = new ArrayList<>();
            //做题记录map
            Map<Integer, UserExamPaperRecord> userExamPaperRecordMap = new HashMap<>();

            for (UserExamPaperRecord userExamPaperRecord : userExamPaperRecordList) {
                userExamPaperRecordMap.put(userExamPaperRecord.getId(),userExamPaperRecord);
                //用户自己组卷的类型
                if(QuestionArguments.USER_EXAM_PAPER_CATALOG_CODE_LIST.contains(userExamPaperRecord.getCatalog())){
                    combinationExamPaperRecordIds.add(userExamPaperRecord.getId());
                    combinationExamPaperIds.add(userExamPaperRecord.getExamPaperId());
                }
                //系统卷
                if(QuestionArguments.SYS_EXAM_PAPER_CATALOG_CODE_LIST.contains(userExamPaperRecord.getCatalog())){
                    sysExamPaperRecordIds.add(userExamPaperRecord.getId());
                    sysExamPaperIds.add(userExamPaperRecord.getExamPaperId());
                }
            }
            Map<Integer, Map<Integer, ExamPaperQuestion>> sysExamPaperQuestionMap = new HashMap<>();
            Map<Integer, Map<Integer, UserExamPaperQuestion>> combinationExamPaperQuestionMap = new HashMap<>();
            //查询试卷与题目的绑定关系表
            if(!sysExamPaperIds.isEmpty()){
                ExamPaperQuestionQuery examPaperQuestionQuery = new ExamPaperQuestionQuery();
                examPaperQuestionQuery.setStatus(DataStatus.Y.getCode());
                examPaperQuestionQuery.setExamPaperIds(sysExamPaperIds);
                List<ExamPaperQuestion> examPaperQuestionList = examPaperQuestionService.findAll(examPaperQuestionQuery);
                sysExamPaperQuestionMap = examPaperQuestionList.stream()
                        .collect(Collectors.groupingBy(ExamPaperQuestion::getExamPaperId,
                                Collectors.toMap(ExamPaperQuestion::getQuestionId,
                                        Function.identity(),
                                        (a, b) -> a)));
            }
            if(!combinationExamPaperIds.isEmpty()){
                UserExamPaperQuestionQuery userExamPaperQuestionQuery = new UserExamPaperQuestionQuery();
                userExamPaperQuestionQuery.setStatus(DataStatus.Y.getCode());
                userExamPaperQuestionQuery.setExamPaperIds(combinationExamPaperIds);
                List<UserExamPaperQuestion> userExamPaperQuestionList = userExamPaperQuestionService.findAll(userExamPaperQuestionQuery);
                combinationExamPaperQuestionMap = userExamPaperQuestionList.stream()
                        .collect(Collectors.groupingBy(UserExamPaperQuestion::getExamPaperId,
                                Collectors.toMap(UserExamPaperQuestion::getQuestionId,
                                        Function.identity(),
                                        (a, b) -> a)));
            }

            //修改做题记录   答案  得分  对错状态值
            List<UserExamPaperRecordDetail> userExamPaperRecordDetailModifyList = new ArrayList<>();
            List<UserExamPaperRecord> examPaperRecordModifyList = new ArrayList<>();
            for (Integer userExamPaperRecordId : userExamPaperRecordDetailGroupMap.keySet()) {

                UserExamPaperRecord userExamPaperRecord = userExamPaperRecordMap.get(userExamPaperRecordId);

                //获取属于同一个做题记录的做题记录详情
                List<UserExamPaperRecordDetail> examPaperRecordDetails = userExamPaperRecordDetailGroupMap.get(userExamPaperRecordId);
                //累加的分数
                BigDecimal sumScoreSummation = BigDecimal.ZERO;
                //累加的做题数量
                int playQuestionNumberSummation = 0;
                for (UserExamPaperRecordDetail userExamPaperRecordDetail : examPaperRecordDetails) {
                    Question question = questionMap.get(userExamPaperRecordDetail.getQuestionId());
                    if(question != null && userExamPaperRecordDetailRequestMap.containsKey(userExamPaperRecordDetail.getId())){
                        UserExamPaperRecordDetail userExamPaperRecordDetailModify = new UserExamPaperRecordDetail();
                        UserExamPaperRecordDetailRequest userExamPaperRecordDetailRequest = userExamPaperRecordDetailRequestMap.get(userExamPaperRecordDetail.getId());
                        if(!isEmpty(userExamPaperRecordDetailRequest.getAnswer())){
                            //如果原来的做题记录详情 用户答案为空,那么做题数量 累加
                            if(isEmpty(userExamPaperRecordDetail.getAnswer()) ){
                                playQuestionNumberSummation = playQuestionNumberSummation +1;
                            }
                            //如果做对 了 sum_score 加上题目分值
                            if(question.getAnswer().equals(userExamPaperRecordDetailRequest.getAnswer())){
                                //区分系统卷和用户组的卷
                                if(userExamPaperRecord != null){
                                    BigDecimal score = null;
                                    if(QuestionArguments.USER_EXAM_PAPER_CATALOG_CODE_LIST.contains(userExamPaperRecord.getCatalog())){
                                        Map<Integer, UserExamPaperQuestion> userExamPaperQuestionMap = combinationExamPaperQuestionMap.get(userExamPaperRecord.getExamPaperId());
                                        if(userExamPaperQuestionMap != null && userExamPaperQuestionMap.get(question.getId()) != null){
                                            score = userExamPaperQuestionMap.get(question.getId()).getScore();
                                        }
                                    }
                                    if(QuestionArguments.SYS_EXAM_PAPER_CATALOG_CODE_LIST.contains(userExamPaperRecord.getCatalog())){
                                        Map<Integer, ExamPaperQuestion> examPaperQuestionMap = sysExamPaperQuestionMap.get(userExamPaperRecord.getExamPaperId());
                                        if(examPaperQuestionMap != null && examPaperQuestionMap.get(question.getId()) != null){
                                            score = examPaperQuestionMap.get(question.getId()).getScore();
                                        }
                                    }
                                    if(score != null){
                                        userExamPaperRecordDetailModify.setScore(score);
                                        sumScoreSummation = sumScoreSummation.add(score);
                                    }
                                }
                                userExamPaperRecordDetailModify.setQuestionResult(UserExamPaperRecordDetailQuestionResult.Y.getCode());
                            }
                        }
                        userExamPaperRecordDetailModify.setId(userExamPaperRecordDetail.getId());
                        userExamPaperRecordDetailModify.setAnswer(userExamPaperRecordDetailRequest.getAnswer());
                        userExamPaperRecordDetailModify.setDuration(userExamPaperRecordDetailRequest.getDuration());
                        userExamPaperRecordDetailModifyList.add(userExamPaperRecordDetailModify);
                    }
                }
                //更新做题记录信息
                /**
                 * 1.如果原来的做题记录详情 用户答案为空,那么做题数量 累加
                 * 2.如果做对 了 sum_score 加上题目分值
                 * 更新 正确率（基础题型中做对的数量/基础题型数量  =  试卷正确率）(所有可以判断对错的题目)
                 *
                 * 更新得分率 试卷得分率: 总得分 / 总分  (不区分大小题)
                 */

                if(userExamPaperRecord != null){
                    //做题记录总分
                    BigDecimal allScore = userExamPaperRecord.getAllScore();
                    //做题记录得分
                    BigDecimal sumScore = userExamPaperRecord.getSumScore();
                    UserExamPaperRecord userExamPaperRecordModify = new UserExamPaperRecord();
                    userExamPaperRecordModify.setId(userExamPaperRecord.getId());
                    //用户得分
                    if(sumScoreSummation.compareTo(BigDecimal.ZERO) >0){
                        sumScore = sumScore.add(sumScoreSummation);
                        userExamPaperRecordModify.setSumScore(sumScore);
                        //得分率
                        BigDecimal scoreRate = sumScore.equals(BigDecimal.ZERO) ? BigDecimal.ZERO : sumScore.divide(allScore, 2, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100));
                        userExamPaperRecordModify.setScoreRate(scoreRate);
                    }
                    //做题数量
                    if(playQuestionNumberSummation > 0){
                        userExamPaperRecordModify.setPlayQuestionNumber(userExamPaperRecord.getPlayQuestionNumber() + playQuestionNumberSummation);
                    }
                    //正确率
                    examPaperRecordModifyList.add(userExamPaperRecordModify);
                }
            }
            userExamPaperRecordService.modifyRedoWrongQuestion(userExamPaperRecordDetailModifyList,examPaperRecordModifyList);
            return new Response<>();
        }catch (Exception e){
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }

    /**
     * 学习榜单查询-做题榜
     * <AUTHOR>
     */
    @RequestMapping(value = "/v1/user/exam/paper/record/rank/query")
    @Token
    public Response<?> queryUserExamPaperRecordRank(@RequestBody UserExamPaperRecordRequest request){
        try {
            Date serverTime = getServerTime();
            UserExamPaperRecordValidator validator = new UserExamPaperRecordValidator();
            if (!validator.onProjectId(request.getProjectId()).onRange(request.getRange()).result()){
                return new Response<>(ERROR, validator.getErrorMessage());
            }
            Date minStartTime = null;
            switch (request.getRange()){
                //查询本周
                case "0":
                    minStartTime = DateUtil.getWeekStartTime(serverTime);
                    break;
                //查询本月
                case "1":
                    minStartTime = DateUtil.getMonthStartTime(serverTime);
                    break;
            }

            UserToken userToken = this.getUserToken();
            Customer customer = customerService.findById(userToken.getCustomerId());

            UserLearningDayStatisticQuery userLearningDayStatisticQuery = new UserLearningDayStatisticQuery();
            userLearningDayStatisticQuery.setProjectId(request.getProjectId());
            userLearningDayStatisticQuery.setCustomerId(userToken.getCustomerId());

            userLearningDayStatisticQuery.setMinDate(minStartTime);
            userLearningDayStatisticQuery.setStatus(DataStatus.Y.getCode());
            List<UserLearningDayStatistic> learningRankingCustomerList = userLearningDayStatisticService.findQuestionRanking(userLearningDayStatisticQuery);

            int totalQuestionNum = 0;
            for (UserLearningDayStatistic userLearningDayStatistic : learningRankingCustomerList) {
                if(!isEmpty(userLearningDayStatistic.getQuestionCount())){
                    totalQuestionNum += userLearningDayStatistic.getQuestionCount();
                }
            }

            CustomerRankingListResponse rankingResponse = new CustomerRankingListResponse();
            rankingResponse.setRanking("未上榜");
            if(customer != null){
                //学员信息
                rankingResponse.setAvatar(customer.getAvatar());
                rankingResponse.setUserName(this.getMobile(customer.getUsername()));
            }
            rankingResponse.setDoQuestionNumber(totalQuestionNum +"题");
            rankingResponse.setCustomerRankingList(new ArrayList<>());
            userLearningDayStatisticQuery.setCustomerId(null);
            userLearningDayStatisticQuery.setLimit(100);
            userLearningDayStatisticQuery.setStart(0);
            userLearningDayStatisticQuery.setQuestionSort("1");

            List<UserLearningDayStatistic> learningRankingList = userLearningDayStatisticService.findLearningRanking(userLearningDayStatisticQuery);

            if(learningRankingList.isEmpty()){
                return new Response<>(OK,SUCCESS,rankingResponse);
            }

            Set<Integer> customerIdSet = learningRankingList.stream().map(UserLearningDayStatistic::getCustomerId).collect(Collectors.toSet());

            List<Customer> customerList = customerService.findByIds(new ArrayList<>(customerIdSet));
            Map<Integer, Customer> customerMap = customerList.stream().collect(Collectors.toMap(Customer::getId, Function.identity()));
            int rankNumber = 1;

            for (UserLearningDayStatistic userLearningDayStatistic : learningRankingList) {
                Integer doQuestionNumber = userLearningDayStatistic.getQuestionCount();
                if(doQuestionNumber == 0){
                    break;
                }
                Integer customerId = userLearningDayStatistic.getCustomerId();
                if(customerMap.containsKey(customerId)){
                    CustomerRankingListResponse customerRankingListResponse = new CustomerRankingListResponse();
                    //学员信息
                    Customer entryCustomer = customerMap.get(customerId);
                    customerRankingListResponse.setAvatar(entryCustomer.getAvatar());
                    customerRankingListResponse.setUserName(this.getMobile(entryCustomer.getUsername()));
                    customerRankingListResponse.setDoQuestionNumber(String.valueOf(doQuestionNumber));
                    String ranking = String.valueOf(rankNumber);
                    customerRankingListResponse.setRanking(ranking);
                    rankingResponse.getCustomerRankingList().add(customerRankingListResponse);
                    if(this.getUserToken().getCustomerId().equals(customerId)){
                        rankingResponse.setRanking("第"+ranking+"名");
                        rankingResponse.setDoQuestionNumber(String.valueOf(doQuestionNumber));
                    }
                    rankNumber ++;
                }
            }

            return new Response<>(OK,SUCCESS,rankingResponse);
        }catch (Exception e){
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }
    /**
     * 学员各科目学习数据统计
     */
    @Token
    @RequestMapping("/v1/user/exam/paper/record/item/query")
    public Response<?> queryItemUserExamPaperRecord(@RequestBody UserExamPaperRecordRequest request) {
        try {
            Integer customerId = this.getUserToken().getCustomerId();
            UserExamPaperRecordValidator validator = new UserExamPaperRecordValidator();
            if (!validator.onProjectId(request.getProjectId()).result()){
                return new Response<>(ERROR,validator.getErrorMessage());
            }

            List<ProjectItem> projectItemList = new ArrayList<>();
            OrderProductResponseV3 orderProductResponseV3 = queryUserPurchasedProductDetail(request.getProjectId(), customerId);
            if(isEmpty(orderProductResponseV3.getProjectItemAllList()) || orderProductResponseV3.getProjectItemAllList().isEmpty()){
                //查询项目下所有的科目
                ProjectItemQuery projectItemQuery = new ProjectItemQuery();
                projectItemQuery.setProjectId(request.getProjectId());
                projectItemQuery.setStage(PublicStage.Y.getCode());
                projectItemQuery.setStatus(com.api.constant.DataStatus.Y.getCode());
                projectItemList = projectItemService.findAll(projectItemQuery);
            }else{
                projectItemList = orderProductResponseV3.getProjectItemAllList();
                List<Integer> projectItemIds = projectItemList.stream().map(ProjectItem::getId).collect(Collectors.toList());

                //根据科目id查询绑定科目的标签
                QuestionLabelItemQuery questionLabelItemQuery = new QuestionLabelItemQuery();
                questionLabelItemQuery.setProjectItemIds(projectItemIds);
                questionLabelItemQuery.setStatus(DataStatus.Y.getCode());
                List<QuestionLabelItem> questionLabelItemList = questionLabelItemService.findAll(questionLabelItemQuery);
                Set<Integer> questionLabelIdSet = questionLabelItemList.stream().map(QuestionLabelItem::getQuestionLabelId).collect(Collectors.toSet());
                questionLabelItemQuery.setProjectItemIds(null);
                questionLabelItemQuery.setQuestionLabelIds(new ArrayList<>(questionLabelIdSet));
                List<QuestionLabelItem> questionLabelItems = questionLabelItemService.findAll(questionLabelItemQuery);
                Set<Integer> projectItemIdSet = questionLabelItems.stream().map(QuestionLabelItem::getProjectItemId).collect(Collectors.toSet());

                ProjectItemQuery projectItemQuery = new ProjectItemQuery();
                projectItemQuery.setStatus(DataStatus.Y.getCode());
                projectItemQuery.setIds(new ArrayList<>(projectItemIdSet));
                projectItemList = projectItemService.findAll(projectItemQuery);
            }
            //查询用户做题记录
            UserExamPaperRecordQuery userExamPaperRecordQuery = new UserExamPaperRecordQuery();
            userExamPaperRecordQuery.setProjectId(request.getProjectId());
            userExamPaperRecordQuery.setCustomerId(customerId);
            userExamPaperRecordQuery.setStatus(DataStatus.Y.getCode());
            List<UserExamPaperRecord> examPaperRecordList = userExamPaperRecordService.findAll(userExamPaperRecordQuery);

            List<UserExamPaperRecordQuery> userExamPaperRecordListAll = new ArrayList<>();
            if(!isEmpty(examPaperRecordList) && !examPaperRecordList.isEmpty()){
                List<Integer> userExamPaperRecordIdOverYesList = new ArrayList<>();
                List<Integer> userExamPaperRecordIdOverNoList = new ArrayList<>();
                for (UserExamPaperRecord userExamPaperRecord : examPaperRecordList) {
                    //区分交卷状态和未交卷状态
                    if(UserExamPaperRecordOver.Y.getCode().equals(userExamPaperRecord.getOver())){
                        userExamPaperRecordIdOverYesList.add(userExamPaperRecord.getId());
                    }else{
                        userExamPaperRecordIdOverNoList.add(userExamPaperRecord.getId());
                    }
                }

                UserExamPaperRecordDetailQuery userExamPaperRecordDetailQuery = new UserExamPaperRecordDetailQuery();
                userExamPaperRecordDetailQuery.setStatus(DataStatus.Y.getCode());
                //交卷的做题记录
                if(!userExamPaperRecordIdOverYesList.isEmpty()){
                    userExamPaperRecordDetailQuery.setUserExamPaperRecordIds(userExamPaperRecordIdOverYesList);
                    List<UserExamPaperRecordQuery> userExamPaperRecordYesList = userExamPaperRecordDetailService.findOverGroupByItemId(userExamPaperRecordDetailQuery);
                    userExamPaperRecordListAll.addAll(userExamPaperRecordYesList);
                }
                if(!userExamPaperRecordIdOverNoList.isEmpty()){
                    userExamPaperRecordDetailQuery.setHaveAnswer("true");
                    userExamPaperRecordDetailQuery.setUserExamPaperRecordIds(userExamPaperRecordIdOverNoList);
                    List<UserExamPaperRecordQuery> userExamPaperRecordNoList = userExamPaperRecordDetailService.findOverGroupByItemId(userExamPaperRecordDetailQuery);
                    userExamPaperRecordListAll.addAll(userExamPaperRecordNoList);
                }
            }
            //做题记录按题目归属科目分组
            Map<Integer, Integer> itemQuestionNumMap = userExamPaperRecordListAll.stream().collect(Collectors.groupingBy(UserExamPaperRecordQuery::getProjectItemId, Collectors.summingInt(UserExamPaperRecordQuery::getQuestionNumber)));

            //封装数据
            List<ProjectItemDataResponse> responses = new ArrayList<>();
            for (ProjectItem projectItem : projectItemList) {
                ProjectItemDataResponse projectItemDataResponse = new ProjectItemDataResponse();
                projectItemDataResponse.setProjectItemId(projectItem.getId());
                projectItemDataResponse.setProjectItemName(projectItem.getName());
                int questionNumber = 0;
                if(itemQuestionNumMap.containsKey(projectItem.getId())){
                    questionNumber = itemQuestionNumMap.get(projectItem.getId());
                }
                projectItemDataResponse.setQuestionNumber(questionNumber);
                responses.add(projectItemDataResponse);
            }
            return new Response<>(OK,SUCCESS,responses);
        }catch (Exception e){
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }

    }

    /**
     * 做题错题报告
     */
    @Token
    @RequestMapping("/v1/user/exam/paper/record/wrong/question/report/query")
    public Response<?> queryUserExamPaperRecordWrongQuestionReport(@RequestBody UserExamPaperRecordRequest request) {
        try {
            UserToken userToken = this.getUserToken();
            if (isEmpty(request.getProjectItemId())) {
                return new Response<>(ERROR, "科目编号不能为空");
            }
            //查询科目信息
            ProjectItem projectItem = projectItemService.findById(request.getProjectItemId());
            if (isEmpty(projectItem) || DataStatus.N.getCode().equals(projectItem.getStatus())) {
                return new Response<>(ERROR, "科目不存在");
            }
            //查询科目下的题目信息
            QuestionQuery questionQuery = new QuestionQuery();
            questionQuery.setTypes(QuestionArguments.CAN_DETERMINE_ANSWER_RIGHT_OR_WRONG);
            questionQuery.setProjectItemId(projectItem.getId());
            questionQuery.setStatus(DataStatus.Y.getCode());
            questionQuery.setParentId(0);
            List<Question> questionList = questionService.findAll(questionQuery);
            if (isEmpty(questionList) || questionList.isEmpty()) {
                return new Response<>(ERROR, "该科目下暂时没有题目");
            }

            List<Integer> questionIds = questionList.stream().map(Question::getId).collect(Collectors.toList());

            ExamPaperRecordWrongReportResponse examPaperRecordWrongReportResponse = new ExamPaperRecordWrongReportResponse();

            //查询用户信息
            Customer customer = customerService.findById(userToken.getCustomerId());
            String userName = customer.getName();
            //用户名为空就返回加密手机号
            if (isEmpty(customer.getName()) && !isEmpty(customer.getUsername())) {
                userName = this.getMobile(customer.getUsername());
            }

            examPaperRecordWrongReportResponse.setUserName(userName);

            //查询用户的错题记录
            UserExamPaperRecordQuery userExamPaperRecordQuery = new UserExamPaperRecordQuery();
            userExamPaperRecordQuery.setProjectId(projectItem.getProjectId());
            userExamPaperRecordQuery.setCustomerId(userToken.getCustomerId());
            userExamPaperRecordQuery.setOver(UserExamRecordOver.Y.getCode());
            userExamPaperRecordQuery.setStatus(DataStatus.Y.getCode());
            List<UserExamPaperRecord> userExamPaperRecordList = userExamPaperRecordService.findAll(userExamPaperRecordQuery);

            if (!isEmpty(userExamPaperRecordList) && !userExamPaperRecordList.isEmpty()) {
                //查询用户最早的做题记录的
                userExamPaperRecordQuery.setOver(null);
                UserExamPaperRecord userExamPaperRecordEarliest = userExamPaperRecordService.findEarliestRecordByCustomer(userExamPaperRecordQuery);

                if (!isEmpty(userExamPaperRecordEarliest)) {
                    examPaperRecordWrongReportResponse.setStartTime(DateUtil.format(userExamPaperRecordEarliest.getCreateTime(), CHINESE_DATE_FORMAT));
                    String tipMessage = String.format("亲爱的%s学员你好，自%s至今，您刷题频率较高，刷题的同时别忘了查漏补缺哦", examPaperRecordWrongReportResponse.getUserName(), examPaperRecordWrongReportResponse.getStartTime());
                    examPaperRecordWrongReportResponse.setTipMessage(tipMessage);
                }
                examPaperRecordWrongReportResponse.setChapterExamPaperRecordDetailResponses(new ArrayList<>());


                //按修改时间降序排序
                List<UserExamPaperRecord> sortUserExamPaperRecordList = userExamPaperRecordList.stream().sorted(Comparator.comparing(UserExamPaperRecord::getModifyTime).reversed()).collect(Collectors.toList());
                //同一个试卷多次做题记录只取最新的一次
                List<UserExamPaperRecord> userExamPaperRecords = new ArrayList<>();
                //存储 智能组卷、快速练习 课后练习-题目 组建的虚拟卷
                List<UserExamPaperRecord> userExamPaperRecordFictitious = new ArrayList<>();
                //存储 模拟考试 历年真题 科目练习 课后练习-试卷系统卷
                List<UserExamPaperRecord> userExamPaperRecordOther = new ArrayList<>();
                for (UserExamPaperRecord userExamPaperRecord : sortUserExamPaperRecordList) {
                    if (QuestionArguments.USER_EXAM_PAPER_CATALOG_CODE_LIST.contains(userExamPaperRecord.getCatalog())) {
                        userExamPaperRecordFictitious.add(userExamPaperRecord);
                    } else {
                        userExamPaperRecordOther.add(userExamPaperRecord);
                    }
                }
                //虚拟卷取最新做题记录
                Map<Integer, List<UserExamPaperRecord>> examPaperIdRecordFictitiousListMap = userExamPaperRecordFictitious.stream().collect(Collectors.groupingBy(UserExamPaperRecord::getExamPaperId,
                        Collectors.collectingAndThen(
                                Collectors.toList(),
                                list -> list.stream()
                                        .sorted(Comparator.comparing(UserExamPaperRecord::getCreateTime).reversed())
                                        .collect(Collectors.toList())
                        )
                ));
                for (List<UserExamPaperRecord> value : examPaperIdRecordFictitiousListMap.values()) {
                    userExamPaperRecords.add(value.get(0));
                }
                //系统卷取最新做题记录
                Map<Integer, List<UserExamPaperRecord>> examPaperIdRecordOtherListMap = userExamPaperRecordOther.stream().collect(Collectors.groupingBy(UserExamPaperRecord::getExamPaperId,
                        Collectors.collectingAndThen(
                                Collectors.toList(),
                                list -> list.stream()
                                        .sorted(Comparator.comparing(UserExamPaperRecord::getCreateTime).reversed())
                                        .collect(Collectors.toList())
                        )
                ));
                for (List<UserExamPaperRecord> value : examPaperIdRecordOtherListMap.values()) {
                    userExamPaperRecords.add(value.get(0));
                }

                List<Integer> userExamPaperRecordIds = userExamPaperRecords.stream().map(UserExamPaperRecord::getId).collect(Collectors.toList());

                //查询用户已经做过的题
                UserExamPaperRecordDetailQuery userExamPaperRecordDetailQuery = new UserExamPaperRecordDetailQuery();
                userExamPaperRecordDetailQuery.setUserExamPaperRecordIds(new ArrayList<>(userExamPaperRecordIds));
                userExamPaperRecordDetailQuery.setQuestionIdList(questionIds);
                userExamPaperRecordDetailQuery.setQuestionResult(UserExamPaperRecordDetailQuestionResult.N.getCode());
                userExamPaperRecordDetailQuery.setStatus(DataStatus.Y.getCode());
                List<UserExamPaperRecordDetail> errorUserExamPaperRecordDetailList = userExamPaperRecordDetailService.findAll(userExamPaperRecordDetailQuery);

                if (isEmpty(errorUserExamPaperRecordDetailList) || errorUserExamPaperRecordDetailList.isEmpty()) {
                    return new Response<>(OK, SUCCESS, examPaperRecordWrongReportResponse);
                }

                Map<Integer, List<Integer>> questionIdUserExamPaperRecordDetailIdsMap = errorUserExamPaperRecordDetailList.stream().collect(Collectors.groupingBy(UserExamPaperRecordDetail::getQuestionId, Collectors.mapping(UserExamPaperRecordDetail::getId, Collectors.toList())));

                Set<Integer> questionIdSet = errorUserExamPaperRecordDetailList.stream().map(UserExamPaperRecordDetail::getQuestionId).collect(Collectors.toSet());

                if (isEmpty(questionIdSet) || questionIdSet.isEmpty()) {
                    return new Response<>(OK, SUCCESS, examPaperRecordWrongReportResponse);
                }
                Map<Integer, String> questionChapterMap = new HashMap<>();
                List<QuestionChapterResponse> responses = new ArrayList<>();
                if (!isEmpty(questionList) && !questionList.isEmpty()) {
                    //查询科目信息
                    examPaperRecordWrongReportResponse.setProjectItemId(projectItem != null ? projectItem.getId() : null);
                    examPaperRecordWrongReportResponse.setProjectItemName(projectItem != null ? projectItem.getName() : null);

                    examPaperRecordWrongReportResponse.setProjectItemMessage(projectItem != null ? projectItem.getName() + "中，您有待加强的方向在：" : null);
                    //查询项目下章节知识点
                    QuestionChapterQuery questionChapterQuery = new QuestionChapterQuery();
                    questionChapterQuery.setProjectItemId(projectItem.getId());
                    questionChapterQuery.setStatus(DataStatus.Y.getCode());
                    List<QuestionChapter> questionChapterList = questionChapterService.findAll(questionChapterQuery);

                    for (QuestionChapter questionChapter : questionChapterList) {
                        QuestionChapterResponse response = new QuestionChapterResponse();
                        response.setId(questionChapter.getId());
                        response.setName(questionChapter.getName());
                        response.setCatalog(questionChapter.getCatalog());
                        response.setParentId(questionChapter.getParentId());
                        response.setSort(questionChapter.getSort());
                        responses.add(response);
                        questionChapterMap.put(questionChapter.getId(), questionChapter.getName());
                    }
                }

                Map<Integer, List<Integer>> questionChapterParentIdAllChildrenIdsMap = new HashMap<>();
                //构建树结构
                List<QuestionChapterResponse> QuestionChapterTreeList = buildQuestionChapterTree(responses, 0);

                for (QuestionChapterResponse questionChapterResponse : QuestionChapterTreeList) {
                    List<Integer> allChildrenIds = getAllChildrenIds(questionChapterResponse);
                    questionChapterParentIdAllChildrenIdsMap.put(questionChapterResponse.getId(), allChildrenIds);
                }
                List<ChapterExamPaperRecordDetailResponse> chapterExamPaperRecordDetailResponses = new ArrayList<>();
                for (Map.Entry<Integer, List<Integer>> questionChapterEntry : questionChapterParentIdAllChildrenIdsMap.entrySet()) {
                    Integer questionChapterId = questionChapterEntry.getKey();

                    List<Integer> userExamPaperRecordDetailIdList = new ArrayList<>();
                    for (Question question : questionList) {
                        if (questionChapterEntry.getValue().contains(question.getQuestionChapterId()) && questionIdUserExamPaperRecordDetailIdsMap.containsKey(question.getId())) {
                            List<Integer> userExamPaperRecordDetailIds = questionIdUserExamPaperRecordDetailIdsMap.get(question.getId());
                            userExamPaperRecordDetailIdList.addAll(userExamPaperRecordDetailIds);
                        }
                    }

                    if (!userExamPaperRecordDetailIdList.isEmpty()) {
                        ChapterExamPaperRecordDetailResponse chapterExamPaperRecordDetailResponse = new ChapterExamPaperRecordDetailResponse();
                        chapterExamPaperRecordDetailResponse.setQuestionChapterId(questionChapterId);
                        chapterExamPaperRecordDetailResponse.setQuestionChapterName(questionChapterMap.getOrDefault(questionChapterId, null));
                        chapterExamPaperRecordDetailResponse.setUserExamPaperRecordDetailIdList(userExamPaperRecordDetailIdList);
                        chapterExamPaperRecordDetailResponses.add(chapterExamPaperRecordDetailResponse);
                    }
                }

                if (chapterExamPaperRecordDetailResponses.size() > EXAM_PAPER_RECORD_WRONG_REPORT_LIMIT) {
                    chapterExamPaperRecordDetailResponses = chapterExamPaperRecordDetailResponses.stream()
                            .sorted(Comparator.comparing(response -> response.getUserExamPaperRecordDetailIdList().size()))
                            .collect(Collectors.toList());
                }
                //数量大于8 直返会 错题数量较多的前八个 小于等于8个全返回
                int size = chapterExamPaperRecordDetailResponses.size();
                int startIndex = Math.max(0, size - EXAM_PAPER_RECORD_WRONG_REPORT_LIMIT);
                examPaperRecordWrongReportResponse.setChapterExamPaperRecordDetailResponses(chapterExamPaperRecordDetailResponses.subList(startIndex, size));
            }
            return new Response<>(OK, SUCCESS, examPaperRecordWrongReportResponse);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }

    public static List<Integer> getAllChildrenIds(QuestionChapterResponse root) {
        List<Integer> questionChapterIds = new ArrayList<>();
        if (root != null) {
            // 添加当前节点
            questionChapterIds.add(root.getId());
            // 递归添加所有子节点
            for (QuestionChapterResponse child : root.getChildList()) {
                questionChapterIds.addAll(getAllChildrenIds(child));
            }
        }
        return questionChapterIds;
    }
    /**
     * 构建树结构
     */
    private List<QuestionChapterResponse> buildQuestionChapterTree(List<QuestionChapterResponse> list, Integer pid) {
        List<QuestionChapterResponse> tree = new ArrayList<>();
        for (QuestionChapterResponse node : list) {
            if (pid.equals(node.getParentId())) {
                tree.add(findQuestionChapterChild(node, list));
            }
        }
        tree.sort(Comparator.comparingInt(QuestionChapterResponse::getSort));
        return tree;
    }

    /**
     * 递归构建子级树结构
     */
    private QuestionChapterResponse findQuestionChapterChild(QuestionChapterResponse node, List<QuestionChapterResponse> list) {
        node.setChildList(new ArrayList<>());

        for (QuestionChapterResponse item : list) {
            if (node.getId().equals(item.getParentId())) {
                node.getChildList().add(findQuestionChapterChild(item, list));
            }
        }
        if (node.getChildList() != null && !node.getChildList().isEmpty()) {
            node.getChildList().sort(Comparator.comparingInt(QuestionChapterResponse::getSort));
        }
        return node;
    }

    /**
     * 交卷/保存 官网 APP(主观题-单题单练-自动评分)
     * 仅适用于:单题练习
     * 生成做题报告 返回做卷记录ID 根据此ID可调用查询做题报告接口查看详情
     * 2025-06-12 李明洋
     * <AUTHOR>
     * @date 2024-03-28
     */
    @RequestMapping(value = "/v1/home/<USER>/paper/record/single/create")
    @Token
    public Response<?> saveOrSubmitSingle(@RequestBody UserExamPaperRecordRequest request){
        Integer customerId = getUserToken().getCustomerId();
        Lock lock = getLock(redisTemplate, CacheKey.USER_EXAM_PAPER_RECORD_SAVE_OR_SUBMIT, String.valueOf(customerId));
        if (!lock.tryLock()) {
            return new Response<>(ERROR, "请勿点击过快");
        }
        try{
            //校验 交卷基础信息
            UserExamPaperRecordValidator validator = new UserExamPaperRecordValidator();
            if (!validator
                    .onProjectId(request.getProjectId())
                    .onExamPaperId(request.getExamPaperId())
                    .onCatalog(request.getCatalog())
                    .onOver(request.getOver())
                    .onUserExamRecordDetailRequests(request.getUserExamPaperRecordDetailRequests())
                    .onMode(request.getMode())
                    .result()){
                return new Response<>(ERROR,validator.getErrorMessage());
            }

            //校验 交卷每题详细信息 搜集交卷详情Map K:试题ID V:做题详情(可能包含的参数:id,answer)
            Map<Integer, UserExamPaperRecordDetailRequest> questionIdMappingUserExamPaperRecordDetailRequestMap = new HashMap<>();
            UserExamPaperRecordDetailValidator detailValidator;
            for (UserExamPaperRecordDetailRequest userExamPaperRecordDetailRequest : request.getUserExamPaperRecordDetailRequests()) {
                detailValidator = new UserExamPaperRecordDetailValidator();
                if (!detailValidator
                        .onQuestionId(userExamPaperRecordDetailRequest.getQuestionId())
                        .onMark(userExamPaperRecordDetailRequest.getMark())
                        .result()){
                    return new Response<>(ERROR, detailValidator.getErrorMessage());
                }
                if (questionIdMappingUserExamPaperRecordDetailRequestMap.containsKey(userExamPaperRecordDetailRequest.getQuestionId())){
                    return new Response<>(ERROR, "试题编号存在重复");
                }
                questionIdMappingUserExamPaperRecordDetailRequestMap.put(userExamPaperRecordDetailRequest.getQuestionId(), userExamPaperRecordDetailRequest);
            }

            Date serverTime = getServerTime();

            //查询试卷详情 包含试题列表(注意用户组卷中材料题小题需要额外查询)
            List<Question> questionList = new ArrayList<>();
            //单题练习是先创建用户卷,在做题的形式,所以直接查询的是用户组卷
            UserExamPaper userExamPaper = userExamPaperService.findById(request.getExamPaperId());
            if (userExamPaper == null || !DataStatus.Y.getCode().equals(userExamPaper.getStatus())){
                return new Response<>(ERROR, "试卷不存在或已下架");
            }
            //查询原卷的自动批阅开关
            PublicSwitch autoReadSwitch = PublicSwitch.N;
            if (!isEmpty(userExamPaper.getExamPaperId())){
                ExamPaper examPaper = examPaperService.findById(userExamPaper.getExamPaperId());
                if (!isEmpty(examPaper) && !isEmpty(examPaper.getAutoReadSwitch()) && PublicSwitch.Y.getCode().equals(examPaper.getAutoReadSwitch())){
                    autoReadSwitch = PublicSwitch.Y;
                }
            }
            UserExamPaperQuestionQuery userExamPaperQuestionQuery = new UserExamPaperQuestionQuery();
            userExamPaperQuestionQuery.setStatus(DataStatus.Y.getCode());
            userExamPaperQuestionQuery.setExamPaperId(request.getExamPaperId());
            List<UserExamPaperQuestion> userExamPaperQuestionList = userExamPaperQuestionService.findAll(userExamPaperQuestionQuery);
            if (isEmpty(userExamPaperQuestionList) || userExamPaperQuestionList.isEmpty()){
                return new Response<>(ERROR, "试卷有误");
            }
            List<Integer> questionIdList = userExamPaperQuestionList.stream().filter(userExamPaperQuestion -> !isEmpty(userExamPaperQuestion.getQuestionId())).map(UserExamPaperQuestion::getQuestionId).collect(Collectors.toList());
            if (!questionIdList.isEmpty()){
                //查询试题
                QuestionQuery questionQuery = new QuestionQuery();
                questionQuery.setStatus(DataStatus.Y.getCode());
                questionQuery.setIds(new ArrayList<>(questionIdList));
                List<Question> questionParentList = questionService.findAll(questionQuery);
                List<Integer> questionIdForC6 = new ArrayList<>();
                if (!isEmpty(questionParentList) && !questionParentList.isEmpty()){
                    questionList.addAll(questionParentList);
                    //查询材料题下的小题信息
                    for (Question question : questionParentList) {
                        if (QuestionBaseType.C6.getCode().equals(question.getType())){
                            questionIdForC6.add(question.getId());
                        }
                    }
                    if (!questionIdForC6.isEmpty()){
                        QuestionQuery questionQueryForChildren = new QuestionQuery();
                        questionQueryForChildren.setStatus(DataStatus.Y.getCode());
                        questionQueryForChildren.setParentIds(questionIdForC6);
                        List<Question> questionForC6ChildrenList = questionService.findAll(questionQueryForChildren);
                        if (!isEmpty(questionForC6ChildrenList) && !questionForC6ChildrenList.isEmpty()){
                            questionList.addAll(questionForC6ChildrenList);
                        }
                    }
                }
            }

            //2.2 试卷中有效的试题详情
            if (isEmpty(questionList) || questionList.isEmpty()){
                return new Response<>(ERROR, "查询试题元数据信息失败!");
            }

            //3.1 创建或修改交卷记录
            UserExamPaperRecord userExamPaperRecordCreateOrModify = new UserExamPaperRecord();
            if (!isEmpty(request.getId())){
                UserExamPaperRecord userExamPaperRecord = userExamPaperRecordService.findById(request.getId());
                if (isEmpty(userExamPaperRecord) || !DataStatus.Y.getCode().equals(userExamPaperRecord.getStatus())){
                    return new Response<>(ERROR, "做题记录编号无效");
                }
                userExamPaperRecordCreateOrModify.setId(request.getId());
            } else {
                userExamPaperRecordCreateOrModify.setStatus(DataStatus.Y.getCode());
                userExamPaperRecordCreateOrModify.setCreateTime(serverTime);
            }
            //项目编号
            userExamPaperRecordCreateOrModify.setProjectId(request.getProjectId());
            //客户编号
            userExamPaperRecordCreateOrModify.setCustomerId(customerId);
            //userId编号
            userExamPaperRecordCreateOrModify.setUserId(getUserToken().getId());
            //考卷编号
            userExamPaperRecordCreateOrModify.setExamPaperId(request.getExamPaperId());
            //做题记录分类: 还是原来的分类,使用playType字段区分
            userExamPaperRecordCreateOrModify.setCatalog(request.getCatalog());
            //完成(交卷类型: 交卷/保存)
            userExamPaperRecordCreateOrModify.setOver(request.getOver());
            //剩余交卷时间
            userExamPaperRecordCreateOrModify.setSecond(request.getSecond());
            //用户做卷开始时间
            if (!isEmpty(request.getStartTime())){
                userExamPaperRecordCreateOrModify.setStartTime(DateUtil.parse(request.getStartTime(), DATETIME_FORMAT));
            }
            //用户做卷结束时间 只有提交了才有时间
            if (UserExamPaperRecordOver.Y.getCode().equals(request.getOver())){
                userExamPaperRecordCreateOrModify.setEndTime(serverTime);
            }
            //耗时
            userExamPaperRecordCreateOrModify.setDuration(request.getDuration());
            //上次练习位置
            userExamPaperRecordCreateOrModify.setLastExerciseQuestion(request.getLastExerciseQuestion());
            //是否批阅(默认未完成)
            userExamPaperRecordCreateOrModify.setReadOver(UserExamOverStatus.C1.getCode());
            //更新时间
            userExamPaperRecordCreateOrModify.setModifyTime(serverTime);

            //3.2 创建或修改交卷记录详情
            //3.2.1 查询是不是存在之前保存过的做题详情记录
            List<UserExamPaperRecordDetail> userExamPaperRecordDetailCreateOrModifyList = new ArrayList<>();
            Map<Integer, UserExamPaperRecordDetail> questionIdMappingUserExamPaperRecordDetailMap = new HashMap<>();
            if (!isEmpty(request.getId())){
                UserExamPaperRecordDetailQuery userExamPaperRecordDetailQuery = new UserExamPaperRecordDetailQuery();
                userExamPaperRecordDetailQuery.setStatus(DataStatus.Y.getCode());
                userExamPaperRecordDetailQuery.setUserExamPaperRecordId(request.getId());
                List<UserExamPaperRecordDetail> userExamPaperRecordDetailList = userExamPaperRecordDetailService.findAll(userExamPaperRecordDetailQuery);
                if (!isEmpty(userExamPaperRecordDetailList) && !userExamPaperRecordDetailList.isEmpty()){
                    questionIdMappingUserExamPaperRecordDetailMap = userExamPaperRecordDetailList.stream().filter(a -> !isEmpty(a.getQuestionId())).collect(Collectors.toMap(UserExamPaperRecordDetail::getQuestionId, Function.identity(), (a,b) -> a));
                }
            }

            //查询得分点
            QuestionScorePointQuery questionScorePointQuery = new QuestionScorePointQuery();
            questionScorePointQuery.setStatus(DataStatus.Y.getCode());
            questionScorePointQuery.setQuestionIds(questionList.stream().map(Question::getId).collect(Collectors.toList()));
            List<QuestionScorePoint> questionScorePointList = questionScorePointService.findAll(questionScorePointQuery);
            Map<Integer, List<QuestionScorePoint>> questionScorePointMap = new HashMap<>();
            if (!isEmpty(questionScorePointList) && !questionScorePointList.isEmpty()){
                questionScorePointMap = questionScorePointList.stream().collect(Collectors.groupingBy(QuestionScorePoint::getQuestionId));
            }

            //开始以试卷关联的所有试题进行搜集    这里试题列表包含大题和小题
            BigDecimal sumScore = BigDecimal.ZERO,      allScore = BigDecimal.ZERO;
            for (Question question : questionList) {
                //AI评分状态
                UserExamPaperRecordDetailAiScoreStatus userExamPaperRecordDetailAiScoreStatus = UserExamPaperRecordDetailAiScoreStatus.NOT_REQUIRED;
                UserExamPaperRecordDetail userExamPaperRecordDetailCreateOrModify = this.getUserExamPaperRecordDetailCreateOrModify(questionIdMappingUserExamPaperRecordDetailRequestMap.get(question.getId()), questionIdMappingUserExamPaperRecordDetailMap.get(question.getId()), question, getUserToken(), serverTime);
                //分值
                userExamPaperRecordDetailCreateOrModify.setQuestionScore(question.getScore());

                //大题
                if (0 == question.getParentId()){
                    allScore = question.getScore();
                }

                //交卷 生成得分点的报告信息
                if (PublicSwitch.Y.equals(autoReadSwitch) && UserExamPaperRecordOver.Y.getCode().equals(request.getOver()) && QuestionBaseType.C5.getCode().equals(question.getType())){
                    if (!isEmpty(userExamPaperRecordDetailCreateOrModify.getAnswer())){
                        userExamPaperRecordDetailAiScoreStatus = UserExamPaperRecordDetailAiScoreStatus.IN;
                    }
                    List<UserExamPaperRecordScore> userExamPaperRecordScores = userExamPaperRecordScoreService.generateRecordScoreListV1(null, userExamPaperRecordDetailCreateOrModify.getAnswer(), questionScorePointMap.get(question.getId()));
                    //得分点报告
                    userExamPaperRecordDetailCreateOrModify.setUserExamPaperRecordScoreList(userExamPaperRecordScores);
                    Integer userQuestionScore = 0;
                    for (UserExamPaperRecordScore userExamPaperRecordScore : userExamPaperRecordScores) {
                        Integer userScore = userExamPaperRecordScore.getUserScore();
                        sumScore = sumScore.add(new BigDecimal(userScore));
                        userQuestionScore += userScore;
                    }
                    userExamPaperRecordDetailCreateOrModify.setScore(new BigDecimal(userQuestionScore));
                }

                //AI评分状态
                userExamPaperRecordDetailCreateOrModify.setAiScoreStatus(userExamPaperRecordDetailAiScoreStatus.getCode());
                userExamPaperRecordDetailCreateOrModifyList.add(userExamPaperRecordDetailCreateOrModify);
            }

            //总得分
            userExamPaperRecordCreateOrModify.setSumScore(sumScore);
            //总分
            userExamPaperRecordCreateOrModify.setAllScore(allScore);
            //试题数量
            userExamPaperRecordCreateOrModify.setQuestionNumber(1);
            //做题数量
            userExamPaperRecordCreateOrModify.setPlayQuestionNumber(1);
            //试卷正确率
            userExamPaperRecordCreateOrModify.setRightRate(null);
            //试卷得分率
            BigDecimal scoreRate = sumScore.equals(BigDecimal.ZERO) ? BigDecimal.ZERO : sumScore.divide(allScore, 2, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100));
            userExamPaperRecordCreateOrModify.setScoreRate(scoreRate);
            //做题模式
            userExamPaperRecordCreateOrModify.setMode(request.getMode());

            //批阅状态设置为已批阅
            userExamPaperRecordCreateOrModify.setReadOver(UserExamOverStatus.C0.getCode());

            //这个交卷只是单题练习
            userExamPaperRecordCreateOrModify.setPlayType(UserExamPaperRecordPlayType.C1.getCode());

            //5 持久化
            userExamPaperRecordService.createOrModify(userExamPaperRecordCreateOrModify, userExamPaperRecordDetailCreateOrModifyList);

            //6 发消息: 交卷后自动调用AI评分
            for (UserExamPaperRecordDetail userExamPaperRecordDetail : userExamPaperRecordDetailCreateOrModifyList) {
                //这里已经对后台关闭评分功能的试卷做了过滤
                if (!UserExamPaperRecordDetailAiScoreStatus.IN.getCode().equals(userExamPaperRecordDetail.getAiScoreStatus())){
                    continue;
                }
                //只对评分中的记录进行处理
                MQProducer.INSTANCE.sendOneway(LogSequence.get(), MQTopic.LXFK_API, MQTag.LXFK_API_AI_SCORE_STATUS, userExamPaperRecordDetail.getId().toString());
            }

            return new Response<>(OK,SUCCESS,userExamPaperRecordCreateOrModify.getId());
        }catch (Exception e){
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        } finally {
            lock.unlock();
        }
    }

    /**
     * 交卷/保存 官网 APP(主观题-试卷-自动评分)
     * 适用于:系统试卷,包括科目练习,历年真题(根标签下),模拟考试;
     * 不适用:用户组卷,包括快速练习,历年真题(子标签组卷),智能组卷,快速练真题,单题练习等;
     * 生成做题报告 返回做卷记录ID 根据此ID可调用查询做题报告接口查看详情
     * <AUTHOR>
     * @date 2025-06-16
     */
    @RequestMapping(value = "/v1/home/<USER>/paper/record/exam/create")
    @Token
    public Response<?> saveOrSubmitExam(@RequestBody UserExamPaperRecordRequest request){
        Integer customerId = getUserToken().getCustomerId();
        Lock lock = getLock(redisTemplate, CacheKey.USER_EXAM_PAPER_RECORD_SAVE_OR_SUBMIT, String.valueOf(customerId));
        if (!lock.tryLock()) {
            return new Response<>(ERROR, "请勿点击过快");
        }
        try{
            //校验 交卷基础信息
            UserExamPaperRecordValidator validator = new UserExamPaperRecordValidator();
            if (!validator
                    .onProjectId(request.getProjectId())
                    .onExamPaperId(request.getExamPaperId())
//                    .onCatalog(request.getCatalog())
                    .onOver(request.getOver())
                    .onUserExamRecordDetailRequests(request.getUserExamPaperRecordDetailRequests())
                    .onMode(request.getMode())
                    .result()){
                return new Response<>(ERROR,validator.getErrorMessage());
            }
            //catalog 只允许输入: 模拟考试 历年真题 科目练习
            if (!UserExamPaperRecordCatalog.C2.getCode().equals(request.getCatalog()) && !UserExamPaperRecordCatalog.C3.getCode().equals(request.getCatalog()) && !UserExamPaperRecordCatalog.C4.getCode().equals(request.getCatalog())){
                return new Response<>(ERROR, "做题分类有误");
            }

            //校验 交卷每题详细信息 搜集交卷详情Map K:试题ID V:做题详情(可能包含的参数:id,answer)
            Map<Integer, UserExamPaperRecordDetailRequest> questionIdMappingUserExamPaperRecordDetailRequestMap = new HashMap<>();
            UserExamPaperRecordDetailValidator detailValidator;
            for (UserExamPaperRecordDetailRequest userExamPaperRecordDetailRequest : request.getUserExamPaperRecordDetailRequests()) {
                detailValidator = new UserExamPaperRecordDetailValidator();
                if (!detailValidator
                        .onQuestionId(userExamPaperRecordDetailRequest.getQuestionId())
                        .onMark(userExamPaperRecordDetailRequest.getMark())
                        .result()){
                    return new Response<>(ERROR, detailValidator.getErrorMessage());
                }
                if (questionIdMappingUserExamPaperRecordDetailRequestMap.containsKey(userExamPaperRecordDetailRequest.getQuestionId())){
                    return new Response<>(ERROR, "试题编号存在重复");
                }
                questionIdMappingUserExamPaperRecordDetailRequestMap.put(userExamPaperRecordDetailRequest.getQuestionId(), userExamPaperRecordDetailRequest);
            }

            //这里当前做的都是系统试卷,所以不用查询用户组卷
            Date serverTime = getServerTime();

            //查询试卷详情 包含试题列表(注意用户组卷中材料题小题需要额外查询)
            Map<Integer, ExamPaperQuestion> questionIdMappingExamPaperQuestionMap = new HashMap<>();
            List<Question> questionList = new ArrayList<>();
            //K:试卷-试题关联ID       V:试卷-试题-得分点关联信息(只用其中的分值信息)
            Map<Integer, List<ExamPaperQuestionScorePoint>> examPaperQuestionScorePointMap = new HashMap<>();
            //K:得分点ID       V:得分点
            Map<Integer, QuestionScorePoint> questionScorePointMap = new HashMap<>();


            ExamPaper examPaper = examPaperService.findById(request.getExamPaperId());
            if (examPaper == null || !DataStatus.Y.getCode().equals(examPaper.getStatus()) || !PublicStage.Y.getCode().equals(examPaper.getStage())){
                return new Response<>(ERROR, "试卷不存在或已下架");
            }
            String examPaperName = examPaper.getName();
            String correctSwitch = examPaper.getCorrectSwitch();

            ExamPaperQuestionQuery examPaperQuestionQuery = new ExamPaperQuestionQuery();
            examPaperQuestionQuery.setStatus(DataStatus.Y.getCode());
            examPaperQuestionQuery.setExamPaperId(examPaper.getId());
            List<ExamPaperQuestion> examPaperQuestionList = examPaperQuestionService.findAll(examPaperQuestionQuery);
            if (!isEmpty(examPaperQuestionList) && !examPaperQuestionList.isEmpty()){
                //K: 试题ID       V: 试卷和试题的关联关系           搜集该Map后续会使用系统创建试卷时候设置的分值信息
                questionIdMappingExamPaperQuestionMap = examPaperQuestionList.stream().filter(a -> !isEmpty(a.getQuestionId())).collect(Collectors.toMap(ExamPaperQuestion::getQuestionId, Function.identity(), (a,b) -> a));
                if (!questionIdMappingExamPaperQuestionMap.isEmpty()){
                    QuestionQuery questionQuery = new QuestionQuery();
                    questionQuery.setStatus(DataStatus.Y.getCode());
                    questionQuery.setIds(new ArrayList<>(questionIdMappingExamPaperQuestionMap.keySet()));
                    questionList = questionService.findAll(questionQuery);
                }

                //查询得分点列表
                List<Integer> examPaperQuestionIdList = examPaperQuestionList.stream().map(ExamPaperQuestion::getId).collect(Collectors.toList());
                ExamPaperQuestionScorePointQuery examPaperQuestionScorePointQuery = new ExamPaperQuestionScorePointQuery();
                examPaperQuestionScorePointQuery.setStatus(DataStatus.Y.getCode());
                examPaperQuestionScorePointQuery.setExamPaperQuestionIds(examPaperQuestionIdList);
                List<ExamPaperQuestionScorePoint> examPaperQuestionScorePointList = examPaperQuestionScorePointService.findAll(examPaperQuestionScorePointQuery);
                if (!isEmpty(examPaperQuestionScorePointList) && !examPaperQuestionScorePointList.isEmpty()){
                    examPaperQuestionScorePointMap = examPaperQuestionScorePointList.stream().collect(Collectors.groupingBy(ExamPaperQuestionScorePoint::getExamPaperQuestionId));
                    List<Integer> questionScorePointIdList = examPaperQuestionScorePointList.stream().filter(a -> !isEmpty(a.getQuestionScorePointId())).map(ExamPaperQuestionScorePoint::getQuestionScorePointId).collect(Collectors.toList());
                    QuestionScorePointQuery questionScorePointQuery = new QuestionScorePointQuery();
                    questionScorePointQuery.setStatus(DataStatus.Y.getCode());
                    questionScorePointQuery.setIds(questionScorePointIdList);
                    List<QuestionScorePoint> questionScorePointList = questionScorePointService.findAll(questionScorePointQuery);
                    if (!isEmpty(questionScorePointList) && !questionScorePointList.isEmpty()){
                        questionScorePointMap = questionScorePointList.stream().collect(Collectors.toMap(QuestionScorePoint::getId, Function.identity()));
                    }
                }


            }
            if (isEmpty(questionList) || questionList.isEmpty()){
                return new Response<>(ERROR, "查询试题元数据信息失败!");
            }

            //创建或修改交卷记录
            UserExamPaperRecord userExamPaperRecordCreateOrModify = new UserExamPaperRecord();
            if (!isEmpty(request.getId())){
                UserExamPaperRecord userExamPaperRecord = userExamPaperRecordService.findById(request.getId());
                if (isEmpty(userExamPaperRecord) || !DataStatus.Y.getCode().equals(userExamPaperRecord.getStatus())){
                    return new Response<>(ERROR, "做题记录编号无效");
                }
                userExamPaperRecordCreateOrModify.setId(request.getId());
            } else {
                userExamPaperRecordCreateOrModify.setStatus(DataStatus.Y.getCode());
                userExamPaperRecordCreateOrModify.setCreateTime(serverTime);
            }
            //项目编号
            userExamPaperRecordCreateOrModify.setProjectId(request.getProjectId());
            //客户编号
            userExamPaperRecordCreateOrModify.setCustomerId(customerId);
            //userId编号
            userExamPaperRecordCreateOrModify.setUserId(getUserToken().getId());
            //考卷编号
            userExamPaperRecordCreateOrModify.setExamPaperId(request.getExamPaperId());
            //做题记录分类
            userExamPaperRecordCreateOrModify.setCatalog(request.getCatalog());
            //完成(交卷类型: 交卷/保存)
            userExamPaperRecordCreateOrModify.setOver(request.getOver());
            //剩余交卷时间
            userExamPaperRecordCreateOrModify.setSecond(request.getSecond());
            //用户做卷开始时间
            if (!isEmpty(request.getStartTime())){
                userExamPaperRecordCreateOrModify.setStartTime(DateUtil.parse(request.getStartTime(), DATETIME_FORMAT));
            }
            //用户做卷结束时间 只有提交了才有时间
            if (UserExamPaperRecordOver.Y.getCode().equals(request.getOver())){
                userExamPaperRecordCreateOrModify.setEndTime(serverTime);
            }
            //耗时
            userExamPaperRecordCreateOrModify.setDuration(request.getDuration());
            //上次练习位置
            userExamPaperRecordCreateOrModify.setLastExerciseQuestion(request.getLastExerciseQuestion());
            //是否批阅(默认未完成)
            userExamPaperRecordCreateOrModify.setReadOver(UserExamOverStatus.C1.getCode());
            //更新时间
            userExamPaperRecordCreateOrModify.setModifyTime(serverTime);

            //创建或修改交卷记录详情 查询是不是存在之前保存过的做题详情记录
            List<UserExamPaperRecordDetail> userExamPaperRecordDetailCreateOrModifyList = new ArrayList<>();
            Map<Integer, UserExamPaperRecordDetail> questionIdMappingUserExamPaperRecordDetailMap = new HashMap<>();
            if (!isEmpty(request.getId())){
                UserExamPaperRecordDetailQuery userExamPaperRecordDetailQuery = new UserExamPaperRecordDetailQuery();
                userExamPaperRecordDetailQuery.setStatus(DataStatus.Y.getCode());
                userExamPaperRecordDetailQuery.setUserExamPaperRecordId(request.getId());
                List<UserExamPaperRecordDetail> userExamPaperRecordDetailList = userExamPaperRecordDetailService.findAll(userExamPaperRecordDetailQuery);
                if (!isEmpty(userExamPaperRecordDetailList) && !userExamPaperRecordDetailList.isEmpty()){
                    questionIdMappingUserExamPaperRecordDetailMap = userExamPaperRecordDetailList.stream().filter(a -> !isEmpty(a.getQuestionId())).collect(Collectors.toMap(UserExamPaperRecordDetail::getQuestionId, Function.identity(), (a,b) -> a));
                }
            }
            /*
             * 总得分:     总得分
             * 试题数量:   大题试题的数量
             * 做题数量:   答案不为空的试题数量(只算大题,大题答案中存标识符)
             * 试卷正确率: 基础题型中做对的数量 / 基础题型数量   (不区分大小题)
             * 试卷得分率: 总得分 / 总分  (不区分大小题)
             */
            //试题数量                                  做题数量
            int questionNumber = 0,                     doQuestionNumber = 0;
            //基础题型中做对的数量            /            基础题型数量         =       试卷正确率
            int rightNumber = 0,                        canRightNumber = 0;
            //总得分                        /            试卷总分          =       试卷得分率;        2024-08-08修改为使用 总得分/试卷总分=得分率
            BigDecimal sumScore = BigDecimal.ZERO,      allScore = BigDecimal.ZERO;
            //用来判断试卷下是否包含不能自动判断对错的题目
            int notCanDetermineAnswerRightOrWrong = 0;

            //开始以试卷关联的所有试题进行搜集    这里试题列表包含大题和小题
            for (Question question : questionList) {
                //AI评分状态
                UserExamPaperRecordDetailAiScoreStatus userExamPaperRecordDetailAiScoreStatus = UserExamPaperRecordDetailAiScoreStatus.NOT_REQUIRED;
                UserExamPaperRecordDetail userExamPaperRecordDetailCreateOrModify = this.getUserExamPaperRecordDetailCreateOrModify(questionIdMappingUserExamPaperRecordDetailRequestMap.get(question.getId()), questionIdMappingUserExamPaperRecordDetailMap.get(question.getId()), question, getUserToken(), serverTime);
                BigDecimal questionScore = BigDecimal.ZERO;
                String userAnswer = userExamPaperRecordDetailCreateOrModify.getAnswer();

                //本试题设置分数
                if (questionIdMappingExamPaperQuestionMap.containsKey(question.getId())){
                    ExamPaperQuestion examPaperQuestion = questionIdMappingExamPaperQuestionMap.get(question.getId());
                    questionScore = examPaperQuestion.getScore();
                    userExamPaperRecordDetailCreateOrModify.setQuestionScore(questionScore);
                }

                //大题
                if (0 == question.getParentId()){
                    allScore = allScore.add(questionScore);
                    //试题数量
                    questionNumber ++;
                    //做题数量
                    if (!isEmpty(userExamPaperRecordDetailCreateOrModify.getAnswer())){
                        doQuestionNumber ++;
                    }
                }

                //所有直接判断对错的试题
                if (QuestionArguments.CAN_DETERMINE_ANSWER_RIGHT_OR_WRONG.contains(question.getType())){
                    canRightNumber ++;
                    //默认做错
                    UserExamPaperRecordDetailQuestionResult result = UserExamPaperRecordDetailQuestionResult.N;
                    //用户提交了本题的做题详情
                    if (!isEmpty(userAnswer) && userAnswer.equals(question.getAnswer())){
                        rightNumber ++;
                        sumScore = sumScore.add(questionScore);
                        result = UserExamPaperRecordDetailQuestionResult.Y;
                        //得分
                        userExamPaperRecordDetailCreateOrModify.setScore(questionScore);
                    }
                    //对错
                    userExamPaperRecordDetailCreateOrModify.setQuestionResult(result.getCode());
                } else if (PublicSwitch.Y.getCode().equals(examPaper.getAutoReadSwitch()) && QuestionBaseType.C5.getCode().equals(question.getType())){
                    //主观题只有交卷才会打分并生成记录 要不然出现保存后再次提交会多次插入打分详情
                    if (UserExamPaperRecordOver.Y.getCode().equals(request.getOver())){
                        if (!isEmpty(userAnswer)){
                            userExamPaperRecordDetailAiScoreStatus = UserExamPaperRecordDetailAiScoreStatus.IN;
                        }
                        //所有可以智能打分的试题(这里不用判断材料类型,只用给小题打分就行)
                        List<QuestionScorePoint> questionScorePointList = new ArrayList<>();
                        if (questionIdMappingExamPaperQuestionMap.containsKey(question.getId())){
                            ExamPaperQuestion examPaperQuestion = questionIdMappingExamPaperQuestionMap.get(question.getId());
                            if (examPaperQuestionScorePointMap.containsKey(examPaperQuestion.getId())){
                                List<ExamPaperQuestionScorePoint> examPaperQuestionScorePointList = examPaperQuestionScorePointMap.get(examPaperQuestion.getId());
                                for (ExamPaperQuestionScorePoint examPaperQuestionScorePoint : examPaperQuestionScorePointList) {
                                    Integer questionScorePointId = examPaperQuestionScorePoint.getQuestionScorePointId();
                                    if (questionScorePointMap.containsKey(questionScorePointId)){
                                        //这里的得分点对象是从数据库中查询的,改变他的分值为组卷时候的真实分值,这个对象不能操作更新
                                        QuestionScorePoint questionScorePoint = questionScorePointMap.get(questionScorePointId);
                                        questionScorePoint.setScore(examPaperQuestionScorePoint.getScore());
                                        questionScorePointList.add(questionScorePoint);
                                    }
                                }
                            }
                        }
                        List<UserExamPaperRecordScore> userExamPaperRecordScoreList = userExamPaperRecordScoreService.generateRecordScoreListV1(question.getAnswer(), userAnswer, questionScorePointList);
                        if (!isEmpty(userExamPaperRecordScoreList) && !userExamPaperRecordScoreList.isEmpty()){
                            userExamPaperRecordDetailCreateOrModify.setUserExamPaperRecordScoreList(userExamPaperRecordScoreList);
                            Integer userQuestionScore = 0;
                            for (UserExamPaperRecordScore userExamPaperRecordScore : userExamPaperRecordScoreList) {
                                Integer userScore = userExamPaperRecordScore.getUserScore();
                                sumScore = sumScore.add(new BigDecimal(userScore));
                                userQuestionScore += userScore;
                            }
                            //主观题用户得分
                            userExamPaperRecordDetailCreateOrModify.setScore(new BigDecimal(userQuestionScore));
                        }
                    }
                } else {
                    //不能打分的题目中,如果用户自评分不为空,需要计算到得分率中,不用计算正确率
                    if (!isEmpty(userExamPaperRecordDetailCreateOrModify.getScore())){
                        sumScore = sumScore.add(userExamPaperRecordDetailCreateOrModify.getScore());
                    }
                }
                //AI评分状态
                userExamPaperRecordDetailCreateOrModify.setAiScoreStatus(userExamPaperRecordDetailAiScoreStatus.getCode());
                userExamPaperRecordDetailCreateOrModifyList.add(userExamPaperRecordDetailCreateOrModify);
                if(!QuestionBaseType.C6.getCode().equals(question.getType()) && !QuestionArguments.CAN_DETERMINE_ANSWER_RIGHT_OR_WRONG.contains(question.getType())){
                    notCanDetermineAnswerRightOrWrong ++;
                }
            }

            //总得分
            userExamPaperRecordCreateOrModify.setSumScore(sumScore);
            //总分
            userExamPaperRecordCreateOrModify.setAllScore(allScore);
            //试题数量
            userExamPaperRecordCreateOrModify.setQuestionNumber(questionNumber);
            //做题数量
            userExamPaperRecordCreateOrModify.setPlayQuestionNumber(doQuestionNumber);
            //试卷正确率
            BigDecimal rightRate = rightNumber == 0 ? BigDecimal.ZERO : new BigDecimal(rightNumber).divide(new BigDecimal(canRightNumber), 2,  BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100));
            userExamPaperRecordCreateOrModify.setRightRate(rightRate);
            //试卷得分率
            BigDecimal scoreRate = sumScore.equals(BigDecimal.ZERO) ? BigDecimal.ZERO : sumScore.divide(allScore, 2, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100));
            userExamPaperRecordCreateOrModify.setScoreRate(scoreRate);
            //做题模式
            userExamPaperRecordCreateOrModify.setMode(request.getMode());

            /*//如果是模拟考试 并且是交卷操作 试卷下全是主观题（可以自动判断对错的题目） 或者试卷不需要批阅 批阅状态设置为已批阅
            if (UserExamPaperRecordOver.Y.getCode().equals(request.getOver()) &&
                    UserExamPaperRecordCatalog.C2.getCode().equals(request.getCatalog()) &&
                    (notCanDetermineAnswerRightOrWrong == 0 || ExamPaperCorrectSwitch.S1.getCode().equals(correctSwitch))) {
                userExamPaperRecordCreateOrModify.setReadOver(UserExamOverStatus.C0.getCode());
            }*/
            //模拟考试 && 交卷 && 不需要批阅           设置为已批阅      批阅列表会全部查询出来
            if (UserExamPaperRecordOver.Y.getCode().equals(request.getOver()) &&
                    UserExamPaperRecordCatalog.C2.getCode().equals(request.getCatalog()) &&
                    (ExamPaperCorrectSwitch.S1.getCode().equals(correctSwitch))) {
                userExamPaperRecordCreateOrModify.setReadOver(UserExamOverStatus.C0.getCode());
            }

            //这个接口交卷来自试卷,不能是单题练习或者星级组卷
            userExamPaperRecordCreateOrModify.setPlayType(UserExamPaperRecordPlayType.C0.getCode());

            //5 持久化
            userExamPaperRecordService.createOrModify(userExamPaperRecordCreateOrModify, userExamPaperRecordDetailCreateOrModifyList);
            //6 发消息: 1.得分率 <= 30%;  2.无主观题;  3.模拟考试  4.交卷
            boolean sendMessage = scoreRate.compareTo(new BigDecimal(30)) <= 0 && notCanDetermineAnswerRightOrWrong == 0 && UserExamPaperRecordCatalog.C2.getCode().equals(request.getCatalog()) && UserExamPaperRecordOver.Y.getCode().equals(request.getOver());
            if (sendMessage){
                UserQuery userQuery = new UserQuery();
                userQuery.setStatus(DataStatus.Y.getCode());
                userQuery.setProjectId(request.getProjectId());
                userQuery.setCustomerId(customerId);
                List<User> userList = userService.findAll(userQuery);
                if (!isEmpty(userList) && !userList.isEmpty()){
                    User user = userList.get(0);
                    //发消息给 学管师
                    SysUserMasterQuery sysUserMasterQuery = new SysUserMasterQuery();
                    sysUserMasterQuery.setStatus(DataStatus.Y.getCode());
                    sysUserMasterQuery.setUserId(user.getId());
                    List<SysUserMaster> sysUserMasterList = sysUserMasterService.findAll(sysUserMasterQuery);
                    if (!isEmpty(sysUserMasterList) && !sysUserMasterList.isEmpty()){
                        SysUserMaster sysUserMaster = sysUserMasterList.get(0);
                        Integer sysUserId = sysUserMaster.getSysUserId();
                        SysUser sysUser = sysUserService.findById(sysUserId);
                        if (!isEmpty(sysUser) && DataStatus.Y.getCode().equals(sysUser.getStatus()) && !isEmpty(sysUser.getDingUserId())){
                            BatchSendOTORequest batchSendOTORequest = new BatchSendOTORequest();
                            batchSendOTORequest.setRobotCode(com.api.constant.App.DING_LXFK_MESSAGE_ROBOT_CODE);
                            batchSendOTORequest.setMsgKey(DingMessageKey.SAMPLE_TEXT.getCode());
                            batchSendOTORequest.setUserIds(Collections.singletonList(String.valueOf(sysUser.getDingUserId())));
                            DingMessageSampleTextParam sampleTextParam = new DingMessageSampleTextParam();
                            String content = "律学-【学员考试低分提醒】你名下的线索编号为：" + user.getId() + " 学员近期模拟考试：" + examPaperName + "（模拟考试试卷名称），分数" + userExamPaperRecordCreateOrModify.getSumScore().stripTrailingZeros().toPlainString() + "分，试卷总分：" + userExamPaperRecordCreateOrModify.getAllScore().stripTrailingZeros().toPlainString() + "分，得分率为" + userExamPaperRecordCreateOrModify.getScoreRate().stripTrailingZeros().toPlainString() + "%，请通知提醒学员努力学习，欲度关山，何惧狂澜；风生水起，正好扬帆。";
                            sampleTextParam.setContent(content);
                            batchSendOTORequest.setMsgParam(this.getJSON(sampleTextParam));
                            MQProducer.INSTANCE.sendOneway(LogSequence.get(), MQTopic.LXFK_API, MQTag.LXFK_API_DING_MESSAGE_MESSAGE, this.getJSON(batchSendOTORequest));
                        }
                    }
                    //发消息给 教辅
                    UserTeacherQuery userTeacherQuery = new UserTeacherQuery();
                    userTeacherQuery.setStatus(DataStatus.Y.getCode());
                    userTeacherQuery.setUserId(user.getId());
                    List<UserTeacher> userTeacherList = userTeacherService.findAll(userTeacherQuery);
                    if (!isEmpty(userTeacherList) && !userTeacherList.isEmpty()){
                        UserTeacher userTeacher = userTeacherList.get(0);
                        Integer sysUserId = userTeacher.getSysUserId();
                        SysUser sysUser = sysUserService.findById(sysUserId);
                        if (!isEmpty(sysUser) && DataStatus.Y.getCode().equals(sysUser.getStatus()) && !isEmpty(sysUser.getDingUserId())){
                            BatchSendOTORequest batchSendOTORequest = new BatchSendOTORequest();
                            batchSendOTORequest.setRobotCode(com.api.constant.App.DING_LXFK_MESSAGE_ROBOT_CODE);
                            batchSendOTORequest.setMsgKey(DingMessageKey.SAMPLE_TEXT.getCode());
                            batchSendOTORequest.setUserIds(Collections.singletonList(String.valueOf(sysUser.getDingUserId())));
                            DingMessageSampleTextParam sampleTextParam = new DingMessageSampleTextParam();
                            String content = "律学-【学员考试低分提醒】你名下的线索编号为：" + user.getId() + "学员近期模拟考试：" + examPaperName + "（模拟考试试卷名称），分数" + userExamPaperRecordCreateOrModify.getSumScore().stripTrailingZeros().toPlainString() + "分，试卷总分：" + userExamPaperRecordCreateOrModify.getAllScore().stripTrailingZeros().toPlainString() + "分，得分率为" + userExamPaperRecordCreateOrModify.getScoreRate().stripTrailingZeros().toPlainString() + "%，请通知提醒学员努力学习，夜色难免黑凉 前行必有曙光。";
                            sampleTextParam.setContent(content);
                            batchSendOTORequest.setMsgParam(this.getJSON(sampleTextParam));
                            MQProducer.INSTANCE.sendOneway(LogSequence.get(), MQTopic.LXFK_API, MQTag.LXFK_API_DING_MESSAGE_MESSAGE, this.getJSON(batchSendOTORequest));
                        }
                    }
                }
            }

            //7 发消息: 交卷后自动调用AI评分
            for (UserExamPaperRecordDetail userExamPaperRecordDetail : userExamPaperRecordDetailCreateOrModifyList) {
                //这里已经对后台关闭评分功能的试卷做了过滤
                if (!UserExamPaperRecordDetailAiScoreStatus.IN.getCode().equals(userExamPaperRecordDetail.getAiScoreStatus())){
                    continue;
                }
                //只对评分中的记录进行处理
                MQProducer.INSTANCE.sendOneway(LogSequence.get(), MQTopic.LXFK_API, MQTag.LXFK_API_AI_SCORE_STATUS, userExamPaperRecordDetail.getId().toString());
            }

            return new Response<>(OK,SUCCESS,userExamPaperRecordCreateOrModify.getId());
        }catch (Exception e){
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        } finally {
            lock.unlock();
        }
    }

}
