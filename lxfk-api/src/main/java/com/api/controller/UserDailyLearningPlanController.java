package com.api.controller;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.locks.Lock;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.api.bean.OrderProductResponseV3;
import com.api.bean.ProjectItemResponse;
import com.api.bean.Response;
import com.api.bean.UserDailyLearningPlanDayResponse;
import com.api.bean.UserDailyLearningPlanRequest;
import com.api.bean.UserDailyLearningPlanResponse;
import com.api.constant.App;
import com.api.constant.DataStatus;
import com.api.validator.UserDailyLearningPlanValidator;
import com.common.constant.CacheKey;
import com.common.constant.PublicYesNo;
import com.common.constant.UserDailyLearningPlanCourseAllocationStage;
import com.common.constant.UserDailyLearningPlanDayDayType;
import com.common.constant.UserDailyLearningPlanDayDetailTaskType;
import com.common.constant.UserDailyLearningPlanProjectItemColor;
import com.common.constant.UserDailyLearningPlanRestStudyTime;
import com.common.constant.UserDailyLearningPlanSeeLive;
import com.common.constant.UserDailyLearningPlanWeekdayStudyTime;
import com.common.util.DateUtil;
import com.domain.ProjectItem;
import com.domain.User;
import com.domain.UserDailyLearningPlan;
import com.domain.UserDailyLearningPlanDay;
import com.domain.UserDailyLearningPlanDayDetail;
import com.domain.complex.UserDailyLearningPlanCourseQuery;
import com.domain.complex.UserDailyLearningPlanDayDetailQuery;
import com.domain.complex.UserDailyLearningPlanDayQuery;
import com.domain.complex.UserDailyLearningPlanQuery;
import com.service.ProjectItemService;
import com.service.UserDailyLearningPlanCourseService;
import com.service.UserDailyLearningPlanDayDetailService;
import com.service.UserDailyLearningPlanDayService;
import com.service.UserDailyLearningPlanService;
import com.service.UserService;

/**
 * 用户每日学习计划表
 *
 * @date 2025-07-28 14:15:56
 */
@RestController
public class UserDailyLearningPlanController extends VideoBaseControllerV3 {
    @Autowired
    private UserDailyLearningPlanService userDailyLearningPlanService;
    @Autowired
    private UserDailyLearningPlanDayService userDailyLearningPlanDayService;
    @Autowired
    private UserService userService;
    @Autowired
    private UserDailyLearningPlanCourseService userDailyLearningPlanCourseService;
    @Autowired
    private ProjectItemService projectItemService;
    @Autowired
    private RedisTemplate<String,String> redisTemplate;
    @Autowired
    private UserDailyLearningPlanDayDetailService userDailyLearningPlanDayDetailService;


    /**
     * 查询用户月度学习规律,包括月度任务,每日任务  (如果没有,则创建,并返回)
     * <AUTHOR>
     * @date 2025-07-29
     */
    @RequestMapping("/v1/user/daily/learning/plain/all/query")
    public Response<?> queryByUserId(@RequestBody UserDailyLearningPlanRequest request) {
        Integer userId = request.getUserId();
        Integer projectId = request.getProjectId();
        String lockKey = "" + userId + projectId;
        Lock lock = getLock(redisTemplate, CacheKey.USER_DAILY_LEARNING_PLAN_DEFAULT_CREATE_LOCK, lockKey);
        if (!lock.tryLock()) {
            return new Response<>(ERROR, "请勿点击过快");
        }
        try {
            //默认使用当前月
            Date currentMonth = isEmpty(request.getMonth()) ? getServerTime() : DateUtil.parse(request.getMonth(), DATE_MONTH_FORMAT);
            Date serverTime = getServerTime();
            UserDailyLearningPlanValidator validator = new UserDailyLearningPlanValidator();
            if (!validator.onUserId(userId).onProjectId(projectId).result()){
                return new Response<>(ERROR, validator.getErrorMessage());
            }
            //获取月初的第一天
            Date monthStartTime = DateUtil.getMonthStartTime(currentMonth);

            //待返回对象(新建的或者查询到的)
            UserDailyLearningPlan userDailyLearningPlan;
            List<UserDailyLearningPlanDay> userDailyLearningPlanDayList;
            int waitAllocatedCourseNumber = 0;

            //查询用户当月学习规律(每个用户每个月只会存在一条记录)
            UserDailyLearningPlanQuery userDailyLearningPlanQuery = new UserDailyLearningPlanQuery();
            userDailyLearningPlanQuery.setStatus(DataStatus.Y.getCode());
            userDailyLearningPlanQuery.setUserId(userId);
            userDailyLearningPlanQuery.setProjectId(projectId);
            userDailyLearningPlanQuery.setMonth(monthStartTime);
            List<UserDailyLearningPlan> userDailyLearningPlanList = userDailyLearningPlanService.findAll(userDailyLearningPlanQuery);
            if (!isEmpty(userDailyLearningPlanList) && !userDailyLearningPlanList.isEmpty()){
                userDailyLearningPlan = userDailyLearningPlanList.get(0);
                //继续查询每日计划
                UserDailyLearningPlanDayQuery userDailyLearningPlanDayQuery = new UserDailyLearningPlanDayQuery();
                userDailyLearningPlanDayQuery.setStatus(DataStatus.Y.getCode());
                userDailyLearningPlanDayQuery.setUserDailyLearningPlanId(userDailyLearningPlan.getId());
                //这个地方因为是一块创建的,所以一定不为空, 但是该判断还是判断, 如果为空了,就返回一个空的日历列表呗
                userDailyLearningPlanDayList = userDailyLearningPlanDayService.findAll(userDailyLearningPlanDayQuery);

                //查询未分配的视频数量
                UserDailyLearningPlanCourseQuery userDailyLearningPlanCourseQuery = new UserDailyLearningPlanCourseQuery();
                userDailyLearningPlanCourseQuery.setStatus(DataStatus.Y.getCode());
                userDailyLearningPlanCourseQuery.setUserDailyLearningPlanId(userDailyLearningPlan.getId());
                userDailyLearningPlanCourseQuery.setAllocationStage(UserDailyLearningPlanCourseAllocationStage.NO.getCode());
                waitAllocatedCourseNumber = userDailyLearningPlanCourseService.count(userDailyLearningPlanCourseQuery);
            } else {
                //如果不存在任意记录,则创建
                userDailyLearningPlan = this.getDefaultUserDailyLearningPlan(monthStartTime, userId, projectId, serverTime);
                userDailyLearningPlanDayList = this.getDefaultUserDailyLearningPlanDayList(monthStartTime, userId, userDailyLearningPlan.getCustomerId(), serverTime);
                userDailyLearningPlanService.create(userDailyLearningPlan, userDailyLearningPlanDayList);
            }

            //构造响应数据
            UserDailyLearningPlanResponse response = new UserDailyLearningPlanResponse();
            response.setId(userDailyLearningPlan.getId());
            //学习日学习时长
            response.setWeekdayStudyTime(userDailyLearningPlan.getWeekdayStudyTime());
            //休息日学习时长
            response.setRestStudyTime(userDailyLearningPlan.getRestStudyTime());

            //学习顺序(科目列表)
            List<ProjectItemResponse> projectItemResponseList = new ArrayList<>();
            if (!isEmpty(userDailyLearningPlan.getProjectItemIds())){
                List<Integer> projectItemIdList = new ArrayList<>();
                for (String projectItemIdStr : userDailyLearningPlan.getProjectItemIds().split(App.COMMA)) {
                    projectItemIdList.add(Integer.parseInt(projectItemIdStr));
                }
                if (!projectItemIdList.isEmpty()){
                    List<ProjectItem> projectItemList = projectItemService.findByIds(projectItemIdList);
                    if (!isEmpty(projectItemList) && !projectItemList.isEmpty()){
                        Map<Integer, ProjectItem> projectItemMap = projectItemList.stream().collect(Collectors.toMap(ProjectItem::getId, Function.identity()));
                        for (Integer projectItemId : projectItemIdList) {
                            ProjectItemResponse projectItemResponse = new ProjectItemResponse();
                            //科目id
                            projectItemResponse.setId(projectItemId);
                            if (projectItemMap.containsKey(projectItemId)){
                                ProjectItem projectItem = projectItemMap.get(projectItemId);
                                //科目名称
                                projectItemResponse.setName(projectItem.getName());
                                //颜色
                                UserDailyLearningPlanProjectItemColor color = UserDailyLearningPlanProjectItemColor.getByName(projectItem.getName());
                                if (color != null){
                                    projectItemResponse.setColor(color.getSysColor());
                                }
                            }
                            projectItemResponseList.add(projectItemResponse);
                        }
                    }
                }
            }
            response.setProjectItemResponseList(projectItemResponseList);

            //是否跟直播(每天的也从这里面取)
            response.setSeeLive(userDailyLearningPlan.getSeeLive());
            //日历列表
            List<UserDailyLearningPlanDayResponse> userDailyLearningPlanDayResponseList = new ArrayList<>();
            if (!isEmpty(userDailyLearningPlanDayList) && !userDailyLearningPlanDayList.isEmpty()){
                userDailyLearningPlanDayList.sort(Comparator.comparing(UserDailyLearningPlanDay::getPlanDay));
                for (UserDailyLearningPlanDay userDailyLearningPlanDay : userDailyLearningPlanDayList) {
                    UserDailyLearningPlanDayResponse userDailyLearningPlanDayResponse = new UserDailyLearningPlanDayResponse();
                    //数据编号
                    userDailyLearningPlanDayResponse.setId(userDailyLearningPlanDay.getId());
                    //日期
                    if (!isEmpty(userDailyLearningPlanDay.getPlanDay())){
                        userDailyLearningPlanDayResponse.setPlanDay(DateUtil.format(userDailyLearningPlanDay.getPlanDay(), DATE_FORMAT));
                    }
                    //日期类型
                    userDailyLearningPlanDayResponse.setDayType(userDailyLearningPlanDay.getDayType());
                    //学习时长
                    Integer taskLength = userDailyLearningPlanDay.getTaskLength();
                    userDailyLearningPlanDayResponse.setTaskLength(taskLength);
                    //学习时长(枚举,展示)
                    if (!isEmpty(taskLength)){
                        if (UserDailyLearningPlanDayDayType.WEEKDAY.getCode().equals(userDailyLearningPlanDay.getDayType())){
                            UserDailyLearningPlanWeekdayStudyTime weekdayStudyTime = UserDailyLearningPlanWeekdayStudyTime.getByMin(taskLength);
                            if (weekdayStudyTime != null){
                                userDailyLearningPlanDayResponse.setTaskLengthCode(weekdayStudyTime.getCode());
                                userDailyLearningPlanDayResponse.setTaskLengthName(weekdayStudyTime.getName());
                            }
                        } else if (UserDailyLearningPlanDayDayType.RESET.getCode().equals(userDailyLearningPlanDay.getDayType())){
                            UserDailyLearningPlanRestStudyTime restStudyTime = UserDailyLearningPlanRestStudyTime.getByMin(taskLength);
                            if (restStudyTime != null){
                                userDailyLearningPlanDayResponse.setTaskLengthCode(restStudyTime.getCode());
                                userDailyLearningPlanDayResponse.setTaskLengthName(restStudyTime.getName());
                            }
                        }
                    }

                    //是否跟直播(跟随月份走,这个字段弃用)
                    //userDailyLearningPlanDayResponse.setSeeLive(userDailyLearningPlanDay.getSeeLive());
                    userDailyLearningPlanDayResponseList.add(userDailyLearningPlanDayResponse);
                }
            }
            response.setUserDailyLearningPlanDayResponseList(userDailyLearningPlanDayResponseList);
            //待分配视频数量
            response.setWaitAllocatedCourseNumber(waitAllocatedCourseNumber);

            //绑定的视频列表是另外一个接口查询(分页)
            return new Response<>(OK, SUCCESS, response);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        } finally {
            lock.unlock();
        }
    }

    /**
     * 获取一个默认的用户月度学习规律,用于持久化
     * <AUTHOR>
     * @date 2025-07-29
     */
    private UserDailyLearningPlan getDefaultUserDailyLearningPlan(Date monthStartTime, Integer userId, Integer projectId, Date serverTime) {
        UserDailyLearningPlan userDailyLearningPlanCreate = new UserDailyLearningPlan();
        //月份,每月的第一天
        userDailyLearningPlanCreate.setMonth(monthStartTime);
        //项目编号
        userDailyLearningPlanCreate.setProjectId(projectId);
        //userDailyLearningPlanCreate.setCustomerId(request.getCustomerId());
        userDailyLearningPlanCreate.setUserId(userId);
        //查询用户所拥有的科目,并按照默认顺序拼接ID(,)
        User user = userService.findById(userId);
        if (user != null && user.getCustomerId() != null){
            //customerId
            userDailyLearningPlanCreate.setCustomerId(user.getCustomerId());
            //科目
            OrderProductResponseV3 orderProductResponseV3 = queryUserPurchasedProductDetail(projectId, user.getCustomerId());
            if (orderProductResponseV3 != null && !isEmpty(orderProductResponseV3.getProjectItemAllList()) && !orderProductResponseV3.getProjectItemAllList().isEmpty()){
                userDailyLearningPlanCreate.setProjectItemIds(orderProductResponseV3.getProjectItemAllList().stream().map(ProjectItem::getId).map(String::valueOf).collect(Collectors.joining(App.COMMA)));
            }
        }
        //设置默认学习日学习时长
        userDailyLearningPlanCreate.setWeekdayStudyTime(UserDailyLearningPlanWeekdayStudyTime.H1.getCode());
        //设置默认休息日学习时长
        userDailyLearningPlanCreate.setRestStudyTime(UserDailyLearningPlanRestStudyTime.H1.getCode());
        //默认跟直播(参照原型图的默认选中值)
        userDailyLearningPlanCreate.setSeeLive(UserDailyLearningPlanSeeLive.Y.getCode());

        userDailyLearningPlanCreate.setStatus(DataStatus.Y.getCode());
        userDailyLearningPlanCreate.setCreateTime(serverTime);
        userDailyLearningPlanCreate.setModifyTime(serverTime);
        return userDailyLearningPlanCreate;
    }

    /**
     * 获取一个默认的用户月度-每日学习计划,用于持久化
     * <AUTHOR>
     * @date 2025-07-29
     */
    private List<UserDailyLearningPlanDay> getDefaultUserDailyLearningPlanDayList(Date monthStartTime, Integer userId, Integer customerId, Date serverTime) {
        List<UserDailyLearningPlanDay> result = new ArrayList<>();
        //循环获取这个月的每一天,有几天,就创建几条 UserDailyLearningPlanDay 全部设置为默认值
        for (Date day : DateUtil.getMonthDates(monthStartTime)) {
            UserDailyLearningPlanDay userDailyLearningPlanDay = new UserDailyLearningPlanDay();
            //用户编号
            userDailyLearningPlanDay.setUserId(userId);
            //customerId
            userDailyLearningPlanDay.setCustomerId(customerId);
            //日期
            userDailyLearningPlanDay.setPlanDay(day);
            //学习类型(默认为学习日)
            userDailyLearningPlanDay.setDayType(UserDailyLearningPlanDayDayType.WEEKDAY.getCode());
            //学习时长任务量(分钟)
            userDailyLearningPlanDay.setTaskLength(UserDailyLearningPlanRestStudyTime.H1.getMin());
            //是否完成
            //userDailyLearningPlanDay.setOver(PublicOver.N.getCode());
            ///是否跟直播字段不再使用,是否跟直播的回显是根据月度规律来走的
            //userDailyLearningPlanDay.setSeeLive(UserDailyLearningPlanSeeLive.Y.getCode());

            userDailyLearningPlanDay.setStatus(DataStatus.Y.getCode());
            userDailyLearningPlanDay.setCreateTime(serverTime);
            userDailyLearningPlanDay.setModifyTime(serverTime);
            result.add(userDailyLearningPlanDay);
        }
        return result;
    }

    /**
     * 修改用户月度学习规律,更新绑定的每日学习计划,重新分配视频(只能明天及以后的)
     * <AUTHOR>
     * @date 2025-07-30
     */
    @RequestMapping("/v1/user/daily/learning/plain/modify")
    public Response<?> modify(@RequestBody UserDailyLearningPlanRequest request) {
        try {
            Integer id = request.getId();
            String weekdayStudyTime = request.getWeekdayStudyTime();
            String restStudyTime = request.getRestStudyTime();
            String projectItemIds = request.getProjectItemIds();
            String seeLive = request.getSeeLive();

            UserDailyLearningPlanValidator validator = new UserDailyLearningPlanValidator();
            if (!validator.onId(id).onWeekdayStudyTime(weekdayStudyTime).onRestStudyTime(restStudyTime).onProjectItemIds(projectItemIds).onSeeLive(seeLive).result()){
                return new Response<>(ERROR, validator.getErrorMessage());
            }

            UserDailyLearningPlan userDailyLearningPlan = userDailyLearningPlanService.findById(id);
            if (userDailyLearningPlan == null || !userDailyLearningPlan.getStatus().equals(DataStatus.Y.getCode()) || isEmpty(userDailyLearningPlan.getMonth())){
                return new Response<>(ERROR, "数据编号无效");
            }

            // 只能修改当月及以后月份的数据
            Date serverTime = getServerTime();
            Date taskMonth = userDailyLearningPlan.getMonth();  //任务月份
            Date currentMonth = DateUtil.getMonthStartTime(serverTime);     //当前系统时间所在月份
            if (taskMonth.getTime() < currentMonth.getTime()){
                //之前的月份不能修改
                return new Response<>(ERROR, "只能修改本月及以后的数据");
            } else if (taskMonth.getTime() == currentMonth.getTime()){
                //当前的月份不能在月底这天修改(只能修改今天以后的数据)
                LocalDate localDate = serverTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
                if (localDate.getDayOfMonth() == localDate.lengthOfMonth()){
                    return new Response<>(ERROR, "月底最后一天修改计划不生效");
                }
            }

            // *** 月度任务更新对象
            UserDailyLearningPlan userDailyLearningPlanModify = new UserDailyLearningPlan();
            userDailyLearningPlanModify.setId(id);
            userDailyLearningPlanModify.setWeekdayStudyTime(weekdayStudyTime);
            userDailyLearningPlanModify.setRestStudyTime(restStudyTime);
            userDailyLearningPlanModify.setProjectItemIds(projectItemIds);
            userDailyLearningPlanModify.setSeeLive(seeLive);
            userDailyLearningPlanModify.setModifyTime(serverTime);

            //查询需要重新分配视频的日期列表 本月就是明天开始到月底,次月就是整月,不支持上月
            UserDailyLearningPlanDayQuery userDailyLearningPlanDayQuery = new UserDailyLearningPlanDayQuery();
            userDailyLearningPlanDayQuery.setStatus(DataStatus.Y.getCode());
            userDailyLearningPlanDayQuery.setUserDailyLearningPlanId(id);
            userDailyLearningPlanDayQuery.setMinPlanDay(serverTime);
            List<UserDailyLearningPlanDay> userDailyLearningPlanDayList = userDailyLearningPlanDayService.findAll(userDailyLearningPlanDayQuery);
            if (isEmpty(userDailyLearningPlanDayList) || userDailyLearningPlanDayList.isEmpty()){
                return new Response<>(ERROR, "用户计划数据有误");
            }

            List<UserDailyLearningPlanDay> userDailyLearningPlanDayModifyList = new ArrayList<>();
            for (UserDailyLearningPlanDay userDailyLearningPlanDay : userDailyLearningPlanDayList) {
                UserDailyLearningPlanDay userDailyLearningPlanDayModify = new UserDailyLearningPlanDay();
                //ID
                userDailyLearningPlanDayModify.setId(userDailyLearningPlanDay.getId());
                //学习时长任务量(分钟)
                Integer taskLength;
                if (UserDailyLearningPlanDayDayType.WEEKDAY.getCode().equals(userDailyLearningPlanDay.getDayType())){
                    taskLength = UserDailyLearningPlanWeekdayStudyTime.get(request.getWeekdayStudyTime()).getMin();
                } else if (UserDailyLearningPlanDayDayType.RESET.getCode().equals(userDailyLearningPlanDay.getDayType())){
                    taskLength = UserDailyLearningPlanRestStudyTime.get(request.getRestStudyTime()).getMin();
                } else {
                    taskLength = 0;
                }
                userDailyLearningPlanDayModify.setTaskLength(taskLength);
                userDailyLearningPlanDayModify.setModifyTime(serverTime);
                userDailyLearningPlanDayModifyList.add(userDailyLearningPlanDayModify);
            }

            userDailyLearningPlanService.modifyById(userDailyLearningPlanModify, userDailyLearningPlanDayModifyList);
            return new Response<>(OK, SUCCESS);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }


    /**
     * 查询用户学习计划学习记录
     * <AUTHOR>
     * @date 2025-08-04
     */
    @RequestMapping("/v1/user/plan/learning/record/query")
    public Response<?> queryUserPlanLearningRecord(@RequestBody UserDailyLearningPlanRequest request) {
        try {
            List<UserDailyLearningPlanResponse> responses = new ArrayList<>();
            if (isEmpty(request.getProjectId())) {
                return new Response<>(ERROR,"项目编号不能为空");
            }
            if (isEmpty(request.getCustomerId())) {
                return new Response<>(ERROR,"客户编号不能为空");
            }
            // 查询用户对应项目下的每月计划
            UserDailyLearningPlanQuery userDailyLearningPlanQuery = new UserDailyLearningPlanQuery();
            userDailyLearningPlanQuery.setProjectId(request.getProjectId());
            userDailyLearningPlanQuery.setCustomerId(request.getCustomerId());
            userDailyLearningPlanQuery.setStatus(DataStatus.Y.getCode());
            List<UserDailyLearningPlan> userDailyLearningPlanList = userDailyLearningPlanService.findAll(userDailyLearningPlanQuery);
            if (!isEmpty(userDailyLearningPlanList) && !userDailyLearningPlanList.isEmpty()) {
                // 获取所有月规划编号 查询用户月规划下的日规划
                List<Integer> userDailyLearningPlanIds = userDailyLearningPlanList.stream().map(UserDailyLearningPlan::getId).collect(Collectors.toList());
                UserDailyLearningPlanDayQuery userDailyLearningPlanDayQuery = new UserDailyLearningPlanDayQuery();
                userDailyLearningPlanDayQuery.setUserDailyLearningPlanIds(userDailyLearningPlanIds);
                userDailyLearningPlanDayQuery.setCustomerId(request.getCustomerId());
                userDailyLearningPlanDayQuery.setStatus(DataStatus.Y.getCode());
                List<UserDailyLearningPlanDay> userDailyLearningPlanDayList = userDailyLearningPlanDayService.findAll(userDailyLearningPlanDayQuery);
                if (isEmpty(userDailyLearningPlanDayList) || userDailyLearningPlanDayList.isEmpty()) {
                    return new Response<>(OK,SUCCESS,responses);
                }
                // 获取每月规划下的每日规划map<月规划编号,List<日规划编号>>
                Map<Integer,List<Integer>> userDailyLearningPlanDayMap = new HashMap<>();
                // 每日规划编号集合
                List<Integer> userDailyLearningPlanDayIds = new ArrayList<>();
                for (UserDailyLearningPlanDay userDailyLearningPlanDay : userDailyLearningPlanDayList) {
                    userDailyLearningPlanDayIds.add(userDailyLearningPlanDay.getId());
                    if (userDailyLearningPlanDayMap.containsKey(userDailyLearningPlanDay.getUserDailyLearningPlanId())) {
                        userDailyLearningPlanDayMap.get(userDailyLearningPlanDay.getUserDailyLearningPlanId()).add(userDailyLearningPlanDay.getId());
                    } else {
                        List<Integer> userDailyLearningPlanDayIdList = new ArrayList<>();
                        userDailyLearningPlanDayIdList.add(userDailyLearningPlanDay.getId());
                        userDailyLearningPlanDayMap.put(userDailyLearningPlanDay.getUserDailyLearningPlanId(),userDailyLearningPlanDayIdList);
                    }
                }
                // 查询每日规划下的详细信息 获取map<每日规划编号,List<日规划绑定详情>>
                Map<Integer,List<UserDailyLearningPlanDayDetail>> userDailyLearningPlanDayDetailMap = new HashMap<>();
                UserDailyLearningPlanDayDetailQuery userDailyLearningPlanDayDetailQuery = new UserDailyLearningPlanDayDetailQuery();
                userDailyLearningPlanDayDetailQuery.setUserDailyLearningPlanDayIds(userDailyLearningPlanDayIds);
                userDailyLearningPlanDayDetailQuery.setStatus(DataStatus.Y.getCode());
                List<UserDailyLearningPlanDayDetail> userDailyLearningPlanDayDetailList = userDailyLearningPlanDayDetailService.findAll(userDailyLearningPlanDayDetailQuery);
                if (isEmpty(userDailyLearningPlanDayDetailList) || userDailyLearningPlanDayDetailList.isEmpty()) {
                    return new Response<>(OK,SUCCESS,responses);
                }
                for (UserDailyLearningPlanDayDetail userDailyLearningPlanDayDetail : userDailyLearningPlanDayDetailList) {
                    if (userDailyLearningPlanDayDetailMap.containsKey(userDailyLearningPlanDayDetail.getUserDailyLearningPlanDayId())) {
                        userDailyLearningPlanDayDetailMap.get(userDailyLearningPlanDayDetail.getUserDailyLearningPlanDayId()).add(userDailyLearningPlanDayDetail);
                    } else {
                        List<UserDailyLearningPlanDayDetail> userDailyLearningPlanDays = new ArrayList<>();
                        userDailyLearningPlanDays.add(userDailyLearningPlanDayDetail);
                        userDailyLearningPlanDayDetailMap.put(userDailyLearningPlanDayDetail.getUserDailyLearningPlanDayId(),userDailyLearningPlanDays);
                    }
                }

                // 循环组装返回数据
                for (UserDailyLearningPlan userDailyLearningPlan : userDailyLearningPlanList) {
                    UserDailyLearningPlanResponse response = new UserDailyLearningPlanResponse();
                    String date = DateUtil.format(userDailyLearningPlan.getMonth(), DATE_MONTH_FORMAT);
                    response.setViewTime(date);
                    response.setCustomerId(request.getCustomerId());
                    // 计划视频数量
                    int planVideoNumber = 0;
                    // 计划试卷数量
                    int planExamNumber = 0;
                    // 计划直播数量
                    int planLiveNumber = 0;
                    // 完成视频数量
                    int completeVideoNumber = 0;
                    // 完成试卷数量
                    int completeExamNumber = 0;
                    // 完成直播数量
                    int completeLiveNumber = 0;
                    // 月规划下的日规划
                    if (userDailyLearningPlanDayMap.containsKey(userDailyLearningPlan.getId())) {
                        List<Integer> userDailyLearningPlanDays = userDailyLearningPlanDayMap.get(userDailyLearningPlan.getId());
                        if (!userDailyLearningPlanDays.isEmpty()) {
                            for (Integer userDailyLearningPlanDayId : userDailyLearningPlanDays) {
                                if (userDailyLearningPlanDayDetailMap.containsKey(userDailyLearningPlanDayId)) {
                                    for (UserDailyLearningPlanDayDetail planDayDetail : userDailyLearningPlanDayDetailMap.get(userDailyLearningPlanDayId)) {
                                        if (UserDailyLearningPlanDayDetailTaskType.COURSE.getCode().equals(planDayDetail.getTaskType())) {
                                            planVideoNumber = planVideoNumber + 1;
                                            // 是否完成学习任务
                                            if (PublicYesNo.Y.getCode().equals(planDayDetail.getOver())) {
                                                completeVideoNumber = completeVideoNumber + 1;
                                            }
                                        } else if (UserDailyLearningPlanDayDetailTaskType.EXAM.getCode().equals(planDayDetail.getTaskType())) {
                                            planExamNumber = planExamNumber + 1;
                                            // 是否完成学习任务
                                            if (PublicYesNo.Y.getCode().equals(planDayDetail.getOver())) {
                                                completeExamNumber = completeExamNumber + 1;
                                            }
                                        } else if (UserDailyLearningPlanDayDetailTaskType.LIVE.getCode().equals(planDayDetail.getTaskType())) {
                                            planLiveNumber = planLiveNumber + 1;
                                            // 是否完成学习任务
                                            if (PublicYesNo.Y.getCode().equals(planDayDetail.getOver())) {
                                                completeLiveNumber = completeLiveNumber + 1;
                                            }
                                        }
                                    }
                                }

                            }
                        }
                    }
                    response.setPlanVideoNumber(planVideoNumber);
                    response.setPlanExamNumber(planExamNumber);
                    response.setPlanLiveNumber(planLiveNumber);
                    response.setCompleteVideoNumber(completeVideoNumber);
                    response.setCompleteExamNumber(completeExamNumber);
                    response.setCompleteLiveNumber(completeLiveNumber);
                    responses.add(response);
                }
            }
            return new Response<>(OK, SUCCESS,responses);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }

}
