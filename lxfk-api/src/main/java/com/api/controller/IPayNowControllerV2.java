package com.api.controller;

import java.math.BigDecimal;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.locks.Lock;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.aliyun.dingtalkrobot_1_0.models.BatchSendOTORequest;
import com.api.bean.DingMessageSampleTextParam;
import com.api.bean.IPayNowResponse;
import com.api.bean.OrderQueryRequest;
import com.api.bean.OrderQueryResponse;
import com.api.bean.Response;
import com.api.bean.UserToken;
import com.api.config.MQProducer;
import com.api.config.Token;
import com.api.constant.App;
import com.api.constant.DataStatus;
import com.api.sequence.CommonSequence;
import com.api.sequence.LogSequence;
import com.common.bean.DingTalkRequest;
import com.common.bean.SMSPayload;
import com.common.client.IPayNowServcieClient;
import com.common.constant.CacheKey;
import com.common.constant.DepartmentCatalog;
import com.common.constant.Device;
import com.common.constant.DingMessageKey;
import com.common.constant.OrderAudit;
import com.common.constant.OrderQrcodeCatalog;
import com.common.constant.OrderStage;
import com.common.constant.PayChannel;
import com.common.constant.SMSType;
import com.common.constant.TokenType;
import com.common.constant.UserCatalog;
import com.common.constant.UserLogAction;
import com.common.constant.UserStage;
import com.common.mq.MQTag;
import com.common.mq.MQTopic;
import com.common.util.DateUtil;
import com.domain.Customer;
import com.domain.Department;
import com.domain.Order;
import com.domain.OrderFlow;
import com.domain.OrderFlowSplit;
import com.domain.OrderQrcode;
import com.domain.SysUser;
import com.domain.SysUserDepartment;
import com.domain.User;
import com.domain.UserLog;
import com.domain.complex.OrderFlowSplitQuery;
import com.domain.complex.OrderQrcodeQuery;
import com.domain.complex.OrderQueryV2;
import com.domain.complex.SysUserDepartmentQuery;
import com.domain.complex.SysUserMasterQuery;
import com.domain.complex.UserLogQuery;
import com.service.CustomerService;
import com.service.DepartmentRobotService;
import com.service.DepartmentService;
import com.service.ExplosiveFormworkService;
import com.service.OrderFlowService;
import com.service.OrderFlowSplitService;
import com.service.OrderQrcodeService;
import com.service.OrderService;
import com.service.SysUserDepartmentService;
import com.service.SysUserMasterService;
import com.service.SysUserService;
import com.service.UserLogService;
import com.service.UserService;

/**
 * 现在支付
 *
 * <AUTHOR>
 */
@Controller
public class IPayNowControllerV2 extends BaseController {
    @Autowired
    private OrderService orderService;
    @Autowired
    private UserService userService;
    @Autowired
    private DepartmentService departmentService;
    @Autowired
    private SysUserDepartmentService sysUserDepartmentService;
    @Autowired
    private SysUserService sysUserService;
    @Autowired
    private SysUserMasterService sysUserMasterService;
    @Autowired
    private RedisTemplate<String, String> redisTemplate;
    @Autowired
    private ExplosiveFormworkService explosiveFormworkService;
    @Autowired
    private OrderQrcodeService orderQrcodeService;
    @Autowired
    private DepartmentRobotService departmentRobotService;
    @Autowired
    private OrderFlowService orderFlowService;
    @Autowired
    private CustomerService customerService;
    @Autowired
    private UserLogService userLogService;
    @Autowired
    private OrderFlowSplitService orderFlowSplitService;

    /**
     * 现在支付统一下单接口
     *
     * <AUTHOR>
     */
    @RequestMapping(value = "/v2/ipaynow/cms/order/create")
    @Token
    public @ResponseBody
    Response<?> createV2(@RequestBody OrderQueryRequest request) {
        try {
            Date datetime = this.getServerTime();
            OrderQueryResponse response = new OrderQueryResponse();
            // 判断传入参数订单编号是否为空
            if (this.isEmpty(request.getId())) {
                return new Response<>(ERROR, "请输入订单编号");
            }
            // 查询订单信息
            OrderFlow orderFlow = this.orderFlowService.findById(request.getId());
            if (orderFlow == null) {
                return new Response<>(ERROR, "未查询到订单信息");
            }
            if (OrderAudit.REJECT.getCode().equals(orderFlow.getAudit())) {
                return new Response<>(ERROR, "此订单已经被驳回，无法支付");
            }
            if (OrderStage.Y.getCode().equals(orderFlow.getStage())) {
                return new Response<>(ERROR, "此订单已经支付,无须再次进行支付");
            }
            if (OrderStage.REFUND.getCode().equals(orderFlow.getStage())) {
                return new Response<>(ERROR, "此订单已经退款,无法支付");
            }
            if (DataStatus.N.getCode().equals(orderFlow.getStatus())) {
                return new Response<>(ERROR, "该订单已经被删除，无法支付");
            }
            if (BigDecimal.ZERO.compareTo(orderFlow.getAmount()) > -1) {
                return new Response<>(ERROR, "该订单是0元订单,请去手动审核通过");
            }
            // 查询是否已经已经生成了二维码
            OrderQrcodeQuery orderQrcodeQuery = new OrderQrcodeQuery();
            orderQrcodeQuery.setOrderId(orderFlow.getOrderId());
            orderQrcodeQuery.setOrderFlowId(request.getId());
            orderQrcodeQuery.setCatalog(OrderQrcodeCatalog.C0.getCode());
            orderQrcodeQuery.setStatus(DataStatus.Y.getCode());
            OrderQrcode orderQrcode = orderQrcodeService.findMax(orderQrcodeQuery);
            if (orderQrcode != null) {
                // 查看是否过期
                if (orderQrcode.getExpireTime().after(datetime)) {
                    // 没有过期 直接把二维码地址返回
                    // 返回二维码
                    response.setUrl(orderQrcode.getData());
                    // 返回订单号
                    response.setMhtOrderNo(orderQrcode.getPayCode());
                    // 返回金额
                    response.setAmount(orderFlow.getAmount().toString());
                    return new Response<>(response);
                }
            }
            // 生成订单号
            String payCode = this.getDateTime() + App.ID + CommonSequence.nextValue() + orderFlow.getId();
            // 记录相关信息
            OrderQrcode orderQrcodeCreate = new OrderQrcode();
            orderQrcodeCreate.setOrderId(orderFlow.getOrderId());
            orderQrcodeCreate.setOrderFlowId(orderFlow.getId());
            orderQrcodeCreate.setStatus(DataStatus.Y.getCode());
            orderQrcodeCreate.setPayCode(payCode);
            orderQrcodeCreate.setCatalog(OrderQrcodeCatalog.C0.getCode());
            orderQrcodeCreate.setModifyTime(datetime);
            orderQrcodeCreate.setCreateTime(datetime);
            IPayNowServcieClient iPayNowServcieClient = new IPayNowServcieClient();
            //做MD5签名
            Map<String, String> dataMap = new HashMap<String, String>();
            dataMap.put("appId", IPayNowServcieClient.APP_ID);
            dataMap.put("deviceType", "20");
            dataMap.put("funcode", "WP001");
            dataMap.put("mhtOrderNo", payCode);
            dataMap.put("mhtOrderName", com.common.constant.App.COMPANY_NAME);
            dataMap.put("version", "1.0.2");
            dataMap.put("mhtCurrencyType", "156");
            dataMap.put("mhtOrderAmt", this.getPrice(orderFlow.getAmount().multiply(new BigDecimal(100))).toString());
            dataMap.put("mhtOrderDetail", "课程支付");
            dataMap.put("mhtOrderType", "01");
            dataMap.put("mhtOrderTimeOut", "86400");
            dataMap.put("mhtOrderStartTime", this.getDateTime());
            dataMap.put("notifyUrl", this.getServerName() + "/v2/ipaynow/cms/pay/notify/create");
            //	dataMap.put("frontNotifyUrl", frontNotifyUrl);
            dataMap.put("mhtCharset", StandardCharsets.UTF_8.name());
            dataMap.put("mhtSignType", "MD5");
            dataMap.put("outputType", "1");
            //只有当payChennalType的值为13且outputType的值为0时，consumerCreateIp需要传（IP需真实有效）
//			if (outputType.equals("5")|outputType.equals("1")&&payChannelType.equals("13")){
//				dataMap.put("consumerCreateIp", consumerCreateIp);	
//			}

            //	dataMap.put("mhtReserved", mhtReserved);//商户保留域字段不必填，如果商户有需要对每笔交易记录一些自己的东西，可以放在这个里面
            String mhtSignature = iPayNowServcieClient.getFormDataParamMD5(dataMap, IPayNowServcieClient.APP_KEY, StandardCharsets.UTF_8.name());
            dataMap.put("mhtSignature", mhtSignature);
            String clientResponse = iPayNowServcieClient.execute(iPayNowServcieClient.getMultiValueMap(dataMap));
            String[] tn = clientResponse.split("&");
            for (int i = 0; i < tn.length; i++) {
                if (tn[i].startsWith("tn=")) {
                    String tnValue = tn[i].substring(3);
                    // 记录地址并返回
                    String url = URLDecoder.decode(tnValue, StandardCharsets.UTF_8.name());
                    orderQrcodeCreate.setData(url);
                    // 过期时间是24小时
                    orderQrcodeCreate.setExpireTime(DateUtil.getFutureHour(datetime, 24));
                    orderQrcodeService.create(orderQrcodeCreate);
                    // 返回二维码
                    response.setUrl(url);
                    // 返回订单号
                    response.setMhtOrderNo(payCode);
                    // 返回金额
                    response.setAmount(orderFlow.getAmount().toString());
                    return new Response<Object>(response);
                }
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return new Response<>(ERROR, FAILURE);
    }

    /**
     * 支付通知
     * <P>增加用户成单状态记录  马喜明 2022/03/13</P>
     * <P>机器人报单调整到订单审核通过之后  liangshilei 2022/04/11</P>
     * <p>机器人报单增加支付时间。郭鹏 2022-06-24</p>
     * <p>去掉报单里的支付时间。郭鹏 2022-06-27</p>
     * <p>报单机器人异步处理。王帅朋 2023-05-04</p>
     * <p>手动回库数据3小时内被他人认领且认领后48小时内成单发送提醒。王帅朋 2023-08-15</p>
     * <p>代码优化。王帅朋 2023-09-04</p>
     * <p>添加IPayNowReconciliationTaskV2定时任务,通过支付订单查询的方式实现对账,防止并发通知,添加锁。李明洋 2025-08-18</p>
     * <AUTHOR>
     */
    @RequestMapping(value = "/v2/ipaynow/cms/pay/notify/create", produces = MediaType.TEXT_PLAIN_VALUE)
    public @ResponseBody
    String createNotify() {
        try {
            String mhtOrderNo = this.httpServletRequest.getParameter("mhtOrderNo");
            //加锁 见 IPayNowReconciliationTaskV2.start()注释的第4步,防止主动查询订单状态时出现并发问题
            Lock lock = getLock(redisTemplate, CacheKey.I_PAY_NOW_CREATE_NOTIFY_LOCK, mhtOrderNo);
            try {
                if (!lock.tryLock()) {
                   //没拿到锁,可能是定时任务主动查询到了,并开始走通知逻辑,这里先认为定时任务最终会处理失败,返给第三方一个空,等待第三方下次再调用,就算定时任务执行成功了,下次进来也会检查审核状态
                    return null;
                }
                // 支付方式
                String payChannelType = this.httpServletRequest.getParameter("payChannelType");
                // 设备类型
                String deviceType = this.httpServletRequest.getParameter("deviceType");
                logger.info("deviceType : {}",deviceType);
                // 查询该支付订单信息
                String orderNo = mhtOrderNo.substring(24);
                OrderFlow orderFlow = this.orderFlowService.findById(Integer.parseInt(orderNo));
                Order order = this.orderService.findById(orderFlow.getOrderId());
                OrderFlowSplitQuery orderFlowSplitQuery = new OrderFlowSplitQuery();
                orderFlowSplitQuery.setOrderFlowId(orderFlow.getId());
                List<OrderFlowSplit> orderFlowSplitList = orderFlowSplitService.findAll(orderFlowSplitQuery);
                if (!orderFlowSplitList.isEmpty() && order != null && OrderAudit.ING.getCode().equals(orderFlow.getAudit())) {
                    //业务逻辑审核前同一流水下只会有一条拆分数据
                    OrderFlowSplit orderFlowSplit = orderFlowSplitList.get(0);
                    Date datetime = this.getServerTime();
                    User user = this.userService.findById(order.getUserId());
                    if (user != null) {
                        //查询用户手机号
                        Customer customer = this.customerService.findById(user.getCustomerId());
                        User userModify = new User();
                        userModify.setId(user.getId());
                        userModify.setCatalog(UserCatalog.C2.getCode());
                        userModify.setStage(UserStage.N.getCode());
                        userModify.setModifyTime(datetime);
                        // 更新订单
                        OrderQueryV2 orderQueryV2 = new OrderQueryV2();
                        // 记录用户当时成单状态
                        orderQueryV2.setUserCatalog(user.getCatalog());
                        orderQueryV2.setOrderFlowId(orderFlow.getId());
                        // 12 支付宝 36 支付宝花呗分期
                        if ("12".equals(payChannelType) || "36".equals(payChannelType)) {
                            orderQueryV2.setPayChannel(PayChannel.IPAYNOW_ALIPAY.getCode());
                        }
                        // 13 微信
                        if ("13".equals(payChannelType)) {
                            orderQueryV2.setPayChannel(PayChannel.IPAYNOW_WXPAY.getCode());
                        }
                        if ("01".equals(deviceType)) {
                            // 12 支付宝
                            if ("12".equals(payChannelType)) {
                                orderQueryV2.setPayChannel(PayChannel.IPAYNOW_LX_APP_ALIPAY.getCode());
                            }
                            // 13 微信
                            if ("13".equals(payChannelType)) {
                                orderQueryV2.setPayChannel(PayChannel.IPAYNOW_LX_APP_WXPAY.getCode());
                            }
                        }
                        if (!isEmpty(orderFlowSplit.getChanceUserId())) {
                            //查询业绩坐席是不是班主任
                            SysUser sysUser = sysUserService.findById(orderFlowSplit.getChanceUserId());
                            if (!isEmpty(sysUser) && DataStatus.Y.getCode().equals(sysUser.getStatus())) {
                                SysUserDepartmentQuery sysUserDepartmentQuery = new SysUserDepartmentQuery();
                                sysUserDepartmentQuery.setStatus(DataStatus.Y.getCode());
                                sysUserDepartmentQuery.setUserId(sysUser.getId());
                                List<SysUserDepartment> sysUserDepartments = sysUserDepartmentService.findAll(sysUserDepartmentQuery);
                                if (!isEmpty(sysUserDepartments) && !sysUserDepartments.isEmpty()) {
                                    if (!isEmpty(sysUserDepartments.get(0).getDepartmentId())) {
                                        Department department = departmentService.findById(sysUserDepartments.get(0).getDepartmentId());
                                        // 如果所属部门是班主任
                                        if (!isEmpty(department) && DataStatus.Y.getCode().equals(department.getStatus()) && DepartmentCatalog.BANZHUREN.getCode().equals(department.getCatalog())) {
                                            orderQueryV2.setSysCatalog(DepartmentCatalog.BANZHUREN.getCode());
                                            // 设置为已认领
                                            userModify.setStage(UserStage.Y.getCode());
                                        }
                                    }
                                }
                            }
                        }
                        // 检查是否已经分配过班主任
                        SysUserMasterQuery sysUserMasterQuery = new SysUserMasterQuery();
                        sysUserMasterQuery.setUserId(user.getId());
                        sysUserMasterQuery.setStatus(DataStatus.Y.getCode());
                        Integer count = sysUserMasterService.countByQuery(sysUserMasterQuery);
                        if (count > 0) {
                            userModify.setStage(UserStage.Y.getCode());
                        }
                        // 流转明细记录用户成单
                        UserLog userLog = new UserLog();
                        userLog.setUserId(user.getId());
                        userLog.setAuthUserId(orderFlowSplit.getChanceUserId());
                        userLog.setAction(UserLogAction.C1009.getCode());
                        userLog.setCreateTime(datetime);
                        userLog.setModifyTime(datetime);
                        userLog.setStatus(DataStatus.Y.getCode());
                        // 订单审核通过
                        this.orderService.paySuccess(orderQueryV2, userModify, userLog);
                        //发送报单信息
                        Department department = new Department();
                        if (!isEmpty(orderFlowSplit.getChanceUserId())) {
                            //查询业绩坐席是不是班主任
                            SysUser sysUser = sysUserService.findById(orderFlowSplit.getChanceUserId());
                            if (!isEmpty(sysUser) && DataStatus.Y.getCode().equals(sysUser.getStatus())) {
                                SysUserDepartmentQuery sysUserDepartmentQuery = new SysUserDepartmentQuery();
                                sysUserDepartmentQuery.setStatus(DataStatus.Y.getCode());
                                sysUserDepartmentQuery.setUserId(sysUser.getId());
                                List<SysUserDepartment> sysUserDepartments = sysUserDepartmentService.findAll(sysUserDepartmentQuery);
                                if (!isEmpty(sysUserDepartments) && !sysUserDepartments.isEmpty()) {
                                    if (!isEmpty(sysUserDepartments.get(0).getDepartmentId())) {
                                        department = departmentService.findById(sysUserDepartments.get(0).getDepartmentId());
                                        //保单机器人异步处理
                                        DingTalkRequest dingTalkRequest = new DingTalkRequest();
                                        dingTalkRequest.setDepartmentId(department.getId());
                                        dingTalkRequest.setAmount(orderFlow.getAmount());
                                        dingTalkRequest.setSysUserName(sysUser.getName());
                                        MQProducer.INSTANCE.sendOneway(LogSequence.get(), MQTopic.LXFK_API, MQTag.LXFK_API_DING_TALK_ROBOT_MESSAGE, this.getJSON(dingTalkRequest));
                                    }
                                }
                            }

                            //手动回库数据3小时内被他人认领且认领后48小时内成单发送提醒
                            UserLogQuery userLogQuery = new UserLogQuery();
                            userLogQuery.setUserId(userModify.getId());
                            List<String> actions = new ArrayList<>();
                            actions.add(UserLogAction.C1005.getCode());
                            actions.add(UserLogAction.C1006.getCode());
                            userLogQuery.setActions(actions);
                            //查询是否是48小时内认领
                            userLogQuery.setCreateTimeStart(DateUtil.getFutureHour(datetime, -48));
                            List<UserLog> userLogs = userLogService.findAll(userLogQuery);
                            if (!userLogs.isEmpty()){
                                //存在48小时内认领，查询是否在认领前3小时内手动回库
                                actions.clear();
                                actions.add(UserLogAction.C1008.getCode());
                                userLogQuery.setActions(actions);
                                userLogQuery.setCreateTimeStart(DateUtil.getFutureHour(userLogs.get(0).getCreateTime(), -3));
                                userLogQuery.setCreateTimeEnd(userLogs.get(0).getCreateTime());
                                List<UserLog> userLogList = userLogService.findAll(userLogQuery);
                                if (!userLogList.isEmpty()){
                                    //存在手动回库操作，发送提醒
                                    //发送钉钉通知
                                    BatchSendOTORequest batchSendOTORequest = new BatchSendOTORequest();
                                    batchSendOTORequest.setRobotCode(App.DING_LXFK_MESSAGE_ROBOT_CODE);
                                    batchSendOTORequest.setMsgKey(DingMessageKey.SAMPLE_TEXT.getCode());
                                    List<String> dingUserIds = new ArrayList<>();
                                    //提醒人员 王晓蔓 刘振茹 黄志坤
                                    List<Integer> sysUserIds = new ArrayList<>();
                                    sysUserIds.add(10000288);
                                    sysUserIds.add(10021997);
                                    sysUserIds.add(10000093);
                                    List<SysUser> sysUserList = sysUserService.findByIds(sysUserIds);
                                    for (SysUser dingSysUser : sysUserList) {
                                        if (!isEmpty(dingSysUser.getDingUserId())) {
                                            dingUserIds.add(dingSysUser.getDingUserId());
                                        }
                                    }
                                    batchSendOTORequest.setUserIds(dingUserIds);
                                    //设置通知内容
                                    DingMessageSampleTextParam sampleTextParam = new DingMessageSampleTextParam();
                                    String content = "您好，" + sysUser.getUsername() + "在众律智学成交一笔3小时内回库机会认领48小时内成交订单，该线索编号：" +
                                            customer.getUsername() + "  开课手机号：" + customer.getUsername();
                                    sampleTextParam.setContent(content);
                                    batchSendOTORequest.setMsgParam(this.getJSON(sampleTextParam));
                                    //发送通知给指定处理人员
                                    MQProducer.INSTANCE.sendOneway(LogSequence.get(), MQTopic.LXFK_API, MQTag.LXFK_API_DING_MESSAGE_MESSAGE, this.getJSON(batchSendOTORequest));
                                }
                            }
                        }
                        // 发送短信通知
                        SMSPayload smsPayload = new SMSPayload();
                        smsPayload.setMobile(customer.getUsername());
                        smsPayload.setCreateTime(datetime);
                        String code = LogSequence.get();
                        smsPayload.setCode(code);
                        smsPayload.setType(SMSType.C1.getCode());
                        // 设置模板id
                        smsPayload.setTemplateId(com.common.constant.App.FEIGE_ORDER_PASS_TEMPLATE_ID);
                        List<String> datas = new ArrayList<>();
                        datas.add(this.getMobile(customer.getUsername()));
                        datas.add(order.getCode());
                        datas.add(orderFlow.getAmount().toString());
                        // 设置发送的内容(填充模板用)
                        smsPayload.setDatas(datas);
                        String content = String.format(com.common.constant.App.FEIGE_ORDER_PASS_SMS_CONTENT, this.getMobile(customer.getUsername()), order.getCode(), orderFlow.getAmount().toString());
                        if (DepartmentCatalog.BANZHUREN.getCode().equals(department.getCatalog())) {
                            content = String.format(com.common.constant.App.MASTER_FEIGE_ORDER_PASS_SMS_CONTENT, this.getMobile(customer.getUsername()), order.getCode(), orderFlow.getAmount().toString());
                            smsPayload.setTemplateId(com.common.constant.App.MASTER_FEIGE_ORDER_PASS_TEMPLATE_ID);
                        }
                        // 设置发送的内容(记录到数据库用)
                        smsPayload.setContent(content);
                        // 发送到mq
                        MQProducer.INSTANCE.sendOneway(code, MQTopic.LXFK_API, MQTag.LXFK_API_SMS, this.getJSON(smsPayload));
                        // 如果是全款 或者是尾款 需要重新登录 签署协议
//                    if (!OrderFlowCatalog.C1000.getCode().equals(orderFlow.getCatalog())) {
                        // 删除官网的token
                        this.redisTemplate.delete(CacheKey.USER_TOKEN + TokenType.C.name() + Device.PC.name() + String.valueOf(user.getCustomerId()));
                        // 删除安卓和IOS的token
                        this.redisTemplate.delete(CacheKey.USER_TOKEN + TokenType.C.name() + Device.MD.name() + String.valueOf(user.getCustomerId()));
//                    }
                    } else {
                        //更新订单
                        OrderQueryV2 orderQueryV2 = new OrderQueryV2();
                        orderQueryV2.setOrderFlowId(orderFlow.getId());
                        // 12 支付宝
                        if ("12".equals(payChannelType)) {
                            orderQueryV2.setPayChannel(PayChannel.IPAYNOW_ALIPAY.getCode());
                        }
                        // 13 微信
                        if ("13".equals(payChannelType)) {
                            orderQueryV2.setPayChannel(PayChannel.IPAYNOW_WXPAY.getCode());
                        }
                        if ("01".equals(deviceType)) {
                            // 12 支付宝
                            if ("12".equals(payChannelType)) {
                                orderQueryV2.setPayChannel(PayChannel.IPAYNOW_LX_APP_ALIPAY.getCode());
                            }
                            // 13 微信
                            if ("13".equals(payChannelType)) {
                                orderQueryV2.setPayChannel(PayChannel.IPAYNOW_LX_APP_WXPAY.getCode());
                            }
                        }
                        this.orderService.paySuccess(orderQueryV2, null, null);
                    }
                }

            } finally {
                if (lock != null) {
                    lock.unlock();
                }
            }
            return "success=Y";
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return "success=N";
    }


    /**
     * 安卓支付调用(最新版)
     * @param request
     * <AUTHOR>
     * @return
     */
    @RequestMapping(value = "/v3/ipaynow/order/android/create")
    public @ResponseBody Response<?> createAndroidV3(@RequestBody OrderQueryRequest request) {
        try{
            Date datetime = this.getServerTime();
            IPayNowResponse response = new IPayNowResponse();
            UserToken userToken = this.getUserToken();
            if (userToken == null) {
                return new Response<>(ERROR,FORBIDDEN);
            }
            // 判断传入参数订单编号是否为空
            if (this.isEmpty(request.getId())) {
                return new Response<>(ERROR,"请输入订单编号");
            }
            // 查询订单信息
            OrderFlow orderFlow = this.orderFlowService.findById(request.getId());
            if (orderFlow == null) {
                return new Response<>(ERROR, "未查询到订单信息");
            }
            if (OrderAudit.REJECT.getCode().equals(orderFlow.getAudit())) {
                return new Response<>(ERROR, "此订单已经被驳回，无法支付");
            }
            if (OrderStage.Y.getCode().equals(orderFlow.getStage())) {
                return new Response<>(ERROR, "此订单已经支付,无须再次进行支付");
            }
            if (OrderStage.REFUND.getCode().equals(orderFlow.getStage())) {
                return new Response<>(ERROR, "此订单已经退款,无法支付");
            }
            if (DataStatus.N.getCode().equals(orderFlow.getStatus())) {
                return new Response<>(ERROR, "该订单已经被删除，无法支付");
            }
            if (BigDecimal.ZERO.compareTo(orderFlow.getAmount()) > -1) {
                return new Response<>(ERROR, "该订单是0元订单,请去手动审核通过");
            }
            // 生成订单号
            // 订单编号
            String mhtOrderNo = this.getDateTime() + App.ID + CommonSequence.nextValue() + orderFlow.getId();
            // 记录相关信息
            OrderQrcode orderQrcodeCreate = new OrderQrcode();
            orderQrcodeCreate.setOrderId(orderFlow.getOrderId());
            orderQrcodeCreate.setOrderFlowId(orderFlow.getId());
            orderQrcodeCreate.setStatus(DataStatus.Y.getCode());
            orderQrcodeCreate.setPayCode(mhtOrderNo);
            orderQrcodeCreate.setCatalog(OrderQrcodeCatalog.C1.getCode());
            orderQrcodeCreate.setModifyTime(datetime);
            orderQrcodeCreate.setCreateTime(datetime);
            orderQrcodeService.create(orderQrcodeCreate);
            IPayNowServcieClient iPayNowServcieClient = new IPayNowServcieClient();
            //做MD5签名
            Map<String, String> dataMap = new HashMap<String, String>();
            dataMap.put("funcode", "WP001");
            dataMap.put("version", "1.0.3");
            dataMap.put("appId", IPayNowServcieClient.ANDROID_ZTC_APP_ID);
            dataMap.put("mhtOrderNo", mhtOrderNo);
            dataMap.put("mhtOrderName", com.common.constant.App.COMPANY_NAME);
            dataMap.put("mhtOrderType", "05");
            dataMap.put("mhtCurrencyType", "156");
            dataMap.put("mhtOrderAmt",  this.getPrice(orderFlow.getAmount().multiply(new BigDecimal(100))).toString());
            dataMap.put("mhtOrderDetail", "课程支付");
            dataMap.put("mhtOrderTimeOut", "3600");
            dataMap.put("mhtOrderStartTime", this.getDateTime());
            dataMap.put("notifyUrl", this.getServerName() + "/v3/ipaynow/cms/pay/notify/create");
            dataMap.put("mhtCharset", "UTF-8");
            dataMap.put("deviceType", "01");
            dataMap.put("payChannelType", request.getPayChannelType());//12 支付宝  13微信
            dataMap.put("mhtLimitPay", "1"); // 0禁用信用卡   1允许使用信用卡
            if ("13".equals(request.getPayChannelType())){
                dataMap.put("mhtSubAppId", IPayNowServcieClient.WECHAT_FKZTC_APP_ID); //微信开放平台 appId  支付渠道为微信时必填
                dataMap.put("consumerCreateIp",httpServletRequest.getRemoteAddr()); //用户端 IP，微信时必填
            }
            dataMap.put("mhtSignType", "MD5");
            //	dataMap.put("mhtReserved", mhtReserved);//商户保留域字段不必填，如果商户有需要对每笔交易记录一些自己的东西，可以放在这个里面
            String mhtSignature = iPayNowServcieClient.getFormDataParamMD5(dataMap, IPayNowServcieClient.ANDROID_ZTC_APP_KEY, StandardCharsets.UTF_8.name());
            Set<String> keySet = dataMap.keySet();
            StringBuilder builder = new StringBuilder();
            for (String key : keySet) {
                String value = dataMap.get(key);
                if(value != null && value.length() > 0){
                    builder.append(key + "=" + value + "&");
                }
            }
            builder.append("mhtSignature=" + mhtSignature);
            // 请求报文
            String needCheckMsg = builder.toString();
            // 请求报文
            response.setNeedCheckMsg(needCheckMsg);
            // 订单编号
            response.setMhtOrderNo(mhtOrderNo);
            return new Response<>(response);
        } catch (Exception e) {
            logger.error(e.getMessage(),e);
        }
        return new Response<>(ERROR,FAILURE);
    }


    /**
     * 安卓现在支付支付结果查询
     * @param request
     * @return
     */
    @RequestMapping(value = "/v3/ipaynow/order/android/query")
    public @ResponseBody Response<?> queryAndroidV3(@RequestBody OrderQueryRequest request) {
        try{
            UserToken userToken = this.getUserToken();
            if (userToken == null) {
                return new Response<>(ERROR,FORBIDDEN);
            }
            // 判断传入参数订单编号是否为空
            if (this.isEmpty(request.getMhtOrderNo())) {
                return new Response<>(ERROR,"请输入订单编号");
            }
            IPayNowServcieClient iPayNowServcieClient = new IPayNowServcieClient();
            //做MD5签名
            Map<String, String> dataMap = new HashMap<String, String>();
            dataMap.put("funcode","MQ002");
            dataMap.put("version", "1.0.3");
            dataMap.put("deviceType", "01");
            dataMap.put("appId", IPayNowServcieClient.ANDROID_ZTC_APP_ID);
            dataMap.put("mhtOrderNo", request.getMhtOrderNo());
            dataMap.put("mhtCharset", StandardCharsets.UTF_8.name());
            dataMap.put("mhtSignType", "MD5");
            String mhtSignature = iPayNowServcieClient.getFormDataParamMD5(dataMap, IPayNowServcieClient.ANDROID_ZTC_APP_KEY, StandardCharsets.UTF_8.name());
            dataMap.put("mhtSignature", mhtSignature);
            String response = iPayNowServcieClient.execute(iPayNowServcieClient.getMultiValueMap(dataMap));
            String[] tn = response.split("&");
            for (int i = 0; i < tn.length; i++) {
                if (tn[i].startsWith("tradeStatus=")) {
                    // 交易支付状态  A001订单支付成功  A002订单支付失败      A004处理中     A005订单受理失败  A006交易关闭
                    String tnValue = tn[i].substring(12);
                    return new Response<>(tnValue);
                }
            }
        } catch (Exception e) {
            logger.error(e.getMessage(),e);
        }
        return new Response<>(ERROR,FAILURE);
    }


    /**
     * 支付通知(安卓APP支付  点通账户专用)
     * <AUTHOR>
     */
    @RequestMapping(value = "/v3/ipaynow/cms/pay/notify/create", produces = MediaType.TEXT_PLAIN_VALUE)
    public @ResponseBody
    String createNotifyV3() {
        try {
            String orderNo = this.httpServletRequest.getParameter("mhtOrderNo").substring(24);
            // 支付方式
            String payChannelType = this.httpServletRequest.getParameter("payChannelType");
            // 设备类型
            String deviceType = this.httpServletRequest.getParameter("deviceType");
            logger.info("deviceType : {}",deviceType);
            // 查询该支付订单信息
            OrderFlow orderFlow = this.orderFlowService.findById(Integer.parseInt(orderNo));
            Order order = this.orderService.findById(orderFlow.getOrderId());
            OrderFlowSplitQuery orderFlowSplitQuery = new OrderFlowSplitQuery();
            orderFlowSplitQuery.setOrderFlowId(orderFlow.getId());
            List<OrderFlowSplit> orderFlowSplitList = orderFlowSplitService.findAll(orderFlowSplitQuery);
            if (!orderFlowSplitList.isEmpty() && order != null && OrderAudit.ING.getCode().equals(orderFlow.getAudit())) {
                //业务逻辑审核前同一流水下只会有一条拆分数据
                OrderFlowSplit orderFlowSplit = orderFlowSplitList.get(0);
                Date datetime = this.getServerTime();
                User user = this.userService.findById(order.getUserId());
                if (user != null) {
                    //查询用户手机号
                    Customer customer = this.customerService.findById(user.getCustomerId());
                    User userModify = new User();
                    userModify.setId(user.getId());
                    userModify.setCatalog(UserCatalog.C2.getCode());
                    userModify.setStage(UserStage.N.getCode());
                    userModify.setModifyTime(datetime);
                    // 更新订单
                    OrderQueryV2 orderQueryV2 = new OrderQueryV2();
                    // 记录用户当时成单状态
                    orderQueryV2.setUserCatalog(user.getCatalog());
                    orderQueryV2.setOrderFlowId(orderFlow.getId());
                    // 12 支付宝 36 支付宝花呗分期
                    if ("12".equals(payChannelType) || "36".equals(payChannelType)) {
                        orderQueryV2.setPayChannel(PayChannel.IPAYNOW_ALIPAY.getCode());
                    }
                    // 13 微信
                    if ("13".equals(payChannelType)) {
                        orderQueryV2.setPayChannel(PayChannel.IPAYNOW_WXPAY.getCode());
                    }
                    if ("01".equals(deviceType)) {
                        // 12 支付宝
                        if ("12".equals(payChannelType)) {
                            orderQueryV2.setPayChannel(PayChannel.IPAYNOW_DT_APP_ALIPAY.getCode());
                        }
                        // 13 微信
                        if ("13".equals(payChannelType)) {
                            orderQueryV2.setPayChannel(PayChannel.IPAYNOW_DT_APP_WXPAY.getCode());
                        }
                    }
                    if (!isEmpty(orderFlowSplit.getChanceUserId())) {
                        //查询业绩坐席是不是班主任
                        SysUser sysUser = sysUserService.findById(orderFlowSplit.getChanceUserId());
                        if (!isEmpty(sysUser) && DataStatus.Y.getCode().equals(sysUser.getStatus())) {
                            SysUserDepartmentQuery sysUserDepartmentQuery = new SysUserDepartmentQuery();
                            sysUserDepartmentQuery.setStatus(DataStatus.Y.getCode());
                            sysUserDepartmentQuery.setUserId(sysUser.getId());
                            List<SysUserDepartment> sysUserDepartments = sysUserDepartmentService.findAll(sysUserDepartmentQuery);
                            if (!isEmpty(sysUserDepartments) && !sysUserDepartments.isEmpty()) {
                                if (!isEmpty(sysUserDepartments.get(0).getDepartmentId())) {
                                    Department department = departmentService.findById(sysUserDepartments.get(0).getDepartmentId());
                                    // 如果所属部门是班主任
                                    if (!isEmpty(department) && DataStatus.Y.getCode().equals(department.getStatus()) && DepartmentCatalog.BANZHUREN.getCode().equals(department.getCatalog())) {
                                        orderQueryV2.setSysCatalog(DepartmentCatalog.BANZHUREN.getCode());
                                        // 设置为已认领
                                        userModify.setStage(UserStage.Y.getCode());
                                    }
                                }
                            }
                        }
                    }
                    // 检查是否已经分配过班主任
                    SysUserMasterQuery sysUserMasterQuery = new SysUserMasterQuery();
                    sysUserMasterQuery.setUserId(user.getId());
                    sysUserMasterQuery.setStatus(DataStatus.Y.getCode());
                    Integer count = sysUserMasterService.countByQuery(sysUserMasterQuery);
                    if (count > 0) {
                        userModify.setStage(UserStage.Y.getCode());
                    }
                    // 流转明细记录用户成单
                    UserLog userLog = new UserLog();
                    userLog.setUserId(user.getId());
                    userLog.setAuthUserId(orderFlowSplit.getChanceUserId());
                    userLog.setAction(UserLogAction.C1009.getCode());
                    userLog.setCreateTime(datetime);
                    userLog.setModifyTime(datetime);
                    userLog.setStatus(DataStatus.Y.getCode());
                    // 订单审核通过
                    this.orderService.paySuccess(orderQueryV2, userModify, userLog);
                    //发送报单信息
                    Department department = new Department();
                    if (!isEmpty(orderFlowSplit.getChanceUserId())) {
                        //查询业绩坐席是不是班主任
                        SysUser sysUser = sysUserService.findById(orderFlowSplit.getChanceUserId());
                        if (!isEmpty(sysUser) && DataStatus.Y.getCode().equals(sysUser.getStatus())) {
                            SysUserDepartmentQuery sysUserDepartmentQuery = new SysUserDepartmentQuery();
                            sysUserDepartmentQuery.setStatus(DataStatus.Y.getCode());
                            sysUserDepartmentQuery.setUserId(sysUser.getId());
                            List<SysUserDepartment> sysUserDepartments = sysUserDepartmentService.findAll(sysUserDepartmentQuery);
                            if (!isEmpty(sysUserDepartments) && !sysUserDepartments.isEmpty()) {
                                if (!isEmpty(sysUserDepartments.get(0).getDepartmentId())) {
                                    department = departmentService.findById(sysUserDepartments.get(0).getDepartmentId());
                                    //保单机器人异步处理
                                    DingTalkRequest dingTalkRequest = new DingTalkRequest();
                                    dingTalkRequest.setDepartmentId(department.getId());
                                    dingTalkRequest.setAmount(orderFlow.getAmount());
                                    dingTalkRequest.setSysUserName(sysUser.getName());
                                    MQProducer.INSTANCE.sendOneway(LogSequence.get(), MQTopic.LXFK_API, MQTag.LXFK_API_DING_TALK_ROBOT_MESSAGE, this.getJSON(dingTalkRequest));
                                }
                            }
                        }

                        //手动回库数据3小时内被他人认领且认领后48小时内成单发送提醒
                        UserLogQuery userLogQuery = new UserLogQuery();
                        userLogQuery.setUserId(userModify.getId());
                        List<String> actions = new ArrayList<>();
                        actions.add(UserLogAction.C1005.getCode());
                        actions.add(UserLogAction.C1006.getCode());
                        userLogQuery.setActions(actions);
                        //查询是否是48小时内认领
                        userLogQuery.setCreateTimeStart(DateUtil.getFutureHour(datetime, -48));
                        List<UserLog> userLogs = userLogService.findAll(userLogQuery);
                        if (!userLogs.isEmpty()){
                            //存在48小时内认领，查询是否在认领前3小时内手动回库
                            actions.clear();
                            actions.add(UserLogAction.C1008.getCode());
                            userLogQuery.setActions(actions);
                            userLogQuery.setCreateTimeStart(DateUtil.getFutureHour(userLogs.get(0).getCreateTime(), -3));
                            userLogQuery.setCreateTimeEnd(userLogs.get(0).getCreateTime());
                            List<UserLog> userLogList = userLogService.findAll(userLogQuery);
                            if (!userLogList.isEmpty()){
                                //存在手动回库操作，发送提醒
                                //发送钉钉通知
                                BatchSendOTORequest batchSendOTORequest = new BatchSendOTORequest();
                                batchSendOTORequest.setRobotCode(App.DING_LXFK_MESSAGE_ROBOT_CODE);
                                batchSendOTORequest.setMsgKey(DingMessageKey.SAMPLE_TEXT.getCode());
                                List<String> dingUserIds = new ArrayList<>();
                                //提醒人员 王晓蔓 刘振茹 黄志坤
                                List<Integer> sysUserIds = new ArrayList<>();
                                sysUserIds.add(10000288);
                                sysUserIds.add(10021997);
                                sysUserIds.add(10000093);
                                List<SysUser> sysUserList = sysUserService.findByIds(sysUserIds);
                                for (SysUser dingSysUser : sysUserList) {
                                    if (!isEmpty(dingSysUser.getDingUserId())) {
                                        dingUserIds.add(dingSysUser.getDingUserId());
                                    }
                                }
                                batchSendOTORequest.setUserIds(dingUserIds);
                                //设置通知内容
                                DingMessageSampleTextParam sampleTextParam = new DingMessageSampleTextParam();
                                String content = "您好，" + sysUser.getUsername() + "在众律智学成交一笔3小时内回库机会认领48小时内成交订单，该线索编号：" +
                                        customer.getUsername() + "  开课手机号：" + customer.getUsername();
                                sampleTextParam.setContent(content);
                                batchSendOTORequest.setMsgParam(this.getJSON(sampleTextParam));
                                //发送通知给指定处理人员
                                MQProducer.INSTANCE.sendOneway(LogSequence.get(), MQTopic.LXFK_API, MQTag.LXFK_API_DING_MESSAGE_MESSAGE, this.getJSON(batchSendOTORequest));
                            }
                        }
                    }
                    // 发送短信通知
                    SMSPayload smsPayload = new SMSPayload();
                    smsPayload.setMobile(customer.getUsername());
                    smsPayload.setCreateTime(datetime);
                    String code = LogSequence.get();
                    smsPayload.setCode(code);
                    smsPayload.setType(SMSType.C1.getCode());
                    // 设置模板id
                    smsPayload.setTemplateId(com.common.constant.App.FEIGE_ORDER_PASS_TEMPLATE_ID);
                    List<String> datas = new ArrayList<>();
                    datas.add(this.getMobile(customer.getUsername()));
                    datas.add(order.getCode());
                    datas.add(orderFlow.getAmount().toString());
                    // 设置发送的内容(填充模板用)
                    smsPayload.setDatas(datas);
                    String content = String.format(com.common.constant.App.FEIGE_ORDER_PASS_SMS_CONTENT, this.getMobile(customer.getUsername()), order.getCode(), orderFlow.getAmount().toString());
                    if (DepartmentCatalog.BANZHUREN.getCode().equals(department.getCatalog())) {
                        content = String.format(com.common.constant.App.MASTER_FEIGE_ORDER_PASS_SMS_CONTENT, this.getMobile(customer.getUsername()), order.getCode(), orderFlow.getAmount().toString());
                        smsPayload.setTemplateId(com.common.constant.App.MASTER_FEIGE_ORDER_PASS_TEMPLATE_ID);
                    }
                    // 设置发送的内容(记录到数据库用)
                    smsPayload.setContent(content);
                    // 发送到mq
                    MQProducer.INSTANCE.sendOneway(code, MQTopic.LXFK_API, MQTag.LXFK_API_SMS, this.getJSON(smsPayload));
                    // 如果是全款 或者是尾款 需要重新登录 签署协议
//                    if (!OrderFlowCatalog.C1000.getCode().equals(orderFlow.getCatalog())) {
                    // 删除官网的token
                    this.redisTemplate.delete(CacheKey.USER_TOKEN + TokenType.C.name() + Device.PC.name() + String.valueOf(user.getCustomerId()));
                    // 删除安卓和IOS的token
                    this.redisTemplate.delete(CacheKey.USER_TOKEN + TokenType.C.name() + Device.MD.name() + String.valueOf(user.getCustomerId()));
//                    }
                } else {
                    //更新订单
                    OrderQueryV2 orderQueryV2 = new OrderQueryV2();
                    orderQueryV2.setOrderFlowId(orderFlow.getId());
                    // 12 支付宝
                    if ("12".equals(payChannelType)) {
                        orderQueryV2.setPayChannel(PayChannel.IPAYNOW_ALIPAY.getCode());
                    }
                    // 13 微信
                    if ("13".equals(payChannelType)) {
                        orderQueryV2.setPayChannel(PayChannel.IPAYNOW_WXPAY.getCode());
                    }
                    if ("01".equals(deviceType)) {
                        // 12 支付宝
                        if ("12".equals(payChannelType)) {
                            orderQueryV2.setPayChannel(PayChannel.IPAYNOW_DT_APP_ALIPAY.getCode());
                        }
                        // 13 微信
                        if ("13".equals(payChannelType)) {
                            orderQueryV2.setPayChannel(PayChannel.IPAYNOW_DT_APP_WXPAY.getCode());
                        }
                    }
                    this.orderService.paySuccess(orderQueryV2, null, null);
                }
            }
            return "success=Y";
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return "success=N";
    }

}
