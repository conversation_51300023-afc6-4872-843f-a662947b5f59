package com.api.validator;

import java.math.BigDecimal;
import java.util.List;


/**
 * 用户信息统计表数据验证
 *
 * @date 2025-08-12 11:17:25
 */
public class ReportUserInformationValidator extends Validator {

	public ReportUserInformationValidator onId(Integer id) {
		if (isEmpty(id)) {
			this.addAttribute(errors,"主键不能为空");
			this.result = false;
		}
		return this;
	}

	public ReportUserInformationValidator onIds(List<Integer> ids) {
		if (isEmpty(ids)) {
			this.addAttribute(errors,"主键集合不能为空");
			this.result = false;
		}
		return this;
	}

	public ReportUserInformationValidator onUserId(Integer userId) {
		if (isEmpty(userId)) {
			this.addAttribute(errors,"用户编号不能为空");
			this.result = false;
		}
		return this;
	}

	public ReportUserInformationValidator onUserIds(List<Integer> userIds) {
		if (isEmpty(userIds)) {
			this.addAttribute(errors,"用户编号集合不能为空");
			this.result = false;
		}
		return this;
	}

	public ReportUserInformationValidator onProductIds(String productIds) {
		if (isEmpty(productIds)) {
			this.addAttribute(errors,"产品编号不能为空");
			this.result = false;
		} else if (productIds.trim().length() > 1000L) {
			this.addAttribute(errors,"产品编号长度不能超过1000");
			this.result = false;
		}
		return this;
	}

	public ReportUserInformationValidator onProductIdss(List<String> productIdss) {
		if (isEmpty(productIdss)) {
			this.addAttribute(errors,"产品编号集合不能为空");
			this.result = false;
		}
		return this;
	}

	public ReportUserInformationValidator onSumAmount(BigDecimal sumAmount) {
		if (isEmpty(sumAmount)) {
			this.addAttribute(errors,"实付总金额不能为空");
			this.result = false;
		} else if (sumAmount.compareTo(new BigDecimal("99999999.99")) > 0) {
			this.addAttribute(errors,"实付总金额不能大于99999999.99");
			this.result = false;
		}
		return this;
	}

	public ReportUserInformationValidator onSumRefundAmount(BigDecimal sumRefundAmount) {
		if (isEmpty(sumRefundAmount)) {
			this.addAttribute(errors,"实际退费金额不能为空");
			this.result = false;
		} else if (sumRefundAmount.compareTo(new BigDecimal("99999999.99")) > 0) {
			this.addAttribute(errors,"实际退费金额不能大于99999999.99");
			this.result = false;
		}
		return this;
	}

	public ReportUserInformationValidator onSumRetrieveAmount(BigDecimal sumRetrieveAmount) {
		if (isEmpty(sumRetrieveAmount)) {
			this.addAttribute(errors,"挽单金额不能为空");
			this.result = false;
		} else if (sumRetrieveAmount.compareTo(new BigDecimal("99999999.99")) > 0) {
			this.addAttribute(errors,"挽单金额不能大于99999999.99");
			this.result = false;
		}
		return this;
	}

	public ReportUserInformationValidator onCallbackObjection(String callbackObjection) {
		if (isEmpty(callbackObjection)) {
			this.addAttribute(errors,"回访是否有异议(0:是 1:否 2:空 3:无需回访 4:未接)不能为空");
			this.result = false;
		} else if (callbackObjection.trim().length() > 1L) {
			this.addAttribute(errors,"回访是否有异议(0:是 1:否 2:空 3:无需回访 4:未接)长度不能超过1");
			this.result = false;
		}
		return this;
	}

	public ReportUserInformationValidator onComplaintCount(Integer complaintCount) {
		if (isEmpty(complaintCount)) {
			this.addAttribute(errors,"客诉数量不能为空");
			this.result = false;
		}
		return this;
	}

	public ReportUserInformationValidator onAfterSalesWorkOrderStage(String afterSalesWorkOrderStage) {
		if (isEmpty(afterSalesWorkOrderStage)) {
			this.addAttribute(errors,"售后工单状态不能为空");
			this.result = false;
		} else if (afterSalesWorkOrderStage.trim().length() > 4L) {
			this.addAttribute(errors,"售后工单状态长度不能超过4");
			this.result = false;
		}
		return this;
	}

	public ReportUserInformationValidator onComplaintContent(String complaintContent) {
		if (isEmpty(complaintContent)) {
			this.addAttribute(errors,"学员投诉内容不能为空");
			this.result = false;
		} else if (complaintContent.trim().length() > 1000L) {
			this.addAttribute(errors,"学员投诉内容长度不能超过1000");
			this.result = false;
		}
		return this;
	}

	public ReportUserInformationValidator onStatus(String status) {
		if (isEmpty(status)) {
			this.addAttribute(errors,"状态(0:正常 1:无效)不能为空");
			this.result = false;
		} else if (status.trim().length() > 1L) {
			this.addAttribute(errors,"状态(0:正常 1:无效)长度不能超过1");
			this.result = false;
		}
		return this;
	}

	public ReportUserInformationValidator onModifyTime(String modifyTime) {
		if (isEmpty(modifyTime)) {
			this.addAttribute(errors,"修改时间不能为空");
			this.result = false;
		}
		return this;
	}

	public ReportUserInformationValidator onMinModifyTime(String minModifyTime) {
		if (isEmpty(minModifyTime)) {
			this.addAttribute(errors,"最小修改时间不能为空");
			this.result = false;
		}
		return this;
	}

	public ReportUserInformationValidator onMaxModifyTime(String maxModifyTime) {
		if (isEmpty(maxModifyTime)) {
			this.addAttribute(errors,"最大修改时间不能为空");
			this.result = false;
		}
		return this;
	}

	public ReportUserInformationValidator onCreateTime(String createTime) {
		if (isEmpty(createTime)) {
			this.addAttribute(errors,"创建时间不能为空");
			this.result = false;
		}
		return this;
	}

	public ReportUserInformationValidator onMinCreateTime(String minCreateTime) {
		if (isEmpty(minCreateTime)) {
			this.addAttribute(errors,"最小创建时间不能为空");
			this.result = false;
		}
		return this;
	}

	public ReportUserInformationValidator onMaxCreateTime(String maxCreateTime) {
		if (isEmpty(maxCreateTime)) {
			this.addAttribute(errors,"最大创建时间不能为空");
			this.result = false;
		}
		return this;
	}
}
