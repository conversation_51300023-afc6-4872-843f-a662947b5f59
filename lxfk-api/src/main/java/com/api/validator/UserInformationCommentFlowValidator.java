package com.api.validator;

import java.util.List;


/**
 * 用户信息备注明细表数据验证
 *
 * @date 2025-08-11 17:24:47
 */
public class UserInformationCommentFlowValidator extends Validator {

	public UserInformationCommentFlowValidator onId(Integer id) {
		if (isEmpty(id)) {
			this.addAttribute(errors,"主键不能为空");
			this.result = false;
		}
		return this;
	}

	public UserInformationCommentFlowValidator onIds(List<Integer> ids) {
		if (isEmpty(ids)) {
			this.addAttribute(errors,"主键集合不能为空");
			this.result = false;
		}
		return this;
	}

	public UserInformationCommentFlowValidator onUserId(Integer userId) {
		if (isEmpty(userId)) {
			this.addAttribute(errors,"用户编号不能为空");
			this.result = false;
		}
		return this;
	}

	public UserInformationCommentFlowValidator onUserIds(List<Integer> userIds) {
		if (isEmpty(userIds)) {
			this.addAttribute(errors,"用户编号集合不能为空");
			this.result = false;
		}
		return this;
	}

	public UserInformationCommentFlowValidator onComment(String comment) {
		if (isEmpty(comment)) {
			this.addAttribute(errors,"备注不能为空");
			this.result = false;
		} else if (comment.trim().length() > 300L) {
			this.addAttribute(errors,"备注长度不能超过300");
			this.result = false;
		}
		return this;
	}

	public UserInformationCommentFlowValidator onImgUrl(String imgUrl) {
		if (isEmpty(imgUrl)) {
			this.addAttribute(errors,"图片地址不能为空");
			this.result = false;
		} else if (imgUrl.trim().length() > 500L) {
			this.addAttribute(errors,"图片地址长度不能超过500");
			this.result = false;
		}
		return this;
	}

	public UserInformationCommentFlowValidator onSysUserId(Integer sysUserId) {
		if (isEmpty(sysUserId)) {
			this.addAttribute(errors,"操作人编号不能为空");
			this.result = false;
		}
		return this;
	}

	public UserInformationCommentFlowValidator onSysUserIds(List<Integer> sysUserIds) {
		if (isEmpty(sysUserIds)) {
			this.addAttribute(errors,"操作人编号集合不能为空");
			this.result = false;
		}
		return this;
	}

	public UserInformationCommentFlowValidator onCatalog(String catalog) {
		if (isEmpty(catalog)) {
			this.addAttribute(errors,"数据类型(1000:舆情记录 1001:推班记录 1002:群服务情况记录 1003:备注)不能为空");
			this.result = false;
		} else if (catalog.trim().length() > 4L) {
			this.addAttribute(errors,"数据类型(1000:舆情记录 1001:推班记录 1002:群服务情况记录 1003:备注)长度不能超过4");
			this.result = false;
		}
		return this;
	}

	public UserInformationCommentFlowValidator onStatus(String status) {
		if (isEmpty(status)) {
			this.addAttribute(errors,"状态(0:正常 1:无效)不能为空");
			this.result = false;
		} else if (status.trim().length() > 1L) {
			this.addAttribute(errors,"状态(0:正常 1:无效)长度不能超过1");
			this.result = false;
		}
		return this;
	}

	public UserInformationCommentFlowValidator onModifyTime(String modifyTime) {
		if (isEmpty(modifyTime)) {
			this.addAttribute(errors,"修改时间不能为空");
			this.result = false;
		}
		return this;
	}

	public UserInformationCommentFlowValidator onMinModifyTime(String minModifyTime) {
		if (isEmpty(minModifyTime)) {
			this.addAttribute(errors,"最小修改时间不能为空");
			this.result = false;
		}
		return this;
	}

	public UserInformationCommentFlowValidator onMaxModifyTime(String maxModifyTime) {
		if (isEmpty(maxModifyTime)) {
			this.addAttribute(errors,"最大修改时间不能为空");
			this.result = false;
		}
		return this;
	}

	public UserInformationCommentFlowValidator onCreateTime(String createTime) {
		if (isEmpty(createTime)) {
			this.addAttribute(errors,"创建时间不能为空");
			this.result = false;
		}
		return this;
	}

	public UserInformationCommentFlowValidator onMinCreateTime(String minCreateTime) {
		if (isEmpty(minCreateTime)) {
			this.addAttribute(errors,"最小创建时间不能为空");
			this.result = false;
		}
		return this;
	}

	public UserInformationCommentFlowValidator onMaxCreateTime(String maxCreateTime) {
		if (isEmpty(maxCreateTime)) {
			this.addAttribute(errors,"最大创建时间不能为空");
			this.result = false;
		}
		return this;
	}
}
