<?xml version="1.0"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.local</groupId>
    <artifactId>lxfk</artifactId>
    <version>dev</version>
  </parent>
  <groupId>com.local.lxfk.api</groupId>
  <artifactId>lxfk-api</artifactId>
  <name>lxfk-api</name>
  <packaging>war</packaging>
  <url>http://maven.apache.org</url>
  <dependencies>
  	<dependency>
  		<groupId>com.local.lxfk.common</groupId>
  		<artifactId>lxfk-common</artifactId>
  		<version>${project.version}</version>
  	</dependency>
    <dependency>
      <groupId>com.local.lxfk.service</groupId>
      <artifactId>lxfk-service</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>com.local.lxfk.domain</groupId>
      <artifactId>lxfk-domain</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter</artifactId>
	</dependency>
  	<dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-web</artifactId>
    </dependency>
    <dependency>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-thymeleaf</artifactId>
	</dependency>
	  <dependency>
		  <groupId>org.springframework.boot</groupId>
		  <artifactId>spring-boot-starter-mail</artifactId>
	  </dependency>
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-aop</artifactId>
    </dependency>
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-data-redis</artifactId>
    </dependency>
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-integration</artifactId>
    </dependency>
    <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-redis</artifactId>
    </dependency>
    <dependency>
	    <groupId>org.apache.httpcomponents</groupId>
	    <artifactId>httpclient</artifactId>
	</dependency>
	<dependency>
	  <groupId>net.coobird</groupId>
	  <artifactId>thumbnailator</artifactId>
	</dependency>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <scope>test</scope>
    </dependency> 
    	<dependency>
     	<groupId>org.xhtmlrenderer</groupId>
      	<artifactId>flying-saucer-core</artifactId>
	</dependency>
	<dependency>
	    <groupId>org.xhtmlrenderer</groupId>
	    <artifactId>flying-saucer-pdf-itext5</artifactId>
	</dependency>
	<dependency>
		<groupId>net.pusuo</groupId>
		<artifactId>patchca</artifactId>
	</dependency>
	<dependency>
	    <groupId>org.apache.rocketmq</groupId>
	    <artifactId>rocketmq-spring-boot-starter</artifactId>
	</dependency>
	<dependency>
	    <groupId>com.aliyun</groupId>
	    <artifactId>aliyun-java-sdk-core</artifactId>
	</dependency>
	<dependency>
	    <groupId>com.aliyun</groupId>
	    <artifactId>aliyun-java-sdk-dypnsapi</artifactId>
	</dependency>
	  <!-- 字符串相似度计算工具 -->
	  <dependency>
		  <groupId>org.apache.commons</groupId>
		  <artifactId>commons-text</artifactId>
	  </dependency>


  </dependencies>
  <build>
  	<plugins>
  		<plugin>
		    <groupId>org.springframework.boot</groupId>
		    <artifactId>spring-boot-maven-plugin</artifactId>
		    <configuration>
		      <fork>true</fork>
		      <skip>false</skip>
		    </configuration>
		    <executions>
		      <execution>
		        <goals>
		          <goal>repackage</goal>
		        </goals>
		      </execution>
		    </executions>
		  </plugin>

		  <plugin>
			<groupId>org.codehaus.mojo</groupId>
			<artifactId>exec-maven-plugin</artifactId>
			<executions>
				<execution>
					<id>tomcat-8092-stop</id>
					<phase>package</phase>
					<goals>
						<goal>exec</goal>
					</goals>
					<configuration>
						<executable>sh</executable>
						<workingDirectory>/home/<USER>/tomcat/pro/tomcat-8092/bin</workingDirectory>
						<arguments>
							<argument>./daemon.sh</argument>
							<argument>stop</argument>
						</arguments>
					</configuration>
				</execution>
				<execution>
					<id>tomcat-8092-start</id>
					<phase>package</phase>
					<goals>
						<goal>exec</goal>
					</goals>
					<configuration>
						<executable>sh</executable>
						<async>true</async>
						<workingDirectory>/home/<USER>/tomcat/pro/tomcat-8092/bin</workingDirectory>
						<arguments>
							<argument>./daemon.sh</argument>
							<argument>start</argument>
						</arguments>
					</configuration>
				</execution>
			</executions>
		</plugin>
  	</plugins>
  </build>
</project>
