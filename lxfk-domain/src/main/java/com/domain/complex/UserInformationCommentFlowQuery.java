package com.domain.complex;

import java.util.Date;
import java.util.List;

/**
 * 用户信息备注明细表查询对象
 *
 * @date 2025-08-11 17:24:47
 */
public class UserInformationCommentFlowQuery extends Page {
	private static final long serialVersionUID = 1L;

	/**
	 * 数据编号集合
	 */
	private List<Integer> ids;
	/**
	 * 用户编号
	 */
	private Integer userId;
	/**
	 * 用户编号集合
	 */
	private List<Integer> userIds;
	/**
	 * 备注
	 */
	private String comment;
	/**
	 * 图片地址
	 */
	private String imgUrl;
	/**
	 * 操作人编号
	 */
	private Integer sysUserId;
	/**
	 * 操作人编号集合
	 */
	private List<Integer> sysUserIds;
	/**
	 * 数据类型(1000:舆情记录 1001:推班记录 1002:群服务情况记录 1003:备注)
	 */
	private String catalog;
	/**
	 * 最小修改时间
	 */
	private Date minModifyTime;
	/**
	 * 最大修改时间
	 */
	private Date maxModifyTime;
	/**
	 * 最小创建时间
	 */
	private Date minCreateTime;
	/**
	 * 最大创建时间
	 */
	private Date maxCreateTime;

	public List<Integer> getIds() {
		return ids;
	}

	public void setIds(List<Integer> ids) {
		this.ids = ids;
	}

	public Integer getUserId (){
		return userId;
	}

	public void setUserId (Integer userId) {
		this.userId = userId;
	}

	public List<Integer> getUserIds (){
		return userIds;
	}

	public void setUserIds (List<Integer> userIds) {
		this.userIds = userIds;
	}

	public String getComment (){
		return comment;
	}

	public void setComment (String comment) {
		this.comment = comment;
	}

	public String getImgUrl (){
		return imgUrl;
	}

	public void setImgUrl (String imgUrl) {
		this.imgUrl = imgUrl;
	}

	public Integer getSysUserId (){
		return sysUserId;
	}

	public void setSysUserId (Integer sysUserId) {
		this.sysUserId = sysUserId;
	}

	public List<Integer> getSysUserIds (){
		return sysUserIds;
	}

	public void setSysUserIds (List<Integer> sysUserIds) {
		this.sysUserIds = sysUserIds;
	}

	public String getCatalog (){
		return catalog;
	}

	public void setCatalog (String catalog) {
		this.catalog = catalog;
	}

	public Date getMinModifyTime() {
		return minModifyTime;
	}

	public void setMinModifyTime(Date minModifyTime) {
		this.minModifyTime = minModifyTime;
	}

	public Date getMaxModifyTime() {
		return maxModifyTime;
	}

	public void setMaxModifyTime(Date maxModifyTime) {
		this.maxModifyTime = maxModifyTime;
	}

	public Date getMinCreateTime() {
		return minCreateTime;
	}

	public void setMinCreateTime(Date minCreateTime) {
		this.minCreateTime = minCreateTime;
	}

	public Date getMaxCreateTime() {
		return maxCreateTime;
	}

	public void setMaxCreateTime(Date maxCreateTime) {
		this.maxCreateTime = maxCreateTime;
	}
}
