package com.domain.complex;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 用户信息统计表查询对象
 *
 * @date 2025-08-12 11:17:25
 */
public class ReportUserInformationQuery extends Page {
	private static final long serialVersionUID = 1L;

	/**
	 * 数据编号集合
	 */
	private List<Integer> ids;
	/**
	 * 用户编号
	 */
	private Integer userId;
	/**
	 * 用户编号集合
	 */
	private List<Integer> userIds;
	/**
	 * 产品编号
	 */
	private String productIds;
	/**
	 * 实付总金额
	 */
	private BigDecimal sumAmount;
	/**
	 * 实际退费金额
	 */
	private BigDecimal sumRefundAmount;
	/**
	 * 挽单金额
	 */
	private BigDecimal sumRetrieveAmount;
	/**
	 * 回访是否有异议(0:是 1:否 2:空 3:无需回访 4:未接)
	 */
	private String callbackObjection;
	/**
	 * 客诉数量
	 */
	private Integer complaintCount;
	/**
	 * 售后工单状态
	 */
	private String afterSalesWorkOrderStage;
	/**
	 * 学员投诉内容
	 */
	private String complaintContent;
	/**
	 * 最小修改时间
	 */
	private Date minModifyTime;
	/**
	 * 最大修改时间
	 */
	private Date maxModifyTime;
	/**
	 * 最小创建时间
	 */
	private Date minCreateTime;
	/**
	 * 最大创建时间
	 */
	private Date maxCreateTime;

	public List<Integer> getIds() {
		return ids;
	}

	public void setIds(List<Integer> ids) {
		this.ids = ids;
	}

	public Integer getUserId (){
		return userId;
	}

	public void setUserId (Integer userId) {
		this.userId = userId;
	}

	public List<Integer> getUserIds (){
		return userIds;
	}

	public void setUserIds (List<Integer> userIds) {
		this.userIds = userIds;
	}

	public String getProductIds (){
		return productIds;
	}

	public void setProductIds (String productIds) {
		this.productIds = productIds;
	}

	public BigDecimal getSumAmount (){
		return sumAmount;
	}

	public void setSumAmount (BigDecimal sumAmount) {
		this.sumAmount = sumAmount;
	}

	public BigDecimal getSumRefundAmount (){
		return sumRefundAmount;
	}

	public void setSumRefundAmount (BigDecimal sumRefundAmount) {
		this.sumRefundAmount = sumRefundAmount;
	}

	public BigDecimal getSumRetrieveAmount (){
		return sumRetrieveAmount;
	}

	public void setSumRetrieveAmount (BigDecimal sumRetrieveAmount) {
		this.sumRetrieveAmount = sumRetrieveAmount;
	}

	public String getCallbackObjection (){
		return callbackObjection;
	}

	public void setCallbackObjection (String callbackObjection) {
		this.callbackObjection = callbackObjection;
	}

	public Integer getComplaintCount (){
		return complaintCount;
	}

	public void setComplaintCount (Integer complaintCount) {
		this.complaintCount = complaintCount;
	}

	public String getAfterSalesWorkOrderStage (){
		return afterSalesWorkOrderStage;
	}

	public void setAfterSalesWorkOrderStage (String afterSalesWorkOrderStage) {
		this.afterSalesWorkOrderStage = afterSalesWorkOrderStage;
	}

	public String getComplaintContent (){
		return complaintContent;
	}

	public void setComplaintContent (String complaintContent) {
		this.complaintContent = complaintContent;
	}

	public Date getMinModifyTime() {
		return minModifyTime;
	}

	public void setMinModifyTime(Date minModifyTime) {
		this.minModifyTime = minModifyTime;
	}

	public Date getMaxModifyTime() {
		return maxModifyTime;
	}

	public void setMaxModifyTime(Date maxModifyTime) {
		this.maxModifyTime = maxModifyTime;
	}

	public Date getMinCreateTime() {
		return minCreateTime;
	}

	public void setMinCreateTime(Date minCreateTime) {
		this.minCreateTime = minCreateTime;
	}

	public Date getMaxCreateTime() {
		return maxCreateTime;
	}

	public void setMaxCreateTime(Date maxCreateTime) {
		this.maxCreateTime = maxCreateTime;
	}
}
