package com.domain;

import java.util.Date;

/**
 * 用户信息备注明细表实体
 *
 * @date 2025-08-11 17:24:47
 */
public class UserInformationCommentFlow extends Base {
	private static final long serialVersionUID = 1L;

	/**
	 * 用户编号
	 */
	private Integer userId;
	/**
	 * 备注
	 */
	private String comment;
	/**
	 * 图片地址
	 */
	private String imgUrl;
	/**
	 * 操作人编号
	 */
	private Integer sysUserId;
	/**
	 * 数据类型(1000:舆情记录 1001:推班记录 1002:群服务情况记录 1003:备注)
	 */
	private String catalog;

	public Integer getUserId (){
		return userId;
	}

	public void setUserId (Integer userId) {
		this.userId = userId;
	}

	public String getComment (){
		return comment;
	}

	public void setComment (String comment) {
		this.comment = comment;
	}

	public String getImgUrl (){
		return imgUrl;
	}

	public void setImgUrl (String imgUrl) {
		this.imgUrl = imgUrl;
	}

	public Integer getSysUserId (){
		return sysUserId;
	}

	public void setSysUserId (Integer sysUserId) {
		this.sysUserId = sysUserId;
	}

	public String getCatalog (){
		return catalog;
	}

	public void setCatalog (String catalog) {
		this.catalog = catalog;
	}

}
