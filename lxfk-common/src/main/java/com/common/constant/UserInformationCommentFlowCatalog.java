package com.common.constant;

public enum UserInformationCommentFlowCatalog {
	C1000("1000", "舆情记录"),
	C1001("1001", "推班记录"),
	C1002("1002", "群服务情况记录"),
	C1003("1003", "备注")
	;

	private String code;
	private String name;

	private UserInformationCommentFlowCatalog(String code, String name) {
		this.code = code;
		this.name = name;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public static String getName(String code) {
		for (UserInformationCommentFlowCatalog gender : UserInformationCommentFlowCatalog.values()) {
			if (gender.getCode().equals(code)) {
				return gender.name;
			}
		}
		return null;
	}

	public static UserInformationCommentFlowCatalog get(String code) {
		for (UserInformationCommentFlowCatalog value : UserInformationCommentFlowCatalog.values()) {
			if (value.getCode().equals(code)) {
				return value;
			}
		}
		return null;
	}
}
