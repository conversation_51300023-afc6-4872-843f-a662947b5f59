<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dao.ReportUserInformationDao">

    <resultMap id="reportUserInformationMap" type="com.domain.ReportUserInformation">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="product_ids" property="productIds"/>
        <result column="sum_amount" property="sumAmount"/>
        <result column="sum_refund_amount" property="sumRefundAmount"/>
        <result column="sum_retrieve_amount" property="sumRetrieveAmount"/>
        <result column="callback_objection" property="callbackObjection"/>
        <result column="complaint_count" property="complaintCount"/>
        <result column="after_sales_work_order_stage" property="afterSalesWorkOrderStage"/>
        <result column="complaint_content" property="complaintContent"/>
        <result column="status" property="status"/>
        <result column="modify_time" property="modifyTime"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <sql id="common_where_if">
        <trim prefix="where" prefixOverrides="and">
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="ids != null and ids.size > 0">
                and id in
                <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="userId != null">
                and user_id = #{userId}
            </if>
            <if test="userIds != null and userIds.size > 0">
                and user_id in
                <foreach collection="userIds" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="productIds != null and productIds != ''">
                and product_ids = #{productIds}
            </if>
            <if test="sumAmount != null">
                and sum_amount = #{sumAmount}
            </if>
            <if test="sumRefundAmount != null">
                and sum_refund_amount = #{sumRefundAmount}
            </if>
            <if test="sumRetrieveAmount != null">
                and sum_retrieve_amount = #{sumRetrieveAmount}
            </if>
            <if test="callbackObjection != null and callbackObjection != ''">
                and callback_objection = #{callbackObjection}
            </if>
            <if test="complaintCount != null">
                and complaint_count = #{complaintCount}
            </if>
            <if test="afterSalesWorkOrderStage != null and afterSalesWorkOrderStage != ''">
                and after_sales_work_order_stage = #{afterSalesWorkOrderStage}
            </if>
            <if test="complaintContent != null and complaintContent != ''">
                and complaint_content = #{complaintContent}
            </if>
            <if test="status != null and status != ''">
                and status = #{status}
            </if>
            <if test="modifyTime != null">
                and modify_time = #{modifyTime}
            </if>
            <if test="minModifyTime != null">
                and modify_time <![CDATA[ >= ]]> #{minModifyTime}
            </if>
            <if test="maxModifyTime != null">
                and modify_time <![CDATA[ < ]]> #{maxModifyTime}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="minCreateTime != null">
                and create_time <![CDATA[ >= ]]> #{minCreateTime}
            </if>
            <if test="maxCreateTime != null">
                and create_time <![CDATA[ < ]]> #{maxCreateTime}
            </if>
        </trim>
    </sql>

    <select id="select" resultMap="reportUserInformationMap" parameterType="com.domain.complex.ReportUserInformationQuery">
        select * from tb_report_user_information
        <include refid="common_where_if"/>
        order by id desc limit #{start},#{limit}
    </select>

    <select id="selectAll" resultMap="reportUserInformationMap" parameterType="com.domain.complex.ReportUserInformationQuery">
        select * from tb_report_user_information
        <include refid="common_where_if"/>
        order by id desc
    </select>

    <update id="updateById" parameterType="com.domain.ReportUserInformation">
        update tb_report_user_information
        <trim prefix="set" suffixOverrides=",">
            <if test="userId != null">
               user_id = #{userId},
            </if>
            <if test="productIds != null">
               product_ids = #{productIds},
            </if>
            <if test="sumAmount != null">
               sum_amount = #{sumAmount},
            </if>
            <if test="sumRefundAmount != null">
               sum_refund_amount = #{sumRefundAmount},
            </if>
            <if test="sumRetrieveAmount != null">
               sum_retrieve_amount = #{sumRetrieveAmount},
            </if>
            <if test="callbackObjection != null">
               callback_objection = #{callbackObjection},
            </if>
            <if test="complaintCount != null">
               complaint_count = #{complaintCount},
            </if>
            <if test="afterSalesWorkOrderStage != null">
               after_sales_work_order_stage = #{afterSalesWorkOrderStage},
            </if>
            <if test="complaintContent != null">
               complaint_content = #{complaintContent},
            </if>
            <if test="status != null">
               status = #{status},
            </if>
            <if test="modifyTime != null">
               modify_time = #{modifyTime},
            </if>
            <if test="createTime != null">
               create_time = #{createTime},
            </if>
        </trim>
        where id = #{id}
    </update>

    <insert id="insert" parameterType="com.domain.ReportUserInformation" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        insert into tb_report_user_information
        <trim suffixOverrides="," prefix="(" suffix=") values">
            <if test="id != null">
                id,
            </if>
            <if test="userId != null">
                user_id,
            </if>
            <if test="productIds != null">
                product_ids,
            </if>
            <if test="sumAmount != null">
                sum_amount,
            </if>
            <if test="sumRefundAmount != null">
                sum_refund_amount,
            </if>
            <if test="sumRetrieveAmount != null">
                sum_retrieve_amount,
            </if>
            <if test="callbackObjection != null">
                callback_objection,
            </if>
            <if test="complaintCount != null">
                complaint_count,
            </if>
            <if test="afterSalesWorkOrderStage != null">
                after_sales_work_order_stage,
            </if>
            <if test="complaintContent != null">
                complaint_content,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="modifyTime != null">
                modify_time,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
        </trim>
        <trim suffixOverrides="," prefix="(" suffix=")">
            <if test="id != null">
                #{id},
            </if>
            <if test="userId != null">
                #{userId},
            </if>
            <if test="productIds != null">
                #{productIds},
            </if>
            <if test="sumAmount != null">
                #{sumAmount},
            </if>
            <if test="sumRefundAmount != null">
                #{sumRefundAmount},
            </if>
            <if test="sumRetrieveAmount != null">
                #{sumRetrieveAmount},
            </if>
            <if test="callbackObjection != null">
                #{callbackObjection},
            </if>
            <if test="complaintCount != null">
                #{complaintCount},
            </if>
            <if test="afterSalesWorkOrderStage != null">
                #{afterSalesWorkOrderStage},
            </if>
            <if test="complaintContent != null">
                #{complaintContent},
            </if>
            <if test="status != null">
                #{status},
            </if>
            <if test="modifyTime != null">
                #{modifyTime},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
        </trim>
    </insert>

    <select id="count" resultType="java.lang.Integer" parameterType="com.domain.complex.ReportUserInformationQuery">
        select count(*) from tb_report_user_information
        <include refid="common_where_if"/>
    </select>

    <select id="selectById" resultMap="reportUserInformationMap">
        select * from tb_report_user_information where id = #{id}
    </select>

    <select id="selectByIds" resultMap="reportUserInformationMap">
        select * from tb_report_user_information where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <insert id="insertBatch" parameterType="java.util.List" useGeneratedKeys="false">
        insert into tb_report_user_information
        (
        user_id,
        product_ids,
        sum_amount,
        sum_refund_amount,
        sum_retrieve_amount,
        callback_objection,
        complaint_count,
        after_sales_work_order_stage,
        complaint_content,
        status,
        modify_time,
        create_time
        )
        values
        <foreach collection="list" item="item" index="index" separator=",">
        (
        #{item.userId},
        #{item.productIds},
        #{item.sumAmount},
        #{item.sumRefundAmount},
        #{item.sumRetrieveAmount},
        #{item.callbackObjection},
        #{item.complaintCount},
        #{item.afterSalesWorkOrderStage},
        #{item.complaintContent},
        #{item.status},
        #{item.modifyTime},
        #{item.createTime}
        )
        </foreach>
    </insert>
</mapper>