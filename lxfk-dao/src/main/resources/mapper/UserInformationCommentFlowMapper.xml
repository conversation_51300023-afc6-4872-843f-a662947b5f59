<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dao.UserInformationCommentFlowDao">

    <resultMap id="userInformationCommentFlowMap" type="com.domain.UserInformationCommentFlow">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="comment" property="comment"/>
        <result column="img_url" property="imgUrl"/>
        <result column="sys_user_id" property="sysUserId"/>
        <result column="catalog" property="catalog"/>
        <result column="status" property="status"/>
        <result column="modify_time" property="modifyTime"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <sql id="common_where_if">
        <trim prefix="where" prefixOverrides="and">
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="ids != null and ids.size > 0">
                and id in
                <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="userId != null">
                and user_id = #{userId}
            </if>
            <if test="userIds != null and userIds.size > 0">
                and user_id in
                <foreach collection="userIds" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="comment != null and comment != ''">
                and comment = #{comment}
            </if>
            <if test="imgUrl != null and imgUrl != ''">
                and img_url = #{imgUrl}
            </if>
            <if test="sysUserId != null">
                and sys_user_id = #{sysUserId}
            </if>
            <if test="sysUserIds != null and sysUserIds.size > 0">
                and sys_user_id in
                <foreach collection="sysUserIds" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="catalog != null and catalog != ''">
                and catalog = #{catalog}
            </if>
            <if test="status != null and status != ''">
                and status = #{status}
            </if>
            <if test="modifyTime != null">
                and modify_time = #{modifyTime}
            </if>
            <if test="minModifyTime != null">
                and modify_time <![CDATA[ >= ]]> #{minModifyTime}
            </if>
            <if test="maxModifyTime != null">
                and modify_time <![CDATA[ < ]]> #{maxModifyTime}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="minCreateTime != null">
                and create_time <![CDATA[ >= ]]> #{minCreateTime}
            </if>
            <if test="maxCreateTime != null">
                and create_time <![CDATA[ < ]]> #{maxCreateTime}
            </if>
        </trim>
    </sql>

    <select id="select" resultMap="userInformationCommentFlowMap" parameterType="com.domain.complex.UserInformationCommentFlowQuery">
        select * from tb_user_information_comment_flow
        <include refid="common_where_if"/>
        order by id desc limit #{start},#{limit}
    </select>

    <select id="selectAll" resultMap="userInformationCommentFlowMap" parameterType="com.domain.complex.UserInformationCommentFlowQuery">
        select * from tb_user_information_comment_flow
        <include refid="common_where_if"/>
        order by id desc
    </select>

    <update id="updateById" parameterType="com.domain.UserInformationCommentFlow">
        update tb_user_information_comment_flow
        <trim prefix="set" suffixOverrides=",">
            <if test="userId != null">
               user_id = #{userId},
            </if>
            <if test="comment != null">
               comment = #{comment},
            </if>
            <if test="imgUrl != null">
               img_url = #{imgUrl},
            </if>
            <if test="sysUserId != null">
               sys_user_id = #{sysUserId},
            </if>
            <if test="catalog != null">
               catalog = #{catalog},
            </if>
            <if test="status != null">
               status = #{status},
            </if>
            <if test="modifyTime != null">
               modify_time = #{modifyTime},
            </if>
            <if test="createTime != null">
               create_time = #{createTime},
            </if>
        </trim>
        where id = #{id}
    </update>

    <insert id="insert" parameterType="com.domain.UserInformationCommentFlow" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        insert into tb_user_information_comment_flow
        <trim suffixOverrides="," prefix="(" suffix=") values">
            <if test="id != null">
                id,
            </if>
            <if test="userId != null">
                user_id,
            </if>
            <if test="comment != null">
                comment,
            </if>
            <if test="imgUrl != null">
                img_url,
            </if>
            <if test="sysUserId != null">
                sys_user_id,
            </if>
            <if test="catalog != null">
                catalog,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="modifyTime != null">
                modify_time,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
        </trim>
        <trim suffixOverrides="," prefix="(" suffix=")">
            <if test="id != null">
                #{id},
            </if>
            <if test="userId != null">
                #{userId},
            </if>
            <if test="comment != null">
                #{comment},
            </if>
            <if test="imgUrl != null">
                #{imgUrl},
            </if>
            <if test="sysUserId != null">
                #{sysUserId},
            </if>
            <if test="catalog != null">
                #{catalog},
            </if>
            <if test="status != null">
                #{status},
            </if>
            <if test="modifyTime != null">
                #{modifyTime},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
        </trim>
    </insert>

    <select id="count" resultType="java.lang.Integer" parameterType="com.domain.complex.UserInformationCommentFlowQuery">
        select count(*) from tb_user_information_comment_flow
        <include refid="common_where_if"/>
    </select>

    <select id="selectById" resultMap="userInformationCommentFlowMap">
        select * from tb_user_information_comment_flow where id = #{id}
    </select>

    <select id="selectByIds" resultMap="userInformationCommentFlowMap">
        select * from tb_user_information_comment_flow where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <insert id="insertBatch" parameterType="java.util.List" useGeneratedKeys="false">
        insert into tb_user_information_comment_flow
        (
        user_id,
        comment,
        img_url,
        sys_user_id,
        catalog,
        status,
        modify_time,
        create_time
        )
        values
        <foreach collection="list" item="item" index="index" separator=",">
        (
        #{item.userId},
        #{item.comment},
        #{item.imgUrl},
        #{item.sysUserId},
        #{item.catalog},
        #{item.status},
        #{item.modifyTime},
        #{item.createTime}
        )
        </foreach>
    </insert>
</mapper>