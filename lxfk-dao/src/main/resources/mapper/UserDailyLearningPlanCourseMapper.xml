<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dao.UserDailyLearningPlanCourseDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap id="userDailyLearningPlanCourseMap" type="com.domain.UserDailyLearningPlanCourse">
        <result column="id" property="id"/>
        <result column="user_daily_learning_plan_id" property="userDailyLearningPlanId"/>
        <result column="course_catalog_id" property="courseCatalogId"/>
        <result column="course_id" property="courseId"/>
        <result column="allocation_stage" property="allocationStage"/>
        <result column="sort" property="sort"/>
        <result column="status" property="status"/>
        <result column="modify_time" property="modifyTime"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <select id="select" resultMap="userDailyLearningPlanCourseMap" parameterType="com.domain.complex.UserDailyLearningPlanCourseQuery">
        select * from tb_user_daily_learning_plan_course
        <trim prefix="where" prefixOverrides="and">
            <if test=" id != null">
                and id = #{id}
            </if>
            <if test="ids != null and ids.size > 0">
                and id in
                <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="boundIds != null and boundIds.size > 0">
                and id not in
                <foreach collection="boundIds" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test=" userDailyLearningPlanId != null">
                and user_daily_learning_plan_id = #{userDailyLearningPlanId}
            </if>
            <if test="userDailyLearningPlanIds != null and userDailyLearningPlanIds.size > 0">
                and user_daily_learning_plan_id in
                <foreach collection="userDailyLearningPlanIds" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test=" courseCatalogId != null">
                and course_catalog_id = #{courseCatalogId}
            </if>
            <if test=" courseCatalogIds != null and courseCatalogIds.size > 0">
                and course_catalog_id in
                <foreach collection="courseCatalogIds" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test=" courseId != null">
                and course_id = #{courseId}
            </if>
            <if test=" courseIds != null and courseIds.size > 0">
                and course_id in
                <foreach collection="courseIds" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test=" allocationStage != null">
                and allocation_stage = #{allocationStage}
            </if>
            <if test=" sort != null">
                and sort = #{sort}
            </if>
            <if test=" status != null">
                and status = #{status}
            </if>
            <if test=" modifyTime != null">
                and modify_time = #{modifyTime}
            </if>
            <if test=" createTime != null">
                and create_time = #{createTime}
            </if>
        </trim>
        order by sort limit #{start},#{limit}
    </select>

    <select id="selectAll" resultMap="userDailyLearningPlanCourseMap" parameterType="com.domain.complex.UserDailyLearningPlanCourseQuery">
        select * from tb_user_daily_learning_plan_course
        <trim prefix="where" prefixOverrides="and">
            <if test=" id != null">
                and id = #{id}
            </if>
            <if test="ids != null and ids.size > 0">
                and id in
                <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="boundIds != null and boundIds.size > 0">
                and id not in
                <foreach collection="boundIds" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test=" userDailyLearningPlanId != null">
                and user_daily_learning_plan_id = #{userDailyLearningPlanId}
            </if>
            <if test="userDailyLearningPlanIds != null and userDailyLearningPlanIds.size > 0">
                and user_daily_learning_plan_id in
                <foreach collection="userDailyLearningPlanIds" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test=" courseCatalogId != null">
                and course_catalog_id = #{courseCatalogId}
            </if>
            <if test=" courseCatalogIds != null and courseCatalogIds.size > 0">
                and course_catalog_id in
                <foreach collection="courseCatalogIds" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test=" courseId != null">
                and course_id = #{courseId}
            </if>
            <if test=" courseIds != null and courseIds.size > 0">
                and course_id in
                <foreach collection="courseIds" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test=" allocationStage != null">
                and allocation_stage = #{allocationStage}
            </if>
            <if test=" sort != null">
                and sort = #{sort}
            </if>
            <if test=" status != null">
                and status = #{status}
            </if>
            <if test=" modifyTime != null">
                and modify_time = #{modifyTime}
            </if>
            <if test=" createTime != null">
                and create_time = #{createTime}
            </if>
        </trim>
        order by sort
    </select>

    <update id="updateById" parameterType="com.domain.UserDailyLearningPlanCourse">
        update tb_user_daily_learning_plan_course
        <trim prefix="set" suffixOverrides=",">
                <if test=" userDailyLearningPlanId != null">
                   user_daily_learning_plan_id = #{userDailyLearningPlanId},
                </if>
                <if test=" courseCatalogId != null">
                    course_catalog_id = #{courseCatalogId},
                </if>
                <if test=" courseId != null">
                   course_id = #{courseId},
                </if>
                <if test=" allocationStage != null">
                   allocation_stage = #{allocationStage},
                </if>
                <if test=" sort != null">
                    sort = #{sort},
                </if>
                <if test=" status != null">
                   status = #{status},
                </if>
                <if test=" modifyTime != null">
                   modify_time = #{modifyTime},
                </if>
                <if test=" createTime != null">
                   create_time = #{createTime},
                </if>
        </trim>
        where id = #{id}
    </update>

    <insert id="insert" parameterType="com.domain.UserDailyLearningPlanCourse" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        insert into tb_user_daily_learning_plan_course
        <trim suffixOverrides="," prefix="(" suffix=") values">
                <if test=" id != null">
                    id,
                </if>
                <if test=" userDailyLearningPlanId != null">
                    user_daily_learning_plan_id,
                </if>
                <if test=" courseCatalogId != null">
                    course_catalog_id,
                </if>
                <if test=" courseId != null">
                    course_id,
                </if>
                <if test=" allocationStage != null">
                    allocation_stage,
                </if>
                <if test=" sort != null">
                    sort,
                </if>
                <if test=" status != null">
                    status,
                </if>
                <if test=" modifyTime != null">
                    modify_time,
                </if>
                <if test=" createTime != null">
                    create_time,
                </if>
        </trim>
        <trim suffixOverrides="," prefix="(" suffix=")">
                <if test=" id != null">
                    #{id},
                </if>
                <if test=" userDailyLearningPlanId != null">
                    #{userDailyLearningPlanId},
                </if>
                <if test=" courseCatalogId != null">
                    #{courseCatalogId},
                </if>
                <if test=" courseId != null">
                    #{courseId},
                </if>
                <if test=" allocationStage != null">
                    #{allocationStage},
                </if>
                <if test=" sort != null">
                    #{sort},
                </if>
                <if test=" status != null">
                    #{status},
                </if>
                <if test=" modifyTime != null">
                    #{modifyTime},
                </if>
                <if test=" createTime != null">
                    #{createTime},
                </if>
        </trim>
    </insert>

    <select id="count" resultType="java.lang.Integer" parameterType="com.domain.complex.UserDailyLearningPlanCourseQuery">
        select count(*) from tb_user_daily_learning_plan_course
        <trim prefix="where" prefixOverrides="and">
                <if test="ids != null and ids.size > 0">
                    and id in
                    <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="boundIds != null and boundIds.size > 0">
                    and id not in
                    <foreach collection="boundIds" item="item" index="index" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test=" id != null ">
                    and id = #{id}
                </if>
                <if test=" userDailyLearningPlanId != null ">
                    and user_daily_learning_plan_id = #{userDailyLearningPlanId}
                </if>
                <if test="userDailyLearningPlanIds != null and userDailyLearningPlanIds.size > 0">
                    and user_daily_learning_plan_id in
                <foreach collection="userDailyLearningPlanIds" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
                </if>
                <if test=" courseCatalogId != null">
                    and course_catalog_id = #{courseCatalogId}
                </if>
                <if test=" courseCatalogIds != null and courseCatalogIds.size > 0">
                    and course_catalog_id in
                    <foreach collection="courseCatalogIds" item="item" index="index" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test=" courseId != null ">
                    and course_id = #{courseId}
                </if>
                <if test=" courseIds != null and courseIds.size > 0">
                    and course_id in
                    <foreach collection="courseIds" item="item" index="index" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test=" allocationStage != null ">
                    and allocation_stage = #{allocationStage}
                </if>
                <if test=" sort != null ">
                    and sort = #{sort}
                </if>
                <if test=" status != null ">
                    and status = #{status}
                </if>
                <if test=" modifyTime != null ">
                    and modify_time = #{modifyTime}
                </if>
                <if test=" createTime != null ">
                    and create_time = #{createTime}
                </if>
        </trim>
    </select>

    <select id="selectById" resultMap="userDailyLearningPlanCourseMap">
        select * from tb_user_daily_learning_plan_course where id = #{id}
    </select>

    <select id="sortMax" resultType="java.lang.Integer" parameterType="com.domain.complex.UserDailyLearningPlanCourseQuery">
        select IFNULL(MAX(sort),0) from tb_user_daily_learning_plan_course
        <trim prefix="where" prefixOverrides="and">
            <if test=" id != null ">
                and id = #{id}
            </if>
            <if test=" userDailyLearningPlanId != null ">
                and user_daily_learning_plan_id = #{userDailyLearningPlanId}
            </if>
            <if test="userDailyLearningPlanIds != null and userDailyLearningPlanIds.size > 0">
                and user_daily_learning_plan_id in
                <foreach collection="userDailyLearningPlanIds" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test=" courseCatalogId != null">
                and course_catalog_id = #{courseCatalogId}
            </if>
            <if test=" courseCatalogIds != null and courseCatalogIds.size > 0">
                and course_catalog_id in
                <foreach collection="courseCatalogIds" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test=" courseId != null ">
                and course_id = #{courseId}
            </if>
            <if test=" courseIds != null and courseIds.size > 0">
                and course_id in
                <foreach collection="courseIds" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test=" allocationStage != null ">
                and allocation_stage = #{allocationStage}
            </if>
            <if test=" sort != null ">
                and sort = #{sort}
            </if>
            <if test=" status != null ">
                and status = #{status}
            </if>
            <if test=" modifyTime != null ">
                and modify_time = #{modifyTime}
            </if>
            <if test=" createTime != null ">
                and create_time = #{createTime}
            </if>
        </trim>
    </select>

</mapper>