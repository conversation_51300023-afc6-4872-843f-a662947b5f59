<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dao.CourseCatalogChapterVideoDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap id="courseCatalogChapterVideoMap" type="com.domain.CourseCatalogChapterVideo">
        <result column="id" property="id"/>
        <result column="chapter_id" property="chapterId"/>
        <result column="video_id" property="videoId"/>
        <result column="live_time" property="liveTime"/>
        <result column="sort" property="sort"/>
        <result column="status" property="status"/>
        <result column="modify_time" property="modifyTime"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <select id="select" resultMap="courseCatalogChapterVideoMap" parameterType="com.domain.complex.CourseCatalogChapterVideoQuery">
        select * from tb_course_catalog_chapter_video
        <trim prefix="where" prefixOverrides="and">
            <if test=" id != null">
                and id = #{id}
            </if>
            <if test=" chapterId != null">
                and chapter_id = #{chapterId}
            </if>
            <if test=" chapterIds != null and chapterIds.size > 0">
                and chapter_id in
                <foreach collection="chapterIds" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test=" videoId != null">
                and video_id = #{videoId}
            </if>
            <if test=" videoIds != null and videoIds.size > 0">
                and video_id in
                <foreach collection="videoIds" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="liveTime != null">
                and live_time = #{liveTime}
            </if>
            <if test="minLiveTime != null">
                and live_time <![CDATA[ >= ]]> #{minLiveTime}
            </if>
            <if test="maxLiveTime != null">
                and live_time <![CDATA[ < ]]> #{maxLiveTime}
            </if>
            <if test=" sort != null">
                and sort = #{sort}
            </if>
            <if test=" status != null">
                and status = #{status}
            </if>
            <if test=" modifyTime != null">
                and modify_time = #{modifyTime}
            </if>
            <if test=" createTime != null">
                and create_time = #{createTime}
            </if>
        </trim>
        order by id desc limit #{start},#{limit}
    </select>

    <select id="selectAll" resultMap="courseCatalogChapterVideoMap" parameterType="com.domain.complex.CourseCatalogChapterVideoQuery">
        select * from tb_course_catalog_chapter_video
        <trim prefix="where" prefixOverrides="and">
            <if test=" id != null">
                and id = #{id}
            </if>
            <if test=" chapterId != null">
                and chapter_id = #{chapterId}
            </if>
            <if test=" chapterIds != null and chapterIds.size > 0">
                and chapter_id in
                <foreach collection="chapterIds" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test=" videoId != null">
                and video_id = #{videoId}
            </if>
            <if test=" videoIds != null and videoIds.size > 0">
                and video_id in
                <foreach collection="videoIds" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="liveTime != null">
                and live_time = #{liveTime}
            </if>
            <if test="minLiveTime != null">
                and live_time <![CDATA[ >= ]]> #{minLiveTime}
            </if>
            <if test="maxLiveTime != null">
                and live_time <![CDATA[ < ]]> #{maxLiveTime}
            </if>
            <if test=" sort != null">
                and sort = #{sort}
            </if>
            <if test=" status != null">
                and status = #{status}
            </if>
            <if test=" modifyTime != null">
                and modify_time = #{modifyTime}
            </if>
            <if test=" createTime != null">
                and create_time = #{createTime}
            </if>
        </trim>
        order by id desc
    </select>

    <update id="updateById" parameterType="com.domain.CourseCatalogChapterVideo">
        update tb_course_catalog_chapter_video
        <trim prefix="set" suffixOverrides=",">
                <if test=" id != null">
                   id = #{id},
                </if>
                <if test=" chapterId != null">
                   chapter_id = #{chapterId},
                </if>
                <if test=" videoId != null">
                   video_id = #{videoId},
                </if>
                <if test=" liveTime != null">
                    live_time = #{liveTime},
                </if>
                <if test=" sort != null">
                    sort = #{sort},
                </if>
                <if test=" status != null">
                   status = #{status},
                </if>
                <if test=" modifyTime != null">
                   modify_time = #{modifyTime},
                </if>
                <if test=" createTime != null">
                   create_time = #{createTime},
                </if>
        </trim>
        where id = #{id}
    </update>

    <update id="updateLiveTime" parameterType="com.domain.CourseCatalogChapterVideo">
        update tb_course_catalog_chapter_video
        <trim prefix="set" suffixOverrides=",">
            <if test=" id != null">
                id = #{id},
            </if>
            <if test=" chapterId != null">
                chapter_id = #{chapterId},
            </if>
            <if test=" videoId != null">
                video_id = #{videoId},
            </if>

                live_time = #{liveTime},

            <if test=" sort != null">
                sort = #{sort},
            </if>
            <if test=" status != null">
                status = #{status},
            </if>
            <if test=" modifyTime != null">
                modify_time = #{modifyTime},
            </if>
            <if test=" createTime != null">
                create_time = #{createTime},
            </if>
        </trim>
        where id = #{id}
    </update>

    <insert id="insert" parameterType="com.domain.CourseCatalogChapterVideo" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        insert into tb_course_catalog_chapter_video
        <trim suffixOverrides="," prefix="(" suffix=") values">
                <if test=" id != null">
                    id,
                </if>
                <if test=" chapterId != null">
                    chapter_id,
                </if>
                <if test=" videoId != null">
                    video_id,
                </if>
                <if test=" liveTime != null">
                    live_time,
                </if>
                <if test=" sort != null">
                    sort,
                </if>
                <if test=" status != null">
                    status,
                </if>
                <if test=" modifyTime != null">
                    modify_time,
                </if>
                <if test=" createTime != null">
                    create_time,
                </if>
        </trim>
        <trim suffixOverrides="," prefix="(" suffix=")">
                <if test=" id != null">
                    #{id},
                </if>
                <if test=" chapterId != null">
                    #{chapterId},
                </if>
                <if test=" videoId != null">
                    #{videoId},
                </if>
                <if test=" liveTime != null">
                    #{liveTime},
                </if>
                <if test=" sort != null">
                    #{sort},
                </if>
                <if test=" status != null">
                    #{status},
                </if>
                <if test=" modifyTime != null">
                    #{modifyTime},
                </if>
                <if test=" createTime != null">
                    #{createTime},
                </if>
        </trim>
    </insert>

    <select id="count" resultType="java.lang.Integer" parameterType="com.domain.complex.CourseCatalogChapterVideoQuery">
        select count(*) from tb_course_catalog_chapter_video
        <trim prefix="where" prefixOverrides="and">
                <if test=" id != null ">
                    and id = #{id}
                </if>
                <if test=" chapterId != null ">
                    and chapter_id = #{chapterId}
                </if>
                <if test=" chapterIds != null and chapterIds.size > 0">
                    and chapter_id in
                    <foreach collection="chapterIds" item="item" index="index" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test=" videoId != null ">
                    and video_id = #{videoId}
                </if>
                <if test=" videoIds != null and videoIds.size > 0">
                    and video_id in
                    <foreach collection="videoIds" item="item" index="index" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="liveTime != null">
                    and live_time = #{liveTime}
                </if>
                <if test="minLiveTime != null">
                    and live_time <![CDATA[ >= ]]> #{minLiveTime}
                </if>
                <if test="maxLiveTime != null">
                    and live_time <![CDATA[ < ]]> #{maxLiveTime}
                </if>
                <if test=" sort != null">
                    and sort = #{sort}
                </if>
                <if test=" status != null ">
                    and status = #{status}
                </if>
                <if test=" modifyTime != null ">
                    and modify_time = #{modifyTime}
                </if>
                <if test=" createTime != null ">
                    and create_time = #{createTime}
                </if>
        </trim>
    </select>

    <select id="selectById" resultMap="courseCatalogChapterVideoMap">
        select * from tb_course_catalog_chapter_video where id = #{id}
    </select>

</mapper>