package com.dao;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;

import com.domain.UserInformationCommentFlow;
import com.domain.complex.UserInformationCommentFlowQuery;
/**
 * 用户信息备注明细表持久层
 *
 * @date 2025-08-11 17:24:47
 */
@Mapper
public interface UserInformationCommentFlowDao {
    List<UserInformationCommentFlow> select(UserInformationCommentFlowQuery userInformationCommentFlowQuery);

    Integer insert(UserInformationCommentFlow userInformationCommentFlow);

    Integer updateById(UserInformationCommentFlow userInformationCommentFlow);

    List<UserInformationCommentFlow> selectAll(UserInformationCommentFlowQuery userInformationCommentFlowQuery);

    Integer count(UserInformationCommentFlowQuery userInformationCommentFlowQuery);

    UserInformationCommentFlow selectById(Long id);

    List<UserInformationCommentFlow> selectByIds(List<Long> ids);

    Integer insertBatch(List<UserInformationCommentFlow> userInformationCommentFlows);

}
