package com.dao;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;

import com.domain.ReportUserInformation;
import com.domain.complex.ReportUserInformationQuery;
/**
 * 用户信息统计表持久层
 *
 * @date 2025-08-12 11:17:25
 */
@Mapper
public interface ReportUserInformationDao {
    List<ReportUserInformation> select(ReportUserInformationQuery reportUserInformationQuery);

    Integer insert(ReportUserInformation reportUserInformation);

    Integer updateById(ReportUserInformation reportUserInformation);

    List<ReportUserInformation> selectAll(ReportUserInformationQuery reportUserInformationQuery);

    Integer count(ReportUserInformationQuery reportUserInformationQuery);

    ReportUserInformation selectById(Integer id);

    List<ReportUserInformation> selectByIds(List<Integer> ids);

    Integer insertBatch(List<ReportUserInformation> reportUserInformations);

}
