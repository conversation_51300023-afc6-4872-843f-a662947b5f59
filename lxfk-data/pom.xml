<?xml version="1.0"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.local</groupId>
    <artifactId>lxfk</artifactId>
    <version>dev</version>
  </parent>
  <groupId>com.local.lxfk.data</groupId>
  <artifactId>lxfk-data</artifactId>
  <name>lxfk-data</name>
  <packaging>war</packaging>
  <url>http://maven.apache.org</url>
  <dependencies>
  	<dependency>
  		<groupId>com.local.lxfk.common</groupId>
  		<artifactId>lxfk-common</artifactId>
  		<version>${project.version}</version>
  	</dependency>
    <dependency>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter</artifactId>
	</dependency>
  	<dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-web</artifactId>
    </dependency>
    <dependency>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-thymeleaf</artifactId>
	</dependency>
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-aop</artifactId>
    </dependency>
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-data-redis</artifactId>
    </dependency>
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-integration</artifactId>
    </dependency>
    <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-redis</artifactId>
    </dependency>
    <dependency>
	    <groupId>org.apache.httpcomponents</groupId>
	    <artifactId>httpclient</artifactId>
	</dependency>
	<dependency>
	  <groupId>net.coobird</groupId>
	  <artifactId>thumbnailator</artifactId>
	</dependency>
	<dependency>
	    <groupId>org.jsoup</groupId>
	    <artifactId>jsoup</artifactId>
	</dependency>
	<dependency>
	    <groupId>com.belerweb</groupId>
	    <artifactId>pinyin4j</artifactId>
	</dependency>
	<dependency>
	    <groupId>com.alibaba</groupId>
	    <artifactId>druid</artifactId>
	</dependency>
	<dependency>
	    <groupId>mysql</groupId>
	    <artifactId>mysql-connector-java</artifactId>
	</dependency>
	<dependency>
	    <groupId>org.mybatis.spring.boot</groupId>
	    <artifactId>mybatis-spring-boot-starter</artifactId>
	</dependency>
  	<dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-devtools</artifactId>
        <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <scope>test</scope>
    </dependency>
  </dependencies>
  <build>
  	<plugins>
  		<plugin>
		    <groupId>org.springframework.boot</groupId>
		    <artifactId>spring-boot-maven-plugin</artifactId>
		    <configuration>
		      <fork>true</fork>
		      <skip>false</skip>
		    </configuration>
		    <executions>
		      <execution>
		        <goals>
		          <goal>repackage</goal>
		        </goals>
		      </execution>
		    </executions>
		  </plugin>
		  <plugin>
			<groupId>org.codehaus.mojo</groupId>
			<artifactId>exec-maven-plugin</artifactId>
			<executions>
				<execution>
					<id>tomcat-8094-stop</id>
					<phase>package</phase>
					<goals>
						<goal>exec</goal>
					</goals>
					<configuration>
						<executable>sh</executable>
						<workingDirectory>/home/<USER>/tomcat/dev/tomcat-8094/bin</workingDirectory>
						<arguments>
							<argument>./daemon.sh</argument>
							<argument>stop</argument>
						</arguments>
					</configuration>
				</execution>
				<execution>
					<id>tomcat-8094-start</id>
					<phase>package</phase>
					<goals>
						<goal>exec</goal>
					</goals>
					<configuration>
						<executable>sh</executable>
						<workingDirectory>/home/<USER>/tomcat/dev/tomcat-8094/bin</workingDirectory>
						<arguments>
							<argument>./daemon.sh</argument>
							<argument>start</argument>
						</arguments>
					</configuration>
				</execution>
			</executions>
		</plugin>
  	</plugins>
  </build>
</project>