###########################################################
# primary
spring.datasource.primary.type=com.alibaba.druid.pool.DruidDataSource
spring.datasource.primary.url=********************************************************************
spring.datasource.primary.username=lxfk
spring.datasource.primary.password=lxfk
spring.datasource.primary.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.primary.initialSize=1
spring.datasource.primary.minIdle=1
spring.datasource.primary.maxActive=5
spring.datasource.primary.maxWait=60000
spring.datasource.primary.timeBetweenEvictionRunsMillis=60000
spring.datasource.primary.minEvictableIdleTimeMillis=300000
spring.datasource.primary.validationQuery=SELECT 1 FROM DUAL
spring.datasource.primary.testWhileIdle=true
spring.datasource.primary.testOnBorrow=false
spring.datasource.primary.testOnReturn=false
spring.datasource.primary.poolPreparedStatements=true
spring.datasource.primary.maxPoolPreparedStatementPerConnectionSize=20
spring.datasource.primary.filters=stat,wall,log4j
spring.datasource.primary.connectionProperties=druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
spring.datasource.primary.useGlobalDataSourceStat=true
# backup
spring.datasource.backup.type=com.alibaba.druid.pool.DruidDataSource
spring.datasource.backup.url=********************************************************************
spring.datasource.backup.username=lxfk
spring.datasource.backup.password=lxfk
spring.datasource.backup.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.backup.initialSize=1
spring.datasource.backup.minIdle=1
spring.datasource.backup.maxActive=5
spring.datasource.backup.maxWait=60000
spring.datasource.backup.timeBetweenEvictionRunsMillis=60000
spring.datasource.backup.minEvictableIdleTimeMillis=300000
spring.datasource.backup.validationQuery=SELECT 1 FROM DUAL
spring.datasource.backup.testWhileIdle=true
spring.datasource.backup.testOnBorrow=false
spring.datasource.backup.testOnReturn=false
spring.datasource.backup.poolPreparedStatements=true
spring.datasource.backup.maxPoolPreparedStatementPerConnectionSize=20
spring.datasource.backup.filters=stat,wall,log4j
spring.datasource.backup.connectionProperties=druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
spring.datasource.backup.useGlobalDataSourceStat=true
###########################################################
# logback
logging.config=classpath:config/logback-spring.xml
###########################################################
app.mq.nameserver=localhost:9876
###########################################################
###########################################################
spring.redis.host=127.0.0.1
spring.redis.password=38659215534F141B5496BDF477D909A0
spring.redis.port=6379
spring.redis.database=4
###########################################################