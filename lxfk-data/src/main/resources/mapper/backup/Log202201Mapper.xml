<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.data.dao.backup.Log202201Dao">
	<resultMap id="logResultMap" type="com.data.domain.Log">
		<id column="id" property="id" />
		<result column="platform" property="platform" />
		<result column="version" property="version" />
		<result column="type" property="type" />
		<result column="user_id" property="userId" />
		<result column="ip" property="ip" />
		<result column="url" property="url" />
		<result column="request_id" property="requestId" />
		<result column="start_time" property="startTime" />
		<result column="end_time" property="endTime" />
		<result column="duration" property="duration" />
		<result column="status" property="status" />
		<result column="modify_time" property="modifyTime" />
		<result column="create_time" property="createTime" />
	</resultMap>
	<select id="selectById" resultMap="logResultMap">
		select * from tb_log_202201 where id = #{id}
	</select>
	<select id="selectByIds" resultMap="logResultMap">
		select * from tb_log_202201 where id in
		<foreach collection="list" item="item" index="index" open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>
	<select id="selectMin" resultMap="logResultMap" parameterType="com.data.domain.Log">
		select * from tb_log_202201 order by id asc limit 0,1
	</select>
	<insert id="insert" parameterType="com.data.domain.Log" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
		insert into tb_log_202201
		<trim suffixOverrides="," prefix="(" suffix=") values">
			<if test="id != null">
				 id,
			</if>
			<if test="platform != null">
				platform,
			</if>
			<if test="version != null">
				version,
			</if>
			<if test="type != null">
				type,
			</if>
			<if test="userId != null">
				user_id,
			</if>
			<if test="ip != null">
				ip,
			</if>
			<if test="url != null">
				url,
			</if>
			<if test="requestId != null">
				request_id,
			</if>
			<if test="startTime != null">
				start_time,
			</if>
			<if test="endTime != null">
				end_time,
			</if>
			<if test="duration != null">
				duration,
			</if>
			<if test="status != null">
				 status,
			</if>
			<if test="modifyTime != null">
				 modify_time,
			</if>
			<if test="createTime != null">
				 create_time,
			</if>
		</trim>
		<trim suffixOverrides="," prefix="(" suffix=")">
			<if test="id != null">
				#{id},
			</if>
			<if test="platform != null">
				#{platform},
			</if>
			<if test="version != null">
				#{version},
			</if>
			<if test="type != null">
				#{type},
			</if>
			<if test="userId != null">
				#{userId},
			</if>
			<if test="ip != null">
				#{ip},
			</if>
			<if test="url != null">
				#{url},
			</if>
			<if test="requestId != null">
				#{requestId},
			</if>
			<if test="startTime != null">
				#{startTime},
			</if>
			<if test="endTime != null">
				#{endTime},
			</if>
			<if test="duration != null">
				#{duration},
			</if>
			<if test="status != null">
				#{status},
			</if>
			<if test="modifyTime != null">
				#{modifyTime},
			</if>
			<if test="createTime != null">
				#{createTime},
			</if>
		</trim>
	</insert>
</mapper>