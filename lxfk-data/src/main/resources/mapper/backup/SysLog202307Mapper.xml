<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.data.dao.backup.SysLog202307Dao">
    <resultMap id="sysLogResultMap" type="com.data.domain.SysLog">
        <id column="id" property="id" />
        <result column="sys_user_id" property="sysUserId" />
        <result column="url" property="url" />
        <result column="request_id" property="requestId" />
        <result column="request" property="request" />
        <result column="response" property="response" />
        <result column="status" property="status" />
        <result column="modify_time" property="modifyTime" />
        <result column="create_time" property="createTime" />
    </resultMap>
    <select id="selectById" resultMap="sysLogResultMap">
		select * from tb_sys_log_202307 where id = #{id}
	</select>
    <select id="selectByIds" resultMap="sysLogResultMap">
        select * from tb_sys_log_202307 where id in
        <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="selectMin" resultMap="sysLogResultMap" parameterType="com.data.domain.SysLog">
		select * from tb_sys_log_202307 order by id asc limit 0,1
	</select>
    <insert id="insert" parameterType="com.data.domain.SysLog" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        insert into tb_sys_log_202307
        <trim suffixOverrides="," prefix="(" suffix=") values">
            <if test="id != null">
                id,
            </if>
            <if test="sysUserId != null">
                sys_user_id,
            </if>
            <if test="url != null">
                url,
            </if>
            <if test="requestId != null">
                request_id,
            </if>
            <if test="request != null">
                request,
            </if>
            <if test="response != null">
                response,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="modifyTime != null">
                modify_time,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
        </trim>
        <trim suffixOverrides="," prefix="(" suffix=")">
            <if test="id != null">
                #{id},
            </if>
            <if test="sysUserId != null">
                #{sysUserId},
            </if>
            <if test="url != null">
                #{url},
            </if>
            <if test="requestId != null">
                #{requestId},
            </if>
            <if test="request != null">
                #{request},
            </if>
            <if test="response != null">
                #{response},
            </if>
            <if test="status != null">
                #{status},
            </if>
            <if test="modifyTime != null">
                #{modifyTime},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
        </trim>
    </insert>
    <delete id="deleteById">
		delete from tb_sys_log_202307 where id = #{id}
	</delete>
</mapper>