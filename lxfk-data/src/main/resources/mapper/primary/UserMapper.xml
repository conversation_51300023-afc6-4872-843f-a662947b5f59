<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.data.dao.primary.UserDao">
	<resultMap id="userResultMap" type="com.data.domain.User">
		<id column="id" property="id" />
		<result column="username" property="userName" />
		<result column="password" property="password" />
		<result column="name" property="name" />
		<result column="gender" property="gender" />
		<result column="mobile" property="mobile" />
		<result column="nation" property="nation" />
		<result column="birthday" property="birthday" />
		<result column="avatar" property="avatar" />
		<result column="address" property="address" />
		<result column="id_number" property="idNumber" />
		<result column="qq" property="qq" />
		<result column="wechat" property="wechat" />
		<result column="email" property="email" />
		<result column="province_id" property="provinceId" />
		<result column="city_id" property="cityId" />
		<result column="project_id" property="projectId" />
		<result column="source_account_id" property="sourceAccountId" />
		<result column="comment" property="comment" />
		<result column="status" property="status" />
		<result column="catalog" property="catalog" />
		<result column="communication_time" property="communicationTime" />
		<result column="communication_stage" property="communicationStage" />
		<result column="communication_next_datetime" property="communicationNextDateTime" />
		<result column="client" property="client" />
		<result column="stock_time" property="stockTime" />
		<result column="url" property="url" />
		<result column="source" property="source" />
		<result column="ad" property="ad" />
		<result column="stage" property="stage" />
		<result column="modify_time" property="modifyTime" />
		<result column="create_time" property="createTime" />
	</resultMap>

	<select id="selectById" resultMap="userResultMap" >
		select * from tb_user where id = #{id}
	</select>

	<select id="selectByUsername" resultMap="userResultMap">
		select * from tb_user where username = #{username}
	</select>

	<select id="selectByIds" resultMap="userResultMap">
		select * from tb_user where id in
		<foreach collection="list" item="item" index="index" open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>

	<update id="updateById" parameterType="com.data.domain.User">
		update tb_user
		<trim prefix="set" suffixOverrides=",">
			<if test="username != null">
				 username = #{username},
			</if>
			<if test="name != null">
				 name = #{name},
			</if>
			<if test="password != null">
				 password = #{password},
			</if>
			<if test="mobile != null">
				 mobile = #{mobile},
			</if>
			<if test="gender != null">
				 gender = #{gender},
			</if>
			<if test="nation != null">
				 nation = #{nation},
			</if>
			<if test="birthday != null">
				 birthday = #{birthday},
			</if>
			<if test="avatar != null">
				 avatar = #{avatar},
			</if>
			<if test="address != null">
				 address = #{address},
			</if>
			<if test="idNumber != null">
				 id_number = #{idNumber},
			</if>
			<if test="qq != null">
				 qq = #{qq},
			</if>
			<if test="wechat != null">
				 wechat = #{wechat},
			</if>
			<if test="email != null">
				 email = #{email},
			</if>
				<if test="provinceId != null">
				 province_id = #{provinceId},
			</if>
			<if test="cityId != null">
				 city_id = #{cityId},
			</if>
			<if test="projectId != null">
				 project_id = #{projectId},
			</if>
			<if test="sourceAccountId != null">
				 source_account_id = #{sourceAccountId},
			</if>
			<if test="comment != null">
				 comment = #{comment},
			</if>
			<if test="status != null">
				 status = #{status},
			</if>
				<if test="communicationTime != null">
				 communication_time = #{communicationTime},
			</if>
			<if test="communicationStage != null">
				 communication_stage = #{communicationStage},
			</if>
			<if test="communicationNextDateTime != null">
				 communication_next_datetime = #{communicationNextDateTime},
			</if>
			<if test="catalog != null">
				 catalog = #{catalog},
			</if>
			<if test="url != null">
				  url = #{url}, 
			</if>
			<if test="source != null">
				 source = #{source}, 
			</if>
			<if test="client != null">
				  client = #{client}, 
			</if>
			<if test="stockTime != null">
				  stock_time = #{stockTime}, 
			</if>
			<if test="stage != null">
				 stage = #{stage},
			</if>
			<if test="ad != null">
				 ad = #{ad},
			</if>
			<if test="modifyTime != null">
				 modify_time = #{modifyTime},
			</if>
			<if test="createTime != null">
				 create_time = #{createTime},
			</if>
		</trim>
		where id = #{id}
	</update>

	<insert id="insert" parameterType="com.data.domain.User" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
		insert into tb_user
		<trim suffixOverrides="," prefix="(" suffix=") values">
			<if test="id != null">
				 id,
			</if>
			<if test="username != null">
				 username,
			</if>
			<if test="password != null">
				 password,
			</if>
			<if test="name != null">
				 name,
			</if>
			<if test="mobile != null">
				 mobile,
			</if>
			<if test="gender != null">
				 gender,
			</if>
			<if test="nation != null">
				 nation,
			</if>
			<if test="birthday != null">
				 birthday,
			</if>
			<if test="avatar != null">
				 avatar,
			</if>
			<if test="address != null">
				 address,
			</if>
			<if test="idNumber != null">
				 id_number,
			</if>
			<if test="qq != null">
				 qq,
			</if>
			<if test="wechat != null">
				 wechat,
			</if>
			<if test="email != null">
				 email,
			</if>
			<if test="provinceId != null">
				 province_id,
			</if>
			<if test="cityId != null">
				 city_id,
			</if>
			<if test="projectId != null">
				 project_id,
			</if>
			<if test="sourceAccountId != null">
				 source_account_id,
			</if>
			<if test="comment != null">
				 comment,
			</if>
			<if test="status != null">
				 status,
			</if>
			<if test="communicationTime != null">
				 communication_time,
			</if>
			<if test="communicationStage != null">
				 communication_stage,
			</if>
			<if test="communicationNextDateTime != null">
				 communication_next_datetime,
			</if>
			<if test="catalog != null">
				 catalog,
			</if>
			<if test="url != null">
				  url, 
			</if>
			<if test="source != null">
				  source, 
			</if>
			<if test="client != null">
				 client, 
			</if>
			<if test="stockTime != null">
				 stock_time, 
			</if>
			<if test="stage != null">
				 stage,
			</if>
			<if test="ad != null">
				 ad,
			</if>
			<if test="modifyTime != null">
				 modify_time,
			</if>
			<if test="createTime != null">
				 create_time,
			</if>
		</trim>
		<trim suffixOverrides="," prefix="(" suffix=")">
			<if test="id != null">
				#{id},
			</if>
			<if test="username != null">
				#{username},
			</if>
			<if test="password != null">
				#{password},
			</if>
			<if test="name != null">
				#{name},
			</if>
			<if test="mobile != null">
				#{mobile},
			</if>
			<if test="gender != null">
				#{gender},
			</if>
			<if test="nation != null">
				#{nation},
			</if>
			<if test="birthday != null">
				#{birthday},
			</if>
			<if test="avatar != null">
				#{avatar},
			</if>
			<if test="address != null">
				#{address},
			</if>
			<if test="idNumber != null">
				#{idNumber},
			</if>
			<if test="qq != null">
				#{qq},
			</if>
			<if test="wechat != null">
				#{wechat},
			</if>
			<if test="email != null">
				#{email},
			</if>
			<if test="provinceId != null">
				#{provinceId},
			</if>
			<if test="cityId != null">
				#{cityId},
			</if>
			<if test="projectId != null">
				#{projectId},
			</if>
			<if test="sourceAccountId != null">
				#{sourceAccountId},
			</if>
			<if test="comment != null">
				#{comment},
			</if>
			<if test="status != null">
				#{status},
			</if>
				<if test="communicationTime != null">
				#{communicationTime},
			</if>
			<if test="communicationStage != null">
				#{communicationStage},
			</if>
			<if test="communicationNextDateTime != null">
				#{communicationNextDateTime},
			</if>
			<if test="catalog != null">
				#{catalog},
			</if>
			<if test="url != null">
				 #{url}, 
			</if>
			<if test="source != null">
				 #{source}, 
			</if>
			<if test="client != null">
				#{client}, 
			</if>
			<if test="stockTime != null">
				#{stockTime}, 
			</if>
			<if test="stage != null">
				#{stage},
			</if>
			<if test="ad != null">
				#{ad},
			</if>
			<if test="modifyTime != null">
				#{modifyTime},
			</if>
			<if test="createTime != null">
				#{createTime},
			</if>
		</trim>
	</insert>

	<select id="selectByUsernames" resultMap="userResultMap">
		select * from tb_user where username in
		<foreach collection="list" item="item" index="index" open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>

	<select id="selectByCustomerId" resultMap="userResultMap">
		select * from tb_user where customer_id = #{customerId}
	</select>

</mapper>