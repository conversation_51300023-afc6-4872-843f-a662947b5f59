<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.data.dao.primary.SmsDao">
    <resultMap id="smsResultMap" type="com.data.domain.Sms">
        <id column="id" property="id"/>
        <result column="mobile" property="mobile"/>
        <result column="content" property="content"/>
        <result column="status" property="status"/>
        <result column="modify_time" property="modifyTime"/>
        <result column="create_time" property="createTime"/>
    </resultMap>
    <insert id="insert" parameterType="com.data.domain.Sms" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        insert into tb_sms
        <trim suffixOverrides="," prefix="(" suffix=") values">
            <if test="id != null">
                id,
            </if>
            <if test="mobile != null">
                mobile,
            </if>
            <if test="content != null">
                content,
            </if>

            <if test="status != null">
                status,
            </if>

            <if test="modifyTime != null">
                modify_time,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
        </trim>
        <trim suffixOverrides="," prefix="(" suffix=")">
            <if test="id != null">
                #{id},
            </if>
            <if test="mobile != null">
                #{mobile},
            </if>
            <if test="content != null">
                #{content},
            </if>
            <if test="status != null">
                #{status},
            </if>

            <if test="modifyTime != null">
                #{modifyTime},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
        </trim>
    </insert>
</mapper>