<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.data.dao.primary.CustomerDao">
	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.data.domain.Customer" id="customerMap">
        <result column="id" property="id"/>
        <result column="username" property="username"/>
        <result column="nickname" property="nickname"/>
        <result column="password" property="password"/>
        <result column="name" property="name"/>
        <result column="gender" property="gender"/>
        <result column="nation" property="nation"/>
        <result column="birthday" property="birthday"/>
        <result column="province_id" property="provinceId"/>
        <result column="city_id" property="cityId"/>
        <result column="avatar" property="avatar"/>
        <result column="id_number" property="idNumber"/>
        <result column="email" property="email"/>
        <result column="status" property="status"/>
        <result column="modify_time" property="modifyTime"/>
        <result column="create_time" property="createTime"/>
    </resultMap>


    <select id="selectById" resultMap="customerMap">
        select * from tb_customer where id = #{id}
    </select>


    <select id="selectByUsername" resultMap="customerMap">
        select * from tb_customer where username = #{username}
    </select>

</mapper>