<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.data.dao.primary.UserMessageDao">
	<resultMap id="userMessageResultMap" type="com.data.domain.UserMessage">
		<id column="id" property="id" />
		<id column="user_id" property="userId" />
		<id column="type" property="type" />
		<id column="content" property="content" />
		<result column="status" property="status" />
		<result column="modify_time" property="modifyTime" />
		<result column="create_time" property="createTime" />
	</resultMap>

	<insert id="insert" parameterType="com.data.domain.UserMessage" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
		insert into tb_user_message
		<trim suffixOverrides="," prefix="(" suffix=") values">
			<if test="id != null">
				 id,
			</if>
			<if test="userId != null">
				user_id,
			</if>
			<if test="type != null">
				type,
			</if>
			<if test="content != null">
				content,
			</if>
			<if test="status != null">
				status,
			</if>
			<if test="modifyTime != null">
				modify_time,
			</if>
			<if test="createTime != null">
				create_time,
			</if>
		</trim>
		<trim suffixOverrides="," prefix="(" suffix=")">
			<if test="id != null">
				#{id},
			</if>
			<if test="userId != null">
				#{userId},
			</if>
			<if test="type != null">
				#{type},
			</if>
			<if test="content != null">
				#{content},
			</if>
			<if test="status != null">
				#{status},
			</if>
			<if test="modifyTime != null">
				#{modifyTime},
			</if>
			<if test="createTime != null">
				#{createTime},
			</if>
		</trim>
	</insert>
</mapper>