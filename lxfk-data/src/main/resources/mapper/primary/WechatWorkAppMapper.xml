<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.data.dao.primary.WechatWorkAppDao">
	<resultMap id="wechatWorkApptResultMap" type="com.data.domain.WechatWorkApp">
		<id column="id" property="id" />
		<result column="corpid" property="corpid" />
		<result column="agentid" property="agentid" />
		<result column="secret" property="secret" />
		<result column="access_token" property="accessToken" />
		<result column="access_token_datetime" property="accessTokenDatetime" />
		<result column="success_count" property="successCount" />
		<result column="status" property="status" />
		<result column="modify_time" property="modifyTime" />
		<result column="create_time" property="createTime" />
	</resultMap>

	<select id="selectById" resultMap="wechatWorkApptResultMap">
		select * from tb_wechat_work_app where id = #{id}
	</select>

	<update id="updateById" parameterType="com.data.domain.WechatWorkApp">
		update tb_wechat_work_app
		<trim prefix="set" suffixOverrides=",">
		   <if test="corpid != null">
			   corpid = #{corpid},
			</if>
		   <if test="agentid != null">
			   agentid = #{agentid},
			</if>
		   <if test="secret != null">
			   secret = #{secret},
			</if>
		   <if test="accessToken != null">
			   access_token = #{accessToken},
			</if>
		   <if test="accessTokenDatetime != null">
			   access_token_datetime = #{accessTokenDatetime},
			</if>
		   <if test="status != null">
			   status = #{status},
			</if>
			<if test="modifyTime != null">
				 modify_time = #{modifyTime},
			</if>
		</trim>
		where id = #{id}
	</update>
</mapper>