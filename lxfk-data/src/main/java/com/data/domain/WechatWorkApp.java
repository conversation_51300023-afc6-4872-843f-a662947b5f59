package com.data.domain;

import java.util.Date;

/**
 * 企业微信应用
 * <AUTHOR>
 */
public class WechatWorkApp extends Base{

	private static final long serialVersionUID = 1L;

	private String corpid;  //企业ID
	private String agentid;  //应用ID
	private String secret;   //应用密钥
	private String accessToken;  //访问凭证
	private Date accessTokenDatetime;  //访问凭证


	public String getCorpid() {
		return corpid;
	}

	public void setCorpid(String corpid) {
		this.corpid = corpid;
	}

	public String getAgentid() {
		return agentid;
	}

	public void setAgentid(String agentid) {
		this.agentid = agentid;
	}

	public String getSecret() {
		return secret;
	}

	public void setSecret(String secret) {
		this.secret = secret;
	}

	public String getAccessToken() {
		return accessToken;
	}

	public void setAccessToken(String accessToken) {
		this.accessToken = accessToken;
	}

	public Date getAccessTokenDatetime() {
		return accessTokenDatetime;
	}

	public void setAccessTokenDatetime(Date accessTokenDatetime) {
		this.accessTokenDatetime = accessTokenDatetime;
	}

}
