package com.data.service;

import java.nio.charset.StandardCharsets;
import java.util.List;

import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.data.dao.backup.SysLogDao;
import com.data.domain.SysLog;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;

@Service
public class SysLogListener implements MessageListenerConcurrently {
	private Logger logger = LoggerFactory.getLogger(this.getClass());

	@Autowired
	private SysLogDao sysLogDao;

	public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs, ConsumeConcurrentlyContext context) {
		try {
			for (MessageExt msg : msgs) {
				String body = new String(msg.getBody(),StandardCharsets.UTF_8);
				logger.info("lxfk data consume sys log message : {}", body);
				SysLog sysLog = (SysLog)this.getObject(body, SysLog.class);
				sysLogDao.insert(sysLog);
			}
		} catch (Exception e) {
			logger.error(e.getMessage(),e);
		}
		return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
	}


	Object getObject(String str,Class<?> clazz){
		ObjectMapper objectMapper = new ObjectMapper();
		objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
		objectMapper.configure(DeserializationFeature.ACCEPT_SINGLE_VALUE_AS_ARRAY, true);
		try{
			return objectMapper.readValue(str, clazz);
		}catch (Exception e) {
			throw new RuntimeException(e);
		}
	}
}
