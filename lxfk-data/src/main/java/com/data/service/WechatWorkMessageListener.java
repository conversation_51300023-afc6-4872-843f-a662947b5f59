package com.data.service;

import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.List;

import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;

import com.common.bean.WechatWorkGetTokenServcieResponse;
import com.common.bean.WechatWorkMessageSendServiceResponse;
import com.common.bean.WechatWorkMessageSendTextServiceRequest;
import com.common.bean.WechatWorkServcieResponse;
import com.common.client.WechatWorkServiceClient;
import com.common.util.DateUtil;
import com.data.dao.primary.WechatWorkAppDao;
import com.data.domain.WechatWorkApp;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * <AUTHOR>
 * @date 2021/5/14 9:31
 */
@Service
public class WechatWorkMessageListener implements MessageListenerConcurrently {
	private Logger logger = LoggerFactory.getLogger(this.getClass());

	@Autowired
	private WechatWorkAppDao wechatWorkAppDao;

	@Override
	public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs, ConsumeConcurrentlyContext context) {
		try {
			for (MessageExt msg : msgs) {
				String body = new String(msg.getBody(), StandardCharsets.UTF_8);
				logger.info("lxfk data consume wechat work message : {}", body);
				// 10000002  企业微信应用->我的消息对应的id
				WechatWorkApp wechatWorkApp = this.findById(10000002);
				String url = String.format("/message/send?access_token=%s", wechatWorkApp.getAccessToken());
				WechatWorkMessageSendTextServiceRequest messageSendTextServiceRequest = (WechatWorkMessageSendTextServiceRequest)this.getObject(body, WechatWorkMessageSendTextServiceRequest.class);
				messageSendTextServiceRequest.setAgentid(Integer.valueOf(wechatWorkApp.getAgentid()));
				WechatWorkServiceClient<WechatWorkMessageSendServiceResponse> wechatWorkServiceClient = new WechatWorkServiceClient<WechatWorkMessageSendServiceResponse>();
				wechatWorkServiceClient.setMethod(HttpMethod.POST);
				WechatWorkMessageSendServiceResponse wechatWorkMessageSendServiceResponse = wechatWorkServiceClient.execute(url, messageSendTextServiceRequest, WechatWorkMessageSendServiceResponse.class);
				if (wechatWorkMessageSendServiceResponse != null) {
					if (WechatWorkServcieResponse.OK.equals(wechatWorkMessageSendServiceResponse.getErrcode())) {
						logger.info("wechat work message ok : {}",body);
						continue;
					}
				}
				logger.info("wechat work message fail : {}",body);
			}
		} catch (Exception e) {
			logger.error(e.getMessage(),e);
		}
		return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
	}

	/**
	 * 通过id查找 获取access_token
	 * @param id
	 * @return
	 */
	public WechatWorkApp findById(Integer id) {
		WechatWorkApp wechatWorkApp = this.wechatWorkAppDao.selectById(id);
		Date datetime = DateUtil.getServerTime();
		if (wechatWorkApp != null) {
			// 未过期,直接返回
			if (!this.isEmpty(wechatWorkApp.getAccessToken())
					&& !this.isEmpty(wechatWorkApp.getAccessTokenDatetime())
					&& datetime.before(DateUtil.getFutureMinute(wechatWorkApp.getAccessTokenDatetime(), 90))
			) {
				return wechatWorkApp;
			}
			// 刷新
			try {
				WechatWorkServiceClient<WechatWorkGetTokenServcieResponse> wechatWorkServiceClient = new WechatWorkServiceClient<WechatWorkGetTokenServcieResponse>();
				wechatWorkServiceClient.setMethod(HttpMethod.GET);
				String url = String.format("/gettoken?corpId=%s&corpsecret=%s", wechatWorkApp.getCorpid(), wechatWorkApp.getSecret());
				WechatWorkGetTokenServcieResponse wechatWorkGetTokenServcieResponse = wechatWorkServiceClient.execute(url, null, WechatWorkGetTokenServcieResponse.class);
				if (wechatWorkGetTokenServcieResponse != null) {
					if (WechatWorkServcieResponse.OK.equals(wechatWorkGetTokenServcieResponse.getErrcode())) {
						logger.info("wechat work access token : {}", wechatWorkGetTokenServcieResponse.getAccessToken());
						// 更新到数据库
						WechatWorkApp wechatWorkAppModify = new WechatWorkApp();
						wechatWorkAppModify.setId(wechatWorkApp.getId());
						wechatWorkAppModify.setAccessToken(wechatWorkGetTokenServcieResponse.getAccessToken());
						wechatWorkAppModify.setAccessTokenDatetime(datetime);
						this.wechatWorkAppDao.updateById(wechatWorkAppModify);
						// 返回数据
						wechatWorkApp.setAccessToken(wechatWorkGetTokenServcieResponse.getAccessToken());
						wechatWorkApp.setModifyTime(datetime);
					}
				}
			} catch (Exception e) {
				logger.error(e.getMessage(),e);
				throw new RuntimeException(e);
			}
		}
		return wechatWorkApp;
	}


	Object getObject(String str,Class<?> clazz){
		ObjectMapper objectMapper = new ObjectMapper();
		objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
		objectMapper.configure(DeserializationFeature.ACCEPT_SINGLE_VALUE_AS_ARRAY, true);
		try{
			return objectMapper.readValue(str, clazz);
		}catch (Exception e) {
			throw new RuntimeException(e);
		}
	}
	Boolean isEmpty(Object obj){
		if(obj != null){
			if(!"".equals(obj) && !"null".equals(obj) && !"undefined".equals(obj)){
				return false;
			}
		}
		return true;
	}
}
