package com.data.service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.common.util.DateUtil;
import com.data.domain.Log;

@Service
public class LogServiceImpl implements LogService{
	protected final String DATE_FORMAT = "yyyy-MM-dd";


	@Autowired
	private com.data.dao.backup.LogDao backupLogDao;

	@Autowired
	private com.data.dao.backup.Log202111Dao backupLog202111Dao;
	@Autowired
	private com.data.dao.backup.Log202112Dao backupLog202112Dao;
	@Autowired
	private com.data.dao.backup.Log202201Dao backupLog202201Dao;
	@Autowired
	private com.data.dao.backup.Log202202Dao backupLog202202Dao;
	@Autowired
	private com.data.dao.backup.Log202203Dao backupLog202203Dao;
	@Autowired
	private com.data.dao.backup.Log202204Dao backupLog202204Dao;
	@Autowired
	private com.data.dao.backup.Log202205Dao backupLog202205Dao;
	@Autowired
	private com.data.dao.backup.Log202206Dao backupLog202206Dao;
	@Autowired
	private com.data.dao.backup.Log202207Dao backupLog202207Dao;
	@Autowired
	private com.data.dao.backup.Log202208Dao backupLog202208Dao;
	@Autowired
	private com.data.dao.backup.Log202209Dao backupLog202209Dao;
	@Autowired
	private com.data.dao.backup.Log202210Dao backupLog202210Dao;
	@Autowired
	private com.data.dao.backup.Log202211Dao backupLog202211Dao;
	@Autowired
	private com.data.dao.backup.Log202212Dao backupLog202212Dao;
	@Autowired
	private com.data.dao.backup.Log202301Dao backupLog202301Dao;
	@Autowired
	private com.data.dao.backup.Log202302Dao backupLog202302Dao;
	@Autowired
	private com.data.dao.backup.Log202303Dao backupLog202303Dao;
	@Autowired
	private com.data.dao.backup.Log202304Dao backupLog202304Dao;
	@Autowired
	private com.data.dao.backup.Log202305Dao backupLog202305Dao;
	@Autowired
	private com.data.dao.backup.Log202306Dao backupLog202306Dao;
	@Autowired
	private com.data.dao.backup.Log202307Dao backupLog202307Dao;
	@Autowired
	private com.data.dao.backup.Log202308Dao backupLog202308Dao;
	@Autowired
	private com.data.dao.backup.Log202309Dao backupLog202309Dao;
	@Autowired
	private com.data.dao.backup.Log202310Dao backupLog202310Dao;
	@Autowired
	private com.data.dao.backup.Log202311Dao backupLog202311Dao;
	@Autowired
	private com.data.dao.backup.Log202312Dao backupLog202312Dao;
	@Autowired
	private com.data.dao.backup.Log202401Dao backupLog202401Dao;
	@Autowired
	private com.data.dao.backup.Log202402Dao backupLog202402Dao;
	@Autowired
	private com.data.dao.backup.Log202403Dao backupLog202403Dao;
	@Autowired
	private com.data.dao.backup.Log202404Dao backupLog202404Dao;
	@Autowired
	private com.data.dao.backup.Log202405Dao backupLog202405Dao;
	@Autowired
	private com.data.dao.backup.Log202406Dao backupLog202406Dao;

	@Override
	@Transactional(value = "backupTransactionManager")
	public void backupByMonth(Log log) {
		Date date_2021_12 = DateUtil.parse("2021-12-01", DATE_FORMAT);
		Date date_2022_01 = DateUtil.parse("2022-01-01", DATE_FORMAT);
		Date date_2022_02 = DateUtil.parse("2022-02-01", DATE_FORMAT);
		Date date_2022_03 = DateUtil.parse("2022-03-01", DATE_FORMAT);
		Date date_2022_04 = DateUtil.parse("2022-04-01", DATE_FORMAT);
		Date date_2022_05 = DateUtil.parse("2022-05-01", DATE_FORMAT);
		Date date_2022_06 = DateUtil.parse("2022-06-01", DATE_FORMAT);
		Date date_2022_07 = DateUtil.parse("2022-07-01", DATE_FORMAT);
		Date date_2022_08 = DateUtil.parse("2022-08-01", DATE_FORMAT);
		Date date_2022_09 = DateUtil.parse("2022-09-01", DATE_FORMAT);
		Date date_2022_10 = DateUtil.parse("2022-10-01", DATE_FORMAT);
		Date date_2022_11 = DateUtil.parse("2022-11-01", DATE_FORMAT);
		Date date_2022_12 = DateUtil.parse("2022-12-01", DATE_FORMAT);
		Date date_2023_01 = DateUtil.parse("2023-01-01", DATE_FORMAT);
		Date date_2023_02 = DateUtil.parse("2023-02-01", DATE_FORMAT);
		Date date_2023_03 = DateUtil.parse("2023-03-01", DATE_FORMAT);
		Date date_2023_04 = DateUtil.parse("2023-04-01", DATE_FORMAT);
		Date date_2023_05 = DateUtil.parse("2023-05-01", DATE_FORMAT);
		Date date_2023_06 = DateUtil.parse("2023-06-01", DATE_FORMAT);
		Date date_2023_07 = DateUtil.parse("2023-07-01", DATE_FORMAT);
		Date date_2023_08 = DateUtil.parse("2023-08-01", DATE_FORMAT);
		Date date_2023_09 = DateUtil.parse("2023-09-01", DATE_FORMAT);
		Date date_2023_10 = DateUtil.parse("2023-10-01", DATE_FORMAT);
		Date date_2023_11 = DateUtil.parse("2023-11-01", DATE_FORMAT);
		Date date_2023_12 = DateUtil.parse("2023-12-01", DATE_FORMAT);
		Date date_2024_01 = DateUtil.parse("2024-01-01", DATE_FORMAT);
		Date date_2024_02 = DateUtil.parse("2024-02-01", DATE_FORMAT);
		Date date_2024_03 = DateUtil.parse("2024-03-01", DATE_FORMAT);
		Date date_2024_04 = DateUtil.parse("2024-04-01", DATE_FORMAT);
		Date date_2024_05 = DateUtil.parse("2024-05-01", DATE_FORMAT);
		Date date_2024_06 = DateUtil.parse("2024-06-01", DATE_FORMAT);
		Date date_2024_07 = DateUtil.parse("2024-07-01", DATE_FORMAT);

		if (log.getCreateTime().before(date_2021_12)){
			Log logEntity = backupLog202111Dao.selectById(log.getId());
			backupLogDao.deleteById(log.getId());
			if (logEntity != null){
				return;
			}
			backupLog202111Dao.insert(log);
		} else if (log.getCreateTime().before(date_2022_01)){
			Log logEntity = backupLog202112Dao.selectById(log.getId());
			backupLogDao.deleteById(log.getId());
			if (logEntity != null){
				return;
			}
			backupLog202112Dao.insert(log);
		} else if (log.getCreateTime().before(date_2022_02)){
			Log logEntity = backupLog202201Dao.selectById(log.getId());
			backupLogDao.deleteById(log.getId());
			if (logEntity != null){
				return;
			}
			backupLog202201Dao.insert(log);
		} else if (log.getCreateTime().before(date_2022_03)){
			Log logEntity = backupLog202202Dao.selectById(log.getId());
			backupLogDao.deleteById(log.getId());
			if (logEntity != null){
				return;
			}
			backupLog202202Dao.insert(log);
		} else if (log.getCreateTime().before(date_2022_04)){
			Log logEntity = backupLog202203Dao.selectById(log.getId());
			backupLogDao.deleteById(log.getId());
			if (logEntity != null){
				return;
			}
			backupLog202203Dao.insert(log);
		} else if (log.getCreateTime().before(date_2022_05)){
			Log logEntity = backupLog202204Dao.selectById(log.getId());
			backupLogDao.deleteById(log.getId());
			if (logEntity != null){
				return;
			}
			backupLog202204Dao.insert(log);
		} else if (log.getCreateTime().before(date_2022_06)){
			Log logEntity = backupLog202205Dao.selectById(log.getId());
			backupLogDao.deleteById(log.getId());
			if (logEntity != null){
				return;
			}
			backupLog202205Dao.insert(log);
		} else if (log.getCreateTime().before(date_2022_07)){
			Log logEntity = backupLog202206Dao.selectById(log.getId());
			backupLogDao.deleteById(log.getId());
			if (logEntity != null){
				return;
			}
			backupLog202206Dao.insert(log);
		} else if (log.getCreateTime().before(date_2022_08)){
			Log logEntity = backupLog202207Dao.selectById(log.getId());
			backupLogDao.deleteById(log.getId());
			if (logEntity != null){
				return;
			}
			backupLog202207Dao.insert(log);
		} else if (log.getCreateTime().before(date_2022_09)){
			Log logEntity = backupLog202208Dao.selectById(log.getId());
			backupLogDao.deleteById(log.getId());
			if (logEntity != null){
				return;
			}
			backupLog202208Dao.insert(log);
		} else if (log.getCreateTime().before(date_2022_10)){
			Log logEntity = backupLog202209Dao.selectById(log.getId());
			backupLogDao.deleteById(log.getId());
			if (logEntity != null){
				return;
			}
			backupLog202209Dao.insert(log);
		} else if (log.getCreateTime().before(date_2022_11)){
			Log logEntity = backupLog202210Dao.selectById(log.getId());
			backupLogDao.deleteById(log.getId());
			if (logEntity != null){
				return;
			}
			backupLog202210Dao.insert(log);
		} else if (log.getCreateTime().before(date_2022_12)){
			Log logEntity = backupLog202211Dao.selectById(log.getId());
			backupLogDao.deleteById(log.getId());
			if (logEntity != null){
				return;
			}
			backupLog202211Dao.insert(log);
		} else if (log.getCreateTime().before(date_2023_01)){
			Log logEntity = backupLog202212Dao.selectById(log.getId());
			backupLogDao.deleteById(log.getId());
			if (logEntity != null){
				return;
			}
			backupLog202212Dao.insert(log);
		} else if (log.getCreateTime().before(date_2023_02)){
			Log logEntity = backupLog202301Dao.selectById(log.getId());
			backupLogDao.deleteById(log.getId());
			if (logEntity != null){
				return;
			}
			backupLog202301Dao.insert(log);
		} else if (log.getCreateTime().before(date_2023_03)){
			Log logEntity = backupLog202302Dao.selectById(log.getId());
			backupLogDao.deleteById(log.getId());
			if (logEntity != null){
				return;
			}
			backupLog202302Dao.insert(log);
		} else if (log.getCreateTime().before(date_2023_04)){
			Log logEntity = backupLog202303Dao.selectById(log.getId());
			backupLogDao.deleteById(log.getId());
			if (logEntity != null){
				return;
			}
			backupLog202303Dao.insert(log);
		} else if (log.getCreateTime().before(date_2023_05)){
			Log logEntity = backupLog202304Dao.selectById(log.getId());
			backupLogDao.deleteById(log.getId());
			if (logEntity != null){
				return;
			}
			backupLog202304Dao.insert(log);
		} else if (log.getCreateTime().before(date_2023_06)){
			Log logEntity = backupLog202305Dao.selectById(log.getId());
			backupLogDao.deleteById(log.getId());
			if (logEntity != null){
				return;
			}
			backupLog202305Dao.insert(log);
		} else if (log.getCreateTime().before(date_2023_07)){
			Log logEntity = backupLog202306Dao.selectById(log.getId());
			backupLogDao.deleteById(log.getId());
			if (logEntity != null){
				return;
			}
			backupLog202306Dao.insert(log);
		} else if (log.getCreateTime().before(date_2023_08)){
			Log logEntity = backupLog202307Dao.selectById(log.getId());
			backupLogDao.deleteById(log.getId());
			if (logEntity != null){
				return;
			}
			backupLog202307Dao.insert(log);
		} else if (log.getCreateTime().before(date_2023_09)){
			Log logEntity = backupLog202308Dao.selectById(log.getId());
			backupLogDao.deleteById(log.getId());
			if (logEntity != null){
				return;
			}
			backupLog202308Dao.insert(log);
		} else if (log.getCreateTime().before(date_2023_10)){
			Log logEntity = backupLog202309Dao.selectById(log.getId());
			backupLogDao.deleteById(log.getId());
			if (logEntity != null){
				return;
			}
			backupLog202309Dao.insert(log);
		} else if (log.getCreateTime().before(date_2023_11)){
			Log logEntity = backupLog202310Dao.selectById(log.getId());
			backupLogDao.deleteById(log.getId());
			if (logEntity != null){
				return;
			}
			backupLog202310Dao.insert(log);
		} else if (log.getCreateTime().before(date_2023_12)){
			Log logEntity = backupLog202311Dao.selectById(log.getId());
			backupLogDao.deleteById(log.getId());
			if (logEntity != null){
				return;
			}
			backupLog202311Dao.insert(log);
		} else if (log.getCreateTime().before(date_2024_01)){
			Log logEntity = backupLog202312Dao.selectById(log.getId());
			backupLogDao.deleteById(log.getId());
			if (logEntity != null){
				return;
			}
			backupLog202312Dao.insert(log);
		} else if (log.getCreateTime().before(date_2024_02)){
			Log logEntity = backupLog202401Dao.selectById(log.getId());
			backupLogDao.deleteById(log.getId());
			if (logEntity != null){
				return;
			}
			backupLog202401Dao.insert(log);
		} else if (log.getCreateTime().before(date_2024_03)){
			Log logEntity = backupLog202402Dao.selectById(log.getId());
			backupLogDao.deleteById(log.getId());
			if (logEntity != null){
				return;
			}
			backupLog202402Dao.insert(log);
		} else if (log.getCreateTime().before(date_2024_04)){
			Log logEntity = backupLog202304Dao.selectById(log.getId());
			backupLogDao.deleteById(log.getId());
			if (logEntity != null){
				return;
			}
			backupLog202403Dao.insert(log);
		} else if (log.getCreateTime().before(date_2024_05)){
			Log logEntity = backupLog202404Dao.selectById(log.getId());
			backupLogDao.deleteById(log.getId());
			if (logEntity != null){
				return;
			}
			backupLog202404Dao.insert(log);
		} else if (log.getCreateTime().before(date_2024_06)){
			Log logEntity = backupLog202405Dao.selectById(log.getId());
			backupLogDao.deleteById(log.getId());
			if (logEntity != null){
				return;
			}
			backupLog202405Dao.insert(log);
		} else if (log.getCreateTime().before(date_2024_07)){
			Log logEntity = backupLog202406Dao.selectById(log.getId());
			backupLogDao.deleteById(log.getId());
			if (logEntity != null){
				return;
			}
			backupLog202406Dao.insert(log);
		}
	}

	@Override
	@Transactional(value = "backupTransactionManager")
	public Integer backupByMonth(Long id) {
		Log log = backupLogDao.selectById(id);
		if (log == null){
			return -1;
		}
		String tableName = "tb_log_" + DateUtil.format(log.getCreateTime(), "yyyyMM");
		log.setTableName(tableName);
		backupLogDao.deleteById(log.getId());
		return backupLogDao.insertByTableName(log);
	}

	@Override
	@Transactional(value = "backupTransactionManager")
	public Integer backupByMonth(Long minId, Long maxId) {
		List<Log> logs = backupLogDao.selectByMinIdAndMaxId(minId,maxId);
		if (logs == null || logs.isEmpty()){
			return -1;
		}
		Log minLog = logs.get(0);
		Log maxLog = logs.get(logs.size() - 1);
		String minTableName = "tb_log_" + DateUtil.format(minLog.getCreateTime(), "yyyyMM");
		String maxTableName = "tb_log_" + DateUtil.format(maxLog.getCreateTime(), "yyyyMM");
		List<Long> ids = new ArrayList<>();
		if (minTableName.equals(maxTableName)){
			// 相同，说明是同一个月的数据，直接批量入库即可
			backupLogDao.insertBatchByTableName(logs,minTableName);
			for (Log log : logs) {
				ids.add(log.getId());
			}
		}else {
			for (Log log : logs) {
				String tableName = "tb_log_" + DateUtil.format(log.getCreateTime(), "yyyyMM");
				log.setTableName(tableName);
				backupLogDao.insertByTableName(log);
				ids.add(log.getId());
			}
		}
		return backupLogDao.deleteByIds(ids);
	}

}
