package com.data.service;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;

import com.common.bean.FeiGeSendResponse;
import com.common.bean.SMSPayload;
import com.common.client.FeigeSmsClient;
import com.common.client.FeigeSmsRequest;
import com.common.constant.App;
import com.common.constant.CacheKey;
import com.common.constant.DataStatus;
import com.common.constant.SMSType;
import com.common.constant.UserMessageType;
import com.common.util.DateUtil;
import com.common.util.StringUtil;
import com.data.dao.primary.CustomerDao;
import com.data.dao.primary.UserDao;
import com.data.dao.primary.UserMessageDao;
import com.data.domain.Customer;
import com.data.domain.User;
import com.data.domain.UserMessage;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;


//@Service
public class SMSListener implements MessageListenerConcurrently {
	private Logger logger = LoggerFactory.getLogger(this.getClass());
	@Autowired
	UserMessageDao userMessageDao;
	@Autowired
	UserDao userDao;
	@Autowired
	CustomerDao customerDao;
	@Autowired
	private RedisTemplate<String, String> redisTemplate;

	public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs, ConsumeConcurrentlyContext context) {
		try {
			for (MessageExt msg : msgs) {
				String body = new String(msg.getBody(),StandardCharsets.UTF_8);
				logger.info("lxfk data consume sms message : {}", body);
				SMSPayload smsPayload = (SMSPayload)getObject(body, SMSPayload.class);
				//类型 0:验证码 1:通知  2:营销
				switch (SMSType.get(smsPayload.getType())){
					case C2:
						//发送推广短信
						List<SMSPayload> smsPayloads = batchCacheMessage(smsPayload, App.FEIGE_PROMOTION_CACHE_UPPER_LIMIT);
						if (smsPayloads != null){
							for (SMSPayload sp : smsPayloads){
							customFeiGeSmsSend(sp);
							}
						}
						break;
					case C1:
						//发送成单短信
						templateFeiGeSmsOrderSend(smsPayload);
						break;
					case C0:break;
				}
			}
		} catch (Exception e) {
			logger.error(e.getMessage(),e);
		}
		return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
	}

	/**
	 * 使用飞鸽平台发送成单短信
	 * <AUTHOR> 2021-04-12
	 * @param smsPayload
	 */
	public void templateFeiGeSmsOrderSend(SMSPayload smsPayload){
		Date datetime = DateUtil.getServerTime();
		//组装入参
		FeigeSmsRequest request = new FeigeSmsRequest();
		request.setMobile(smsPayload.getMobile());
		request.setTemplateId(smsPayload.getTemplateId());
		// 填充模板里面的内容
		if (smsPayload.getDatas() != null && !smsPayload.getDatas().isEmpty()){
			String content = StringUtil.join(smsPayload.getDatas(), "||");
			request.setContent(content);
		}
		request.setCompany(smsPayload.getCompany());
		//初始化发送端发送
		FeigeSmsClient feigeSmsClient = new FeigeSmsClient();
		FeiGeSendResponse response = feigeSmsClient.templateSmsSend(request);
		// 发送成功 再进行记录
		try{
			if (0 == response.getCode()) {
				Customer customer = this.customerDao.selectByUsername(smsPayload.getMobile());
				List<User> users = userDao.selectByCustomerId(customer.getId());
				if (!users.isEmpty()) {
					UserMessage userMessage = new UserMessage();
					userMessage.setUserId(users.get(0).getId().intValue());
					userMessage.setStatus(DataStatus.Y.getCode());
					userMessage.setType(UserMessageType.SMS.getCode());
					userMessage.setContent(smsPayload.getContent());
					userMessage.setCreateTime(datetime);
					userMessage.setModifyTime(datetime);
					userMessageDao.insert(userMessage);
				}
			}
		}catch (Exception e){
			logger.error("order sms error message {},{}",e.getMessage(),e);
		}
	}

	/**
	 * <p>修改成单次发送 2021/4/21 张永贺</p>
	 * 使用飞鸽平台发送推广短信
	 * <AUTHOR>
	 * @param smsPayload
	 */
	public void customFeiGeSmsSend(SMSPayload smsPayload){
		Date datetime = DateUtil.getServerTime();
		//设置发送参数
		FeigeSmsRequest request = new FeigeSmsRequest();
		//设置接收方手机号
		request.setMobile(smsPayload.getMobile());
		//设置发送内容
		request.setContent(smsPayload.getContent());

		//初始化发送端
		FeigeSmsClient client = new FeigeSmsClient();
		//返回结果是否发送成功
		FeiGeSendResponse response = client.customSendSms(request);
		//获取手机号的用户
		Customer customer = this.customerDao.selectByUsername(smsPayload.getMobile());
		User user = userDao.selectById(customer.getId().intValue());
		//发送成功记录
		try {
			if (0 == response.getCode()) {
				UserMessage userMessage = new UserMessage();
				userMessage.setUserId(user.getId().intValue());
				userMessage.setStatus(DataStatus.Y.getCode());
				userMessage.setType(UserMessageType.SMS.getCode());
				userMessage.setContent(request.getContent());
				userMessage.setCreateTime(datetime);
				userMessage.setModifyTime(datetime);
				userMessageDao.insert(userMessage);
			}
		}catch (Exception e){
			logger.error("Promotion sms error message {},{}",e.getMessage(),e);
		}
	}

	/**
	 * 每一个进来的请求都压入缓存，每二十条发送一次
	 * <p>修改，把每一个进来的请求实体json压入队列 张永贺 2021/4/20</p>
	 * <AUTHOR>
	 * @param smsPayload
	 * @param lengthOfBatch 批次长度
	 * @return
	 */
	public List<SMSPayload> batchCacheMessage(SMSPayload smsPayload, long lengthOfBatch){
		List<SMSPayload> smsPayloads = null;
		String cacheKey = CacheKey.FEIGE_SMS_PROMOTION_MESSAGE_SEND;
		//判断消息缓存是否已满
		if (redisTemplate.opsForList().size(cacheKey) == lengthOfBatch - 1){
			smsPayloads = new ArrayList<>();
			for (int i = 0;i < lengthOfBatch - 1;i++){
				smsPayloads.add((SMSPayload) getObject(redisTemplate.opsForList().rightPop(cacheKey), SMSPayload.class));
			}
			smsPayloads.add(smsPayload);
			return smsPayloads;
		}else{
			redisTemplate.opsForList().leftPush(cacheKey, this.getJSON(smsPayload));
		}
		return null;
	}

	Object getObject(String str,Class<?> clazz){
		ObjectMapper objectMapper = new ObjectMapper();
		objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
		objectMapper.configure(DeserializationFeature.ACCEPT_SINGLE_VALUE_AS_ARRAY, true);
		try{
			return objectMapper.readValue(str, clazz);
		}catch (Exception e) {
			throw new RuntimeException(e);
		}
	}

	String getJSON(Object obj) {
		ObjectMapper objectMapper = new ObjectMapper();
		try {
			return objectMapper.writeValueAsString(obj);
		} catch (Exception e) {
			throw new RuntimeException(e);
		}
	}
}
