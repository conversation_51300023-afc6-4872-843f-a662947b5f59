package com.data.service;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.common.bean.FeiGeSendResponse;
import com.common.bean.SMSPayload;
import com.common.client.FeigeSmsClient;
import com.common.client.FeigeSmsRequest;
import com.common.client.YunTongXunRestClient;
import com.common.client.YunTongXunSmsRequest;
import com.common.constant.App;
import com.common.constant.DataStatus;
import com.common.constant.SMSType;
import com.common.util.DateUtil;
import com.data.dao.primary.SmsDao;
import com.data.domain.Sms;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;

@Service
public class SMSCaptchaListener implements MessageListenerConcurrently {
	private Logger logger = LoggerFactory.getLogger(this.getClass());
	private final int USER_SMS_CAPTCHA_EXPIRE_TIME = App.USER_SMS_CAPTCHA_EXPIRE_TIME;

	@Autowired private SmsDao smsDao;

	public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs, ConsumeConcurrentlyContext context) {
		try {
			Date datetime = DateUtil.getServerTime();
			for (MessageExt msg : msgs) {
				String body = new String(msg.getBody(),StandardCharsets.UTF_8);
				logger.info("lxfk data consume sms captcha message : {}", body);
				SMSPayload smsPayload = (SMSPayload)getObject(body, SMSPayload.class);
				// 计算时间差
				long minus = datetime.getTime() - smsPayload.getCreateTime().getTime();
				if (minus < USER_SMS_CAPTCHA_EXPIRE_TIME * 1000L && SMSType.C0.getCode().equals(smsPayload.getType())){
					// 如果没有超过过期时间， 是短信验证码 发送
					//组装入参
					FeigeSmsRequest request = new FeigeSmsRequest();
					request.setMobile(smsPayload.getMobile());
					request.setTemplateId(App.FEIGE_CAPTCHA_TEMPLATE_ID);
					request.setContent(smsPayload.getContent());
					request.setCompany(smsPayload.getCompany());
					//初始化发送端发送
					FeigeSmsClient feigeSmsClient = new FeigeSmsClient();
					FeiGeSendResponse response = feigeSmsClient.templateSmsSend(request);
					// 发送成功 再进行记录
					if (0 == response.getCode()) {
						smsDao.insert(new Sms(smsPayload.getMobile(),this.getJSON(response),DataStatus.Y.getCode(),datetime,datetime));
					}
				}
			}
		} catch (Exception e) {
			logger.error(e.getMessage(),e);
		}
		return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
	}


	Object getObject(String str,Class<?> clazz){
		ObjectMapper objectMapper = new ObjectMapper();
		try{
			return objectMapper.readValue(str, clazz);
		}catch (Exception e) {
			throw new RuntimeException(e);
		}
	}

	/**
	 * 使用云通讯发送短信验证码
	 */
	public void templateYunTongXunSmsCaotchaSend(SMSPayload smsPayload){
		//发送短信验证码到手机
		YunTongXunSmsRequest yunTongXunSmsRequest = new YunTongXunSmsRequest();
		yunTongXunSmsRequest.setTo(smsPayload.getMobile());
		yunTongXunSmsRequest.setDatas(new ArrayList<String>());
		yunTongXunSmsRequest.getDatas().add(smsPayload.getContent());
		yunTongXunSmsRequest.getDatas().add("两分钟");
		if(App.PRODUCTION){
			YunTongXunRestClient xunRestClient = new YunTongXunRestClient();
			String sendResponse = xunRestClient.send(yunTongXunSmsRequest);
			//短信入库
			smsDao.insert(new Sms(smsPayload.getMobile(),sendResponse,DataStatus.Y.getCode(),new Date(),new Date()));
		}
	}

	String getJSON(Object obj){
		ObjectMapper objectMapper = new ObjectMapper();
		objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
		objectMapper.configure(DeserializationFeature.ACCEPT_SINGLE_VALUE_AS_ARRAY, true);
		try{
			return objectMapper.writeValueAsString(obj);
		}catch (Exception e) {
			throw new RuntimeException(e);
		}
	}

}
