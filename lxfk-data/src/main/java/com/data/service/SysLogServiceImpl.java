package com.data.service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.common.util.DateUtil;
import com.data.domain.SysLog;

@Service
public class SysLogServiceImpl implements SysLogService{
	protected final String DATE_FORMAT = "yyyy-MM-dd";

	@Autowired
	private com.data.dao.backup.SysLogDao backupSysLogDao;

	@Autowired
	private com.data.dao.backup.SysLog202111Dao backupSysLog202111Dao;
	@Autowired
	private com.data.dao.backup.SysLog202112Dao backupSysLog202112Dao;
	@Autowired
	private com.data.dao.backup.SysLog202201Dao backupSysLog202201Dao;
	@Autowired
	private com.data.dao.backup.SysLog202202Dao backupSysLog202202Dao;
	@Autowired
	private com.data.dao.backup.SysLog202203Dao backupSysLog202203Dao;
	@Autowired
	private com.data.dao.backup.SysLog202204Dao backupSysLog202204Dao;
	@Autowired
	private com.data.dao.backup.SysLog202205Dao backupSysLog202205Dao;
	@Autowired
	private com.data.dao.backup.SysLog202206Dao backupSysLog202206Dao;
	@Autowired
	private com.data.dao.backup.SysLog202207Dao backupSysLog202207Dao;
	@Autowired
	private com.data.dao.backup.SysLog202208Dao backupSysLog202208Dao;
	@Autowired
	private com.data.dao.backup.SysLog202209Dao backupSysLog202209Dao;
	@Autowired
	private com.data.dao.backup.SysLog202210Dao backupSysLog202210Dao;
	@Autowired
	private com.data.dao.backup.SysLog202211Dao backupSysLog202211Dao;
	@Autowired
	private com.data.dao.backup.SysLog202212Dao backupSysLog202212Dao;
	@Autowired
	private com.data.dao.backup.SysLog202301Dao backupSysLog202301Dao;
	@Autowired
	private com.data.dao.backup.SysLog202302Dao backupSysLog202302Dao;
	@Autowired
	private com.data.dao.backup.SysLog202303Dao backupSysLog202303Dao;
	@Autowired
	private com.data.dao.backup.SysLog202304Dao backupSysLog202304Dao;
	@Autowired
	private com.data.dao.backup.SysLog202305Dao backupSysLog202305Dao;
	@Autowired
	private com.data.dao.backup.SysLog202306Dao backupSysLog202306Dao;
	@Autowired
	private com.data.dao.backup.SysLog202307Dao backupSysLog202307Dao;
	@Autowired
	private com.data.dao.backup.SysLog202308Dao backupSysLog202308Dao;
	@Autowired
	private com.data.dao.backup.SysLog202309Dao backupSysLog202309Dao;
	@Autowired
	private com.data.dao.backup.SysLog202310Dao backupSysLog202310Dao;
	@Autowired
	private com.data.dao.backup.SysLog202311Dao backupSysLog202311Dao;
	@Autowired
	private com.data.dao.backup.SysLog202312Dao backupSysLog202312Dao;
	@Autowired
	private com.data.dao.backup.SysLog202401Dao backupSysLog202401Dao;
	@Autowired
	private com.data.dao.backup.SysLog202402Dao backupSysLog202402Dao;
	@Autowired
	private com.data.dao.backup.SysLog202403Dao backupSysLog202403Dao;
	@Autowired
	private com.data.dao.backup.SysLog202404Dao backupSysLog202404Dao;
	@Autowired
	private com.data.dao.backup.SysLog202405Dao backupSysLog202405Dao;
	@Autowired
	private com.data.dao.backup.SysLog202406Dao backupSysLog202406Dao;

	@Override
	@Transactional(value = "backupTransactionManager")
	public void backupByMonth(SysLog sysLog) {
		Date date_2021_12 = DateUtil.parse("2021-12-01", DATE_FORMAT);
		Date date_2022_01 = DateUtil.parse("2022-01-01", DATE_FORMAT);
		Date date_2022_02 = DateUtil.parse("2022-02-01", DATE_FORMAT);
		Date date_2022_03 = DateUtil.parse("2022-03-01", DATE_FORMAT);
		Date date_2022_04 = DateUtil.parse("2022-04-01", DATE_FORMAT);
		Date date_2022_05 = DateUtil.parse("2022-05-01", DATE_FORMAT);
		Date date_2022_06 = DateUtil.parse("2022-06-01", DATE_FORMAT);
		Date date_2022_07 = DateUtil.parse("2022-07-01", DATE_FORMAT);
		Date date_2022_08 = DateUtil.parse("2022-08-01", DATE_FORMAT);
		Date date_2022_09 = DateUtil.parse("2022-09-01", DATE_FORMAT);
		Date date_2022_10 = DateUtil.parse("2022-10-01", DATE_FORMAT);
		Date date_2022_11 = DateUtil.parse("2022-11-01", DATE_FORMAT);
		Date date_2022_12 = DateUtil.parse("2022-12-01", DATE_FORMAT);
		Date date_2023_01 = DateUtil.parse("2023-01-01", DATE_FORMAT);
		Date date_2023_02 = DateUtil.parse("2023-02-01", DATE_FORMAT);
		Date date_2023_03 = DateUtil.parse("2023-03-01", DATE_FORMAT);
		Date date_2023_04 = DateUtil.parse("2023-04-01", DATE_FORMAT);
		Date date_2023_05 = DateUtil.parse("2023-05-01", DATE_FORMAT);
		Date date_2023_06 = DateUtil.parse("2023-06-01", DATE_FORMAT);
		Date date_2023_07 = DateUtil.parse("2023-07-01", DATE_FORMAT);
		Date date_2023_08 = DateUtil.parse("2023-08-01", DATE_FORMAT);
		Date date_2023_09 = DateUtil.parse("2023-09-01", DATE_FORMAT);
		Date date_2023_10 = DateUtil.parse("2023-10-01", DATE_FORMAT);
		Date date_2023_11 = DateUtil.parse("2023-11-01", DATE_FORMAT);
		Date date_2023_12 = DateUtil.parse("2023-12-01", DATE_FORMAT);
		Date date_2024_01 = DateUtil.parse("2024-01-01", DATE_FORMAT);
		Date date_2024_02 = DateUtil.parse("2024-02-01", DATE_FORMAT);
		Date date_2024_03 = DateUtil.parse("2024-03-01", DATE_FORMAT);
		Date date_2024_04 = DateUtil.parse("2024-04-01", DATE_FORMAT);
		Date date_2024_05 = DateUtil.parse("2024-05-01", DATE_FORMAT);
		Date date_2024_06 = DateUtil.parse("2024-06-01", DATE_FORMAT);
		Date date_2024_07 = DateUtil.parse("2024-07-01", DATE_FORMAT);

		if (sysLog.getCreateTime().before(date_2021_12)){
			SysLog sysLogEntity = backupSysLog202111Dao.selectById(sysLog.getId());
			backupSysLogDao.deleteById(sysLog.getId());
			if (sysLogEntity != null){
				return;
			}
			backupSysLog202111Dao.insert(sysLog);
		}else if (sysLog.getCreateTime().before(date_2022_01)){
			SysLog sysLogEntity = backupSysLog202112Dao.selectById(sysLog.getId());
			backupSysLogDao.deleteById(sysLog.getId());
			if (sysLogEntity != null){
				return;
			}
			backupSysLog202112Dao.insert(sysLog);
		}else if (sysLog.getCreateTime().before(date_2022_02)){
			SysLog sysLogEntity = backupSysLog202201Dao.selectById(sysLog.getId());
			backupSysLogDao.deleteById(sysLog.getId());
			if (sysLogEntity != null){
				return;
			}
			backupSysLog202201Dao.insert(sysLog);
		}else if (sysLog.getCreateTime().before(date_2022_03)){
			SysLog sysLogEntity = backupSysLog202202Dao.selectById(sysLog.getId());
			backupSysLogDao.deleteById(sysLog.getId());
			if (sysLogEntity != null){
				return;
			}
			backupSysLog202202Dao.insert(sysLog);
		}else if (sysLog.getCreateTime().before(date_2022_04)){
			SysLog sysLogEntity = backupSysLog202203Dao.selectById(sysLog.getId());
			backupSysLogDao.deleteById(sysLog.getId());
			if (sysLogEntity != null){
				return;
			}
			backupSysLog202203Dao.insert(sysLog);
		}else if (sysLog.getCreateTime().before(date_2022_05)){
			SysLog sysLogEntity = backupSysLog202204Dao.selectById(sysLog.getId());
			backupSysLogDao.deleteById(sysLog.getId());
			if (sysLogEntity != null){
				return;
			}
			backupSysLog202204Dao.insert(sysLog);
		}else if (sysLog.getCreateTime().before(date_2022_06)){
			SysLog sysLogEntity = backupSysLog202205Dao.selectById(sysLog.getId());
			backupSysLogDao.deleteById(sysLog.getId());
			if (sysLogEntity != null){
				return;
			}
			backupSysLog202205Dao.insert(sysLog);
		}else if (sysLog.getCreateTime().before(date_2022_07)){
			SysLog sysLogEntity = backupSysLog202206Dao.selectById(sysLog.getId());
			backupSysLogDao.deleteById(sysLog.getId());
			if (sysLogEntity != null){
				return;
			}
			backupSysLog202206Dao.insert(sysLog);
		}else if (sysLog.getCreateTime().before(date_2022_08)){
			SysLog sysLogEntity = backupSysLog202207Dao.selectById(sysLog.getId());
			backupSysLogDao.deleteById(sysLog.getId());
			if (sysLogEntity != null){
				return;
			}
			backupSysLog202207Dao.insert(sysLog);
		}else if (sysLog.getCreateTime().before(date_2022_09)){
			SysLog sysLogEntity = backupSysLog202208Dao.selectById(sysLog.getId());
			backupSysLogDao.deleteById(sysLog.getId());
			if (sysLogEntity != null){
				return;
			}
			backupSysLog202208Dao.insert(sysLog);
		}else if (sysLog.getCreateTime().before(date_2022_10)){
			SysLog sysLogEntity = backupSysLog202209Dao.selectById(sysLog.getId());
			backupSysLogDao.deleteById(sysLog.getId());
			if (sysLogEntity != null){
				return;
			}
			backupSysLog202209Dao.insert(sysLog);
		}else if (sysLog.getCreateTime().before(date_2022_11)){
			SysLog sysLogEntity = backupSysLog202210Dao.selectById(sysLog.getId());
			backupSysLogDao.deleteById(sysLog.getId());
			if (sysLogEntity != null){
				return;
			}
			backupSysLog202210Dao.insert(sysLog);
		}else if (sysLog.getCreateTime().before(date_2022_12)){
			SysLog sysLogEntity = backupSysLog202211Dao.selectById(sysLog.getId());
			backupSysLogDao.deleteById(sysLog.getId());
			if (sysLogEntity != null){
				return;
			}
			backupSysLog202211Dao.insert(sysLog);
		}else if (sysLog.getCreateTime().before(date_2023_01)){
			SysLog sysLogEntity = backupSysLog202212Dao.selectById(sysLog.getId());
			backupSysLogDao.deleteById(sysLog.getId());
			if (sysLogEntity != null){
				return;
			}
			backupSysLog202212Dao.insert(sysLog);
		}else if (sysLog.getCreateTime().before(date_2023_02)){
			SysLog sysLogEntity = backupSysLog202301Dao.selectById(sysLog.getId());
			backupSysLogDao.deleteById(sysLog.getId());
			if (sysLogEntity != null){
				return;
			}
			backupSysLog202301Dao.insert(sysLog);
		}else if (sysLog.getCreateTime().before(date_2023_03)){
			SysLog sysLogEntity = backupSysLog202302Dao.selectById(sysLog.getId());
			backupSysLogDao.deleteById(sysLog.getId());
			if (sysLogEntity != null){
				return;
			}
			backupSysLog202302Dao.insert(sysLog);
		}else if (sysLog.getCreateTime().before(date_2023_04)){
			SysLog sysLogEntity = backupSysLog202303Dao.selectById(sysLog.getId());
			backupSysLogDao.deleteById(sysLog.getId());
			if (sysLogEntity != null){
				return;
			}
			backupSysLog202303Dao.insert(sysLog);
		}else if (sysLog.getCreateTime().before(date_2023_05)){
			SysLog sysLogEntity = backupSysLog202304Dao.selectById(sysLog.getId());
			backupSysLogDao.deleteById(sysLog.getId());
			if (sysLogEntity != null){
				return;
			}
			backupSysLog202304Dao.insert(sysLog);
		}else if (sysLog.getCreateTime().before(date_2023_06)){
			SysLog sysLogEntity = backupSysLog202305Dao.selectById(sysLog.getId());
			backupSysLogDao.deleteById(sysLog.getId());
			if (sysLogEntity != null){
				return;
			}
			backupSysLog202305Dao.insert(sysLog);
		}else if (sysLog.getCreateTime().before(date_2023_07)){
			SysLog sysLogEntity = backupSysLog202306Dao.selectById(sysLog.getId());
			backupSysLogDao.deleteById(sysLog.getId());
			if (sysLogEntity != null){
				return;
			}
			backupSysLog202306Dao.insert(sysLog);
		}else if (sysLog.getCreateTime().before(date_2023_08)){
			SysLog sysLogEntity = backupSysLog202307Dao.selectById(sysLog.getId());
			backupSysLogDao.deleteById(sysLog.getId());
			if (sysLogEntity != null){
				return;
			}
			backupSysLog202307Dao.insert(sysLog);
		}else if (sysLog.getCreateTime().before(date_2023_09)){
			SysLog sysLogEntity = backupSysLog202308Dao.selectById(sysLog.getId());
			backupSysLogDao.deleteById(sysLog.getId());
			if (sysLogEntity != null){
				return;
			}
			backupSysLog202308Dao.insert(sysLog);
		}else if (sysLog.getCreateTime().before(date_2023_10)){
			SysLog sysLogEntity = backupSysLog202309Dao.selectById(sysLog.getId());
			backupSysLogDao.deleteById(sysLog.getId());
			if (sysLogEntity != null){
				return;
			}
			backupSysLog202309Dao.insert(sysLog);
		}else if (sysLog.getCreateTime().before(date_2023_11)){
			SysLog sysLogEntity = backupSysLog202310Dao.selectById(sysLog.getId());
			backupSysLogDao.deleteById(sysLog.getId());
			if (sysLogEntity != null){
				return;
			}
			backupSysLog202310Dao.insert(sysLog);
		}else if (sysLog.getCreateTime().before(date_2023_12)){
			SysLog sysLogEntity = backupSysLog202311Dao.selectById(sysLog.getId());
			backupSysLogDao.deleteById(sysLog.getId());
			if (sysLogEntity != null){
				return;
			}
			backupSysLog202311Dao.insert(sysLog);
		}else if (sysLog.getCreateTime().before(date_2024_01)){
			SysLog sysLogEntity = backupSysLog202312Dao.selectById(sysLog.getId());
			backupSysLogDao.deleteById(sysLog.getId());
			if (sysLogEntity != null){
				return;
			}
			backupSysLog202312Dao.insert(sysLog);
		}else if (sysLog.getCreateTime().before(date_2024_02)){
			SysLog sysLogEntity = backupSysLog202401Dao.selectById(sysLog.getId());
			backupSysLogDao.deleteById(sysLog.getId());
			if (sysLogEntity != null){
				return;
			}
			backupSysLog202401Dao.insert(sysLog);
		}else if (sysLog.getCreateTime().before(date_2024_03)){
			SysLog sysLogEntity = backupSysLog202402Dao.selectById(sysLog.getId());
			backupSysLogDao.deleteById(sysLog.getId());
			if (sysLogEntity != null){
				return;
			}
			backupSysLog202402Dao.insert(sysLog);
		}else if (sysLog.getCreateTime().before(date_2024_04)){
			SysLog sysLogEntity = backupSysLog202403Dao.selectById(sysLog.getId());
			backupSysLogDao.deleteById(sysLog.getId());
			if (sysLogEntity != null){
				return;
			}
			backupSysLog202403Dao.insert(sysLog);
		}else if (sysLog.getCreateTime().before(date_2024_05)){
			SysLog sysLogEntity = backupSysLog202404Dao.selectById(sysLog.getId());
			backupSysLogDao.deleteById(sysLog.getId());
			if (sysLogEntity != null){
				return;
			}
			backupSysLog202404Dao.insert(sysLog);
		}else if (sysLog.getCreateTime().before(date_2024_06)){
			SysLog sysLogEntity = backupSysLog202405Dao.selectById(sysLog.getId());
			backupSysLogDao.deleteById(sysLog.getId());
			if (sysLogEntity != null){
				return;
			}
			backupSysLog202405Dao.insert(sysLog);
		}else if (sysLog.getCreateTime().before(date_2024_07)){
			SysLog sysLogEntity = backupSysLog202406Dao.selectById(sysLog.getId());
			backupSysLogDao.deleteById(sysLog.getId());
			if (sysLogEntity != null){
				return;
			}
			backupSysLog202406Dao.insert(sysLog);
		}
	}

	@Override
	@Transactional(value = "backupTransactionManager")
	public Integer backupByMonth(Long id) {
		SysLog sysLog = backupSysLogDao.selectById(id);
		if (sysLog == null){
			return -1;
		}
		String tableName = "tb_sys_log_" + DateUtil.format(sysLog.getCreateTime(), "yyyyMM");
		sysLog.setTableName(tableName);
		backupSysLogDao.deleteById(sysLog.getId());
		return backupSysLogDao.insertByTableName(sysLog);
	}

	@Override
	@Transactional(value = "backupTransactionManager")
	public Integer backupByMonth(Long minId, Long maxId) {
		List<SysLog> sysLogs = backupSysLogDao.selectByMinIdAndMaxId(minId,maxId);
		if (sysLogs == null || sysLogs.isEmpty()){
			return -1;
		}
		SysLog minSysLog = sysLogs.get(0);
		SysLog maxSysLog = sysLogs.get(sysLogs.size() - 1);
		String minTableName = "tb_sys_log_" + DateUtil.format(minSysLog.getCreateTime(), "yyyyMM");
		String maxTableName = "tb_sys_log_" + DateUtil.format(maxSysLog.getCreateTime(), "yyyyMM");
		List<Long> ids = new ArrayList<>();
		if (minTableName.equals(maxTableName)){
			// 相同，说明是同一个月的数据，直接批量入库即可
			backupSysLogDao.insertBatchByTableName(sysLogs,minTableName);
			for (SysLog sysLog : sysLogs) {
				ids.add(sysLog.getId());
			}
		}else {
			for (SysLog sysLog : sysLogs) {
				String tableName = "tb_sys_log_" + DateUtil.format(sysLog.getCreateTime(), "yyyyMM");
				sysLog.setTableName(tableName);
				backupSysLogDao.insertByTableName(sysLog);
				ids.add(sysLog.getId());
			}
		}
		return backupSysLogDao.deleteByIds(ids);
	}

}
