package com.data.service;

import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import com.common.constant.App;
import com.common.constant.CacheKey;
import com.common.constant.DataStatus;
import com.common.util.DateUtil;
import com.common.util.StringUtil;
import com.data.dao.backup.LogDao;
import com.data.domain.Log;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;

@Service
public class LogListener implements MessageListenerConcurrently {
	private Logger logger = LoggerFactory.getLogger(this.getClass());
	@Autowired private LogDao logDao;
	@Autowired private RedisTemplate<String, String> redisTemplate;

	/**
	 * ip访问次数限制
	 */
	private final Long IP_VISIT_COUNT_LIMIT = App.IP_VISIT_COUNT_LIMIT;

	public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs, ConsumeConcurrentlyContext context) {
		try {
			Date datetime = DateUtil.getServerTime();
			for (MessageExt msg : msgs) {
				String body = new String(msg.getBody(),StandardCharsets.UTF_8);
				logger.info("lxfk data consume log message : {}", body);
				com.common.bean.Log commonLog =(com.common.bean.Log) this.getObject(body, com.common.bean.Log.class);
				Log log = new Log();
				log.setPlatform(commonLog.getPlatform());
				log.setVersion(commonLog.getVersion());
				log.setType(commonLog.getType());
				log.setUserId(commonLog.getUserId());
				log.setIp(commonLog.getIp());
				log.setUrl(commonLog.getUrl());
				log.setRequestId(commonLog.getRequestId());
				Long startTime = commonLog.getStartTime();
				Long endTime = commonLog.getEndTime();
				if (startTime != null) {
					log.setStartTime(new Date(commonLog.getStartTime()));
				}
				if (endTime != null) {
					log.setEndTime(new Date(commonLog.getEndTime()));
				}
				if (startTime != null && endTime != null) {
					log.setDuration((int)(endTime - startTime));
				}
				log.setStatus(DataStatus.Y.getCode());
				log.setCreateTime(datetime);
				log.setModifyTime(datetime);
				logDao.insert(log);
				// 如果没有token 记录访问ip
				if (StringUtil.isEmpty(commonLog.getToken())){
					String key = CacheKey.IP_VISIT_COUNT + commonLog.getIp();
					if (redisTemplate.hasKey(key)){
						Long increment = redisTemplate.opsForValue().increment(key, 1);
						if (increment > IP_VISIT_COUNT_LIMIT){
							redisTemplate.opsForSet().add(CacheKey.IP_VISIT_LIMIT,commonLog.getIp());
						}
					}else {
						redisTemplate.opsForValue().set(key,"1",24,TimeUnit.HOURS);
					}
				}
			}
		} catch (Exception e) {
			logger.error(e.getMessage(),e);
		}
		return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
	}


	Object getObject(String str,Class<?> clazz){
		ObjectMapper objectMapper = new ObjectMapper();
		objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
		objectMapper.configure(DeserializationFeature.ACCEPT_SINGLE_VALUE_AS_ARRAY, true);
		try{
			return objectMapper.readValue(str, clazz);
		}catch (Exception e) {
			throw new RuntimeException(e);
		}
	}
}
