package com.task.component;


import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.common.constant.ClassBindingType;
import com.common.constant.DataStatus;
import com.common.constant.LivePlatformType;
import com.common.constant.LiveType;
import com.common.constant.OrderStage;
import com.common.constant.PublicStage;
import com.common.constant.PublicSwitch;
import com.common.constant.PublicYesNo;
import com.common.constant.UserDailyLearningPlanDayDetailTaskType;
import com.common.constant.UserDailyLearningPlanSeeLive;
import com.common.util.DateUtil;
import com.domain.Base;
import com.domain.CourseAuthor;
import com.domain.CourseCatalog;
import com.domain.CourseCatalogLive;
import com.domain.Live;
import com.domain.Order;
import com.domain.OrderProduct;
import com.domain.ProductCourse;
import com.domain.ProductCourseCatalog;
import com.domain.ProductCourseClass;
import com.domain.UserDailyLearningPlan;
import com.domain.UserDailyLearningPlanDay;
import com.domain.UserDailyLearningPlanDayDetail;
import com.domain.complex.CourseCatalogLiveQuery;
import com.domain.complex.CourseCatalogQuery;
import com.domain.complex.LiveQuery;
import com.domain.complex.OrderProductQuery;
import com.domain.complex.OrderQuery;
import com.domain.complex.ProductCourseCatalogQuery;
import com.domain.complex.ProductCourseClassQuery;
import com.domain.complex.UserDailyLearningPlanDayDetailQuery;
import com.domain.complex.UserDailyLearningPlanDayQuery;
import com.domain.complex.UserDailyLearningPlanQuery;
import com.service.CourseAuthorService;
import com.service.CourseCatalogLiveService;
import com.service.CourseCatalogService;
import com.service.CustomerService;
import com.service.LiveService;
import com.service.LiveTimeService;
import com.service.OrderProductService;
import com.service.OrderService;
import com.service.ProductCourseCatalogService;
import com.service.ProductCourseClassService;
import com.service.ProductCourseService;
import com.service.ProductLiveService;
import com.service.UserDailyLearningPlanDayDetailService;
import com.service.UserDailyLearningPlanDayService;
import com.service.UserDailyLearningPlanService;
import com.task.bean.UserDailyLearningPlanDayDetailLiveResponse;

//@Component
public class LivePlanDayTask extends BaseTask {

    @Autowired
    private LiveService liveService;
    @Autowired
    private ProductLiveService productLiveService;
    @Autowired
    private OrderProductService orderProductService;
    @Autowired
    private OrderService orderService;
    @Autowired
    private CustomerService customerService;
    @Autowired
    private LiveTimeService liveTimeService;
    @Autowired
    private CourseCatalogLiveService courseCatalogLiveService;
    @Autowired
    private ProductCourseCatalogService productCourseCatalogService;
    @Autowired
    private ProductCourseService productCourseService;
    @Autowired
    private CourseCatalogService courseCatalogService;
    @Autowired
    private ProductCourseClassService productCourseClassService;
    @Autowired
    private UserDailyLearningPlanService userDailyLearningPlanService;
    @Autowired
    private UserDailyLearningPlanDayService userDailyLearningPlanDayService;
    @Autowired
    private UserDailyLearningPlanDayDetailService userDailyLearningPlanDayDetailService;
    @Autowired
    private CourseAuthorService courseAuthorService;


    /**
     * 直播规划每日分配
     * 每小时执行一次
     * <AUTHOR>
     * @date 2025/8/4
     */
    @Scheduled(cron = "0 */10 * * * ?")
    public void livePlanDayTask() {
        try {
            logger.info("live plan day modify task start");
            Date datetime = this.getServerTime();
            // 查询当月所有用户计划
            UserDailyLearningPlanQuery userDailyLearningPlanQuery = new UserDailyLearningPlanQuery();
            userDailyLearningPlanQuery.setMonth(DateUtil.getMonthStartTime(datetime));
            // 跟直播
            userDailyLearningPlanQuery.setSeeLive(UserDailyLearningPlanSeeLive.Y.getCode());
            userDailyLearningPlanQuery.setStatus(DataStatus.Y.getCode());
            List<UserDailyLearningPlan> userDailyLearningPlanList = userDailyLearningPlanService.findAll(userDailyLearningPlanQuery);
            if (!isEmpty(userDailyLearningPlanList) && !userDailyLearningPlanList.isEmpty()) {
                for (UserDailyLearningPlan userDailyLearningPlan : userDailyLearningPlanList) {
                    if (!isEmpty(userDailyLearningPlan.getProjectId()) && !isEmpty(userDailyLearningPlan.getCustomerId())) {
                        // 调用方法：用户规划每日分配直播任务
                        UserDailyLearningPlanDayDetailLive(userDailyLearningPlan.getProjectId(),userDailyLearningPlan.getCustomerId());
                    }
                }
            }
            logger.info("live plan day modify task end");
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
    }

    // 用户规划每日分配直播任务
    public void UserDailyLearningPlanDayDetailLive(Integer projectId , Integer customerId) {
        Date datetime = this.getServerTime();
        // 查询用户项目下每月计划 (每个用户每个月只会存在一条记录)
        UserDailyLearningPlanQuery userDailyLearningPlanQuery = new UserDailyLearningPlanQuery();
        userDailyLearningPlanQuery.setProjectId(projectId);
        userDailyLearningPlanQuery.setCustomerId(customerId);
        userDailyLearningPlanQuery.setMonth(DateUtil.getMonthStartTime(datetime));
        // 跟直播
        userDailyLearningPlanQuery.setSeeLive(UserDailyLearningPlanSeeLive.Y.getCode());
        userDailyLearningPlanQuery.setStatus(DataStatus.Y.getCode());
        List<UserDailyLearningPlan> userDailyLearningPlanList = userDailyLearningPlanService.findAll(userDailyLearningPlanQuery);
        if (isEmpty(userDailyLearningPlanList) || userDailyLearningPlanList.isEmpty()) {
            return;
        }
        UserDailyLearningPlan userDailyLearningPlan = userDailyLearningPlanList.get(0);
        // 查询每日计划
        UserDailyLearningPlanDayQuery userDailyLearningPlanDayQuery = new UserDailyLearningPlanDayQuery();
        userDailyLearningPlanDayQuery.setUserDailyLearningPlanId(userDailyLearningPlan.getId());
        userDailyLearningPlanDayQuery.setStatus(DataStatus.Y.getCode());
        List<UserDailyLearningPlanDay> userDailyLearningPlanDayList = userDailyLearningPlanDayService.findAll(userDailyLearningPlanDayQuery);
        if (isEmpty(userDailyLearningPlanDayList) || userDailyLearningPlanDayList.isEmpty()) {
            return;
        }
        // 获取map<每日时间,每日计划编号>
        Map<Date,Integer> userDailyLearningPlanDayMap = userDailyLearningPlanDayList.stream().collect(Collectors.toMap(UserDailyLearningPlanDay::getPlanDay,UserDailyLearningPlanDay::getId));
        // 获取每日计划编号合集
        List<Integer> userDailyLearningPlanDayIds = userDailyLearningPlanDayList.stream().map(UserDailyLearningPlanDay::getId).collect(Collectors.toList());
        // 查询每日计划直播详情 查询跟直播的数据
        UserDailyLearningPlanDayDetailQuery userDailyLearningPlanDayDetailQuery = new UserDailyLearningPlanDayDetailQuery();
        userDailyLearningPlanDayDetailQuery.setUserDailyLearningPlanDayIds(userDailyLearningPlanDayIds);
        userDailyLearningPlanDayDetailQuery.setTaskType(UserDailyLearningPlanDayDetailTaskType.LIVE.getCode());
        userDailyLearningPlanDayDetailQuery.setStatus(DataStatus.Y.getCode());
        List<UserDailyLearningPlanDayDetail> userDailyLearningPlanDayDetailList = userDailyLearningPlanDayDetailService.findAll(userDailyLearningPlanDayDetailQuery);
        // 获取map<每日计划编号,每日直播详情集合>
        Map<Integer,List<UserDailyLearningPlanDayDetail>> userDailyLearningPlanDayDetailMap = new HashMap<>();
        if (!userDailyLearningPlanDayDetailList.isEmpty()) {
            userDailyLearningPlanDayDetailMap = userDailyLearningPlanDayDetailList.stream().collect(Collectors.groupingBy(UserDailyLearningPlanDayDetail::getUserDailyLearningPlanDayId));
        }
        // 查询用户购买产品下的所有直播信息
        // 查询用户购买的所有视频数量
        List<CourseCatalog> courseCatalogs = this.queryUserCourseCatalogs(projectId, customerId);
        // 取出课程分类ids  过滤下架课程分类，只要上架课程分类
        List<Integer> courseCatalogIds = courseCatalogs.stream().filter(c -> PublicStage.Y.getCode().equals(c.getStage())).map(CourseCatalog::getId).collect(Collectors.toList());
        if (courseCatalogIds.isEmpty()) {
            return;
        }
        // 查询课程分类关联直播
        CourseCatalogLiveQuery courseCatalogLiveQuery = new CourseCatalogLiveQuery();
        courseCatalogLiveQuery.setCourseCatalogIds(courseCatalogIds);
        courseCatalogLiveQuery.setStatus(DataStatus.Y.getCode());
        List<CourseCatalogLive> courseCatalogLiveList = courseCatalogLiveService.findAll(courseCatalogLiveQuery);
        List<Integer> liveIds = courseCatalogLiveList.stream().map(CourseCatalogLive::getLiveId).collect(Collectors.toList());
        if (liveIds.isEmpty()) {
            return;
        }

        LiveQuery liveQuery = new LiveQuery();
        // 项目
        liveQuery.setProjectId(projectId);
        // 当天开始时间
        liveQuery.setMinStartTime(DateUtil.getDayStartTime(datetime));
        // 当月结束时间
        liveQuery.setMaxStartTime(DateUtil.getMonthEndTime(datetime));
        liveQuery.setType(LiveType.FEE.getCode());
        liveQuery.setLiveType(LivePlatformType.C0.getCode());
        liveQuery.setStatus(DataStatus.Y.getCode());
        List<Live> liveList = liveService.findAll(liveQuery);
        if (isEmpty(liveList) || liveList.isEmpty()) {
            return;
        }
        // 获取讲师信息 map<讲师编号,讲师名称>
        Map<Integer,String> courseAuthorMap = new HashMap<>();
        List<Integer> authorIds = liveList.stream().map(Live::getAuthorId).collect(Collectors.toList());
        if (!authorIds.isEmpty()) {
            List<CourseAuthor> courseAuthorList = courseAuthorService.findByIds(authorIds);
            courseAuthorMap = courseAuthorList.stream().collect(Collectors.toMap(CourseAuthor::getId,CourseAuthor::getName));
        }
        // 循环分配每日计划直播
        for (Live live : liveList) {
            // 获取当前直播时间
            Date liveTime = DateUtil.getDayStartTime(live.getStartTime());
            if (userDailyLearningPlanDayMap.containsKey(liveTime)) {
                // 直播时间对应的计划编号
                Integer userDailyLearningPlanDayId = userDailyLearningPlanDayMap.get(liveTime);
                String authorName = "";
                if (courseAuthorMap.containsKey(live.getAuthorId())) {
                    authorName = courseAuthorMap.get(live.getAuthorId());
                }
                // 获取每日计划的已分配的所有直播
                if (userDailyLearningPlanDayDetailMap.containsKey(userDailyLearningPlanDayId)) {
                    List<UserDailyLearningPlanDayDetail> userDailyLearningPlanDayDetails = userDailyLearningPlanDayDetailMap.get(userDailyLearningPlanDayId);
                    for (UserDailyLearningPlanDayDetail userDailyLearningPlanDayDetail : userDailyLearningPlanDayDetails) {
                        if (live.getId().equals(userDailyLearningPlanDayDetail.getTaskNumber())) {
                            UserDailyLearningPlanDayDetail userDailyLearningPlanDayDetailModify = new UserDailyLearningPlanDayDetail();
                            userDailyLearningPlanDayDetailModify.setId(userDailyLearningPlanDayDetail.getId());
                            UserDailyLearningPlanDayDetailLiveResponse liveResponse = new UserDailyLearningPlanDayDetailLiveResponse();
                            liveResponse.setLiveName(live.getName());
                            liveResponse.setLiveId(live.getLiveId());
                            liveResponse.setRoomId(live.getRoomId());
                            liveResponse.setAuthorName(authorName);
                            liveResponse.setProjectItemId(live.getProjectItemId());
                            liveResponse.setStartTime(DateUtil.format(live.getStartTime(),DATETIME_FORMAT));
                            liveResponse.setEndTime(DateUtil.format(live.getEndTime(),DATETIME_FORMAT));
                            userDailyLearningPlanDayDetailModify.setTaskNumber(this.getJSON(liveResponse));
                            userDailyLearningPlanDayDetailModify.setModifyTime(datetime);
                            userDailyLearningPlanDayDetailService.modifyById(userDailyLearningPlanDayDetailModify);
                        }
                    }
                } else {
                    // 分配到每日计划中
                    UserDailyLearningPlanDayDetail userDailyLearningPlanDayDetailCreate = new UserDailyLearningPlanDayDetail();
                    userDailyLearningPlanDayDetailCreate.setUserDailyLearningPlanDayId(userDailyLearningPlanDayId);
                    userDailyLearningPlanDayDetailCreate.setTaskType(UserDailyLearningPlanDayDetailTaskType.LIVE.getCode());
                    userDailyLearningPlanDayDetailCreate.setExternalId(live.getId());
                    UserDailyLearningPlanDayDetailLiveResponse liveResponse = new UserDailyLearningPlanDayDetailLiveResponse();
                    liveResponse.setLiveName(live.getName());
                    liveResponse.setLiveId(live.getLiveId());
                    liveResponse.setRoomId(live.getRoomId());
                    liveResponse.setAuthorName(authorName);
                    liveResponse.setProjectItemId(live.getProjectItemId());
                    liveResponse.setStartTime(DateUtil.format(live.getStartTime(),DATETIME_FORMAT));
                    liveResponse.setEndTime(DateUtil.format(live.getEndTime(),DATETIME_FORMAT));
                    userDailyLearningPlanDayDetailCreate.setTaskNumber(this.getJSON(liveResponse));
                    userDailyLearningPlanDayDetailCreate.setCompletedNumber(0);
                    userDailyLearningPlanDayDetailCreate.setOver(PublicYesNo.N.getCode());
                    userDailyLearningPlanDayDetailCreate.setStatus(DataStatus.Y.getCode());
                    userDailyLearningPlanDayDetailCreate.setModifyTime(datetime);
                    userDailyLearningPlanDayDetailCreate.setCreateTime(datetime);
                    userDailyLearningPlanDayDetailService.create(userDailyLearningPlanDayDetailCreate);
                }
            }
        }
    }


    /**
     * 查询用户已开课订单产品信息
     */
    List<OrderProduct> queryUserOpenCourseAll(Integer projectId, Integer customerId) {
        List<OrderProduct> orderProductList = new ArrayList<>();
        //查询用户已支付订单
        OrderQuery orderQuery = new OrderQuery();
        orderQuery.setStatus(DataStatus.Y.getCode());
        orderQuery.setStage(OrderStage.Y.getCode());
        orderQuery.setProjectId(projectId);
        orderQuery.setCustomerId(customerId);
        List<Order> orderList = this.orderService.findAll(orderQuery);
        if (orderList == null || orderList.isEmpty()) {
            return orderProductList;
        }
        //查询已开课的产品
        List<Integer> orderIds = orderList.stream().map(Base::getId).collect(Collectors.toList());
        OrderProductQuery orderProductQuery = new OrderProductQuery();
        orderProductQuery.setStatus(DataStatus.Y.getCode());
        orderProductQuery.setOrderIds(orderIds);
        orderProductQuery.setCourseSwitch(PublicSwitch.Y.getCode());
        orderProductList = this.orderProductService.findAll(orderProductQuery);
        return orderProductList;
    }


    /**
     * 查询用户购买课程的课程分类
     */
    List<CourseCatalog> queryUserCourseCatalogs(Integer projectId, Integer customerId) {
        List<CourseCatalog> courseCatalogs = new ArrayList<>();
        List<OrderProduct> orderProductList = queryUserOpenCourseAll(projectId, customerId);
        if (!isEmpty(orderProductList) && !orderProductList.isEmpty()) {
            // 获取产品id集合
            List<Integer> productIds = orderProductList.stream().map(OrderProduct::getProductId).collect(Collectors.toList());
            List<Integer> courseIds = new ArrayList<>();
            if (!isEmpty(productIds) && !productIds.isEmpty()) {
                List<ProductCourse> productCourseList = productCourseService.findByProductIds(productIds);
                // 获取产品课程分类为课程且状态正产的产品课程ids
                List<Integer> productCourseIds = new ArrayList<>();
                // 获取产品课程分类为班型且状态正产,绑定类型为课程的班型ids
                List<Integer> courseClassIds = new ArrayList<>();
                for (ProductCourse productCourse : productCourseList) {
                    if (DataStatus.Y.getCode().equals(productCourse.getStatus())) {
                        if (com.common.constant.ProductCourseCatalog.COURSE.getCode().equals(productCourse.getCourseCatalog())) {
                            productCourseIds.add(productCourse.getId());
                        } else if (com.common.constant.ProductCourseCatalog.CLASS.getCode().equals(productCourse.getCourseCatalog()) && ClassBindingType.C0.getCode().equals(productCourse.getClassBindingType())) {
                            courseClassIds.add(productCourse.getId());
                        } else if (com.common.constant.ProductCourseCatalog.CLASS.getCode().equals(productCourse.getCourseCatalog()) && ClassBindingType.C1.getCode().equals(productCourse.getClassBindingType())) {
                            // 获取产品课程分类为班型且状态正常,绑定类型为课程分类的班型id(按照课程逻辑走)
                            productCourseIds.add(productCourse.getId());
                        }
                    }
                }
                // 查询班型下关联的课程，并获取课程ids
                if (!courseClassIds.isEmpty()) {
                    ProductCourseClassQuery productCourseClassQuery = new ProductCourseClassQuery();
                    productCourseClassQuery.setParentIds(courseClassIds);
                    productCourseClassQuery.setStatus(DataStatus.Y.getCode());
                    List<ProductCourseClass> productCourseClassList = productCourseClassService.findAll(productCourseClassQuery);
                    courseIds = productCourseClassList.stream().map(ProductCourseClass::getProductCourseId).collect(Collectors.toList());
                }
                // 合并课程id
                productCourseIds.addAll(courseIds);
                List<Integer> courseCatalogIds = new ArrayList<>();
                if(!isEmpty(productCourseIds) && !productCourseIds.isEmpty()){
                    // 查询课程关联的课程分类数据
                    ProductCourseCatalogQuery productCourseCatalogQuery = new ProductCourseCatalogQuery();
                    productCourseCatalogQuery.setProductCourseIds(productCourseIds);
                    productCourseCatalogQuery.setStatus(DataStatus.Y.getCode());
                    List<ProductCourseCatalog> productCourseCatalogList = productCourseCatalogService.findAll(productCourseCatalogQuery);
                    if(!isEmpty(productCourseCatalogList) && !productCourseCatalogList.isEmpty()){
                        // 获取的关联的课程分类id集合
                        courseCatalogIds = productCourseCatalogList.stream().map(ProductCourseCatalog::getCourseCatalogId).collect(Collectors.toList());
                    }
                }
                if (!courseCatalogIds.isEmpty()) {
                    CourseCatalogQuery courseCatalogQuery = new CourseCatalogQuery();
                    courseCatalogQuery.setIds(courseCatalogIds);
                    courseCatalogQuery.setStatus(DataStatus.Y.getCode());
                    courseCatalogs = courseCatalogService.findAll(courseCatalogQuery);
                }
            }
        }
        return courseCatalogs;
    }

}
