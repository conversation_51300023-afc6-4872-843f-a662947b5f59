package com.task.component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;

import com.common.constant.DataStatus;
import com.common.constant.PublicOver;
import com.common.constant.UserDailyLearningPlanDayDetailTaskType;
import com.common.util.DateUtil;
import com.domain.Course;
import com.domain.UserCourseRecordMerge;
import com.domain.UserDailyLearningPlanCourse;
import com.domain.UserDailyLearningPlanDay;
import com.domain.UserDailyLearningPlanDayDetail;
import com.domain.UserExamPaperRecord;
import com.domain.UserLiveLogs;
import com.domain.complex.ExamPaperQuestionQuery;
import com.domain.complex.UserCourseRecordMergeQuery;
import com.domain.complex.UserDailyLearningPlanDayDetailQuery;
import com.domain.complex.UserDailyLearningPlanDayQuery;
import com.domain.complex.UserExamPaperRecordDetailQuery;
import com.domain.complex.UserExamPaperRecordQuery;
import com.domain.complex.UserLiveLogsQuery;
import com.service.CourseCatalogService;
import com.service.CourseService;
import com.service.ExamPaperQuestionService;
import com.service.ExamPaperService;
import com.service.LiveService;
import com.service.ProjectItemService;
import com.service.UserCourseRecordMergeService;
import com.service.UserCourseRecordService;
import com.service.UserDailyLearningPlanCourseServiceImpl;
import com.service.UserDailyLearningPlanDayDetailService;
import com.service.UserDailyLearningPlanDayService;
import com.service.UserDailyLearningPlanService;
import com.service.UserExamPaperRecordDetailService;
import com.service.UserExamPaperRecordService;
import com.service.UserLiveLogsService;
import com.task.bean.UserDailyLearningPlanDayDetailLiveResponse;

//@Component
public class UserDailyLearningPlanDayDetailTask extends BaseTask {


	@Autowired
	private UserDailyLearningPlanDayService userDailyLearningPlanDayService;
	@Autowired
	private UserDailyLearningPlanCourseServiceImpl userDailyLearningPlanCourseService;
	@Autowired
	private UserDailyLearningPlanService userDailyLearningPlanService;
	@Autowired
	private ProjectItemService projectItemService;
	@Autowired
	private UserDailyLearningPlanDayDetailService userDailyLearningPlanDayDetailService;
	@Autowired
	private CourseService courseService;
	@Autowired
	private ExamPaperService examPaperService;
	@Autowired
	private LiveService liveService;
	@Autowired
	private UserCourseRecordMergeService userCourseRecordMergeService;
	@Autowired
	private UserLiveLogsService userLiveLogsService;
	@Autowired
	private ExamPaperQuestionService examPaperQuestionService;
	@Autowired
	private UserExamPaperRecordService userExamPaperRecordService;
	@Autowired
	private UserExamPaperRecordDetailService userExamPaperRecordDetailService;
	@Autowired
	private CourseCatalogService courseCatalogService;
	@Autowired
	private UserCourseRecordService userCourseRecordService;


	/**
	 * 定时一小时，修改用户每日学习计划详情表和每日计划表的over字段
	 */
//	@Scheduled(initialDelay = 1000, fixedRate= 1 * 60 * 60 * 1000)
	public void modifyUserDailyLearningPlanDayDetail(){
		try {
			Date serverTime = this.getServerTime();
			logger.info("modify user daily learning plan day detail task start");
			//查询所有用户当日的计划（over字段不为0,证明还未完成）
			UserDailyLearningPlanDayQuery userDailyLearningPlanDayQuery = new UserDailyLearningPlanDayQuery();
			userDailyLearningPlanDayQuery.setPlanDay(DateUtil.parse(DateUtil.format(serverTime, DATE_FORMAT), DATE_FORMAT));
			userDailyLearningPlanDayQuery.setStatus(DataStatus.Y.getCode());
			userDailyLearningPlanDayQuery.setOverNotZero("0");
			List<UserDailyLearningPlanDay> userDailyLearningPlanDays = this.userDailyLearningPlanDayService.findAll(userDailyLearningPlanDayQuery);
			//取出计划ids
			List<Integer> userDailyLearningPlanDayIds = userDailyLearningPlanDays.stream().map(UserDailyLearningPlanDay::getId).collect(Collectors.toList());
			if (isEmpty(userDailyLearningPlanDayIds) || userDailyLearningPlanDayIds.isEmpty()) {
				return;
			}
			//查询每日的计划绑定详情
			UserDailyLearningPlanDayDetailQuery userDailyLearningPlanDayDetailQuery = new UserDailyLearningPlanDayDetailQuery();
			userDailyLearningPlanDayDetailQuery.setUserDailyLearningPlanDayIds(userDailyLearningPlanDayIds);
			userDailyLearningPlanDayDetailQuery.setStatus(DataStatus.Y.getCode());
			List<UserDailyLearningPlanDayDetail> userDailyLearningPlanDayDetails = this.userDailyLearningPlanDayDetailService.findAll(userDailyLearningPlanDayDetailQuery);
			if (isEmpty(userDailyLearningPlanDayDetails) || userDailyLearningPlanDayDetails.isEmpty()){
				return;
			}
			//计划详情按日计划id分组
			Map<Integer, List<UserDailyLearningPlanDayDetail>> userDailyLearningPlanDayDetailMap = userDailyLearningPlanDayDetails.stream().collect(Collectors.groupingBy(UserDailyLearningPlanDayDetail::getUserDailyLearningPlanDayId));

			for (UserDailyLearningPlanDay userDailyLearningPlanDay : userDailyLearningPlanDays) {

				if (userDailyLearningPlanDayDetailMap.containsKey(userDailyLearningPlanDay.getId())){
					//任务完成数量
					Integer finishedNumber = 0;
					List<UserDailyLearningPlanDayDetail> userDailyLearningPlanDayDetailsList = userDailyLearningPlanDayDetailMap.get(userDailyLearningPlanDay.getId());
					//按任务类型分情况查询视频,直播,试卷
					for (UserDailyLearningPlanDayDetail userDailyLearningPlanDayDetail : userDailyLearningPlanDayDetailsList) {
						//修改
						UserDailyLearningPlanDayDetail userDailyLearningPlanDayDetailModify = new UserDailyLearningPlanDayDetail();
						userDailyLearningPlanDayDetailModify.setId(userDailyLearningPlanDayDetail.getId());
						userDailyLearningPlanDayDetailModify.setModifyTime(serverTime);

						if (UserDailyLearningPlanDayDetailTaskType.COURSE.getCode().equals(userDailyLearningPlanDayDetail.getTaskType())){
							//外部id存的是userDailyLearningPlanCourse的id
							if (!this.isEmpty(userDailyLearningPlanDayDetail.getExternalId())){
								UserDailyLearningPlanCourse userDailyLearningPlanCourse = userDailyLearningPlanCourseService.findById(userDailyLearningPlanDayDetail.getExternalId());
								if(!this.isEmpty(userDailyLearningPlanCourse) && !this.isEmpty(userDailyLearningPlanCourse.getCourseId())){
									Course course = this.courseService.findById(userDailyLearningPlanCourse.getCourseId());
									//查询此用户学习此视频的累计时长
									if (!this.isEmpty(course)){
										UserCourseRecordMergeQuery userCourseRecordMergeQuery = new UserCourseRecordMergeQuery();
										userCourseRecordMergeQuery.setCustomerId(userDailyLearningPlanDay.getCustomerId());
										userCourseRecordMergeQuery.setVideoId(course.getId());
										userCourseRecordMergeQuery.setStatus(DataStatus.Y.getCode());
										List<UserCourseRecordMerge> userCourseRecordMerges = this.userCourseRecordMergeService.findAll(userCourseRecordMergeQuery);
										Integer totalLength = 0;
										for (UserCourseRecordMerge userCourseRecordMerge : userCourseRecordMerges) {
											if (!this.isEmpty(userCourseRecordMerge.getLength())){
												totalLength = totalLength + userCourseRecordMerge.getLength();
											}
										}
										//只有累计时长超过总时长的66%才算完成
										if ((new BigDecimal(totalLength).divide(new BigDecimal(course.getLength()),2, RoundingMode.HALF_UP)).multiply(new BigDecimal(100)).intValue() >= 66){
											userDailyLearningPlanDayDetailModify.setOver(PublicOver.Y.getCode());
											finishedNumber = finishedNumber + 1;
										}else {
											userDailyLearningPlanDayDetailModify.setOver(PublicOver.N.getCode());
										}
										userDailyLearningPlanDayDetailModify.setTaskNumber(course.getLength());
										userDailyLearningPlanDayDetailModify.setCompletedNumber(totalLength);
										this.userDailyLearningPlanDayDetailService.modifyById(userDailyLearningPlanDayDetailModify);
									}
								}
							}

						}else if (UserDailyLearningPlanDayDetailTaskType.LIVE.getCode().equals(userDailyLearningPlanDayDetail.getTaskType())){
							//查询用户是否进入过直播间
							UserLiveLogsQuery userLiveLogsQuery = new UserLiveLogsQuery();
							userLiveLogsQuery.setCustomerId(userDailyLearningPlanDay.getCustomerId());
							userLiveLogsQuery.setLiveId(userDailyLearningPlanDayDetail.getExternalId());
							List<UserLiveLogs> userLiveLogs = this.userLiveLogsService.findAll(userLiveLogsQuery);
							if (!this.isEmpty(userLiveLogs) && !userLiveLogs.isEmpty()){
								UserDailyLearningPlanDayDetailLiveResponse userDailyLearningPlanDayDetailLiveResponse = (UserDailyLearningPlanDayDetailLiveResponse) this.getObject(userDailyLearningPlanDayDetail.getTaskNumber(), UserDailyLearningPlanDayDetailLiveResponse.class);
								//因为直播间有复用的情况,一个直播间可能改个开始直播时间就又能用
								boolean flag = false;
								for (UserLiveLogs userLiveLog : userLiveLogs) {
									if (userLiveLog.getCreateTime().compareTo(DateUtil.parse(userDailyLearningPlanDayDetailLiveResponse.getStartTime(),DATE_FORMAT)) > 0){
										flag = true;
										break;
									}
								}
								if (flag){
									userDailyLearningPlanDayDetailModify.setOver(PublicOver.Y.getCode());
									finishedNumber = finishedNumber + 1;
								}else {
									userDailyLearningPlanDayDetailModify.setOver(PublicOver.N.getCode());
								}
							}else {
								userDailyLearningPlanDayDetailModify.setOver(PublicOver.N.getCode());
							}
							this.userDailyLearningPlanDayDetailService.modifyById(userDailyLearningPlanDayDetailModify);

						}else if (UserDailyLearningPlanDayDetailTaskType.EXAM.getCode().equals(userDailyLearningPlanDayDetail.getTaskType())){
							//查询试卷题量
							ExamPaperQuestionQuery examPaperQuestionQuery = new ExamPaperQuestionQuery();
							examPaperQuestionQuery.setExamPaperId(userDailyLearningPlanDayDetail.getExternalId());
							examPaperQuestionQuery.setStatus(DataStatus.Y.getCode());
							Integer questionCount = this.examPaperQuestionService.count(examPaperQuestionQuery);
							//查询做题记录
							UserExamPaperRecordQuery userExamPaperRecordQuery = new UserExamPaperRecordQuery();
							userExamPaperRecordQuery.setCustomerId(userDailyLearningPlanDay.getCustomerId());
							userExamPaperRecordQuery.setExamPaperId(userDailyLearningPlanDayDetail.getExternalId());
							userExamPaperRecordQuery.setStatus(DataStatus.Y.getCode());
							List<UserExamPaperRecord> userExamPaperRecordList = this.userExamPaperRecordService.findAll(userExamPaperRecordQuery);
							Integer doQuestionCount = 0;
							if (!this.isEmpty(userExamPaperRecordList) && !userExamPaperRecordList.isEmpty()){
								UserExamPaperRecord userExamPaperRecord = userExamPaperRecordList.get(0);
								//查询做题明细
								UserExamPaperRecordDetailQuery userExamPaperRecordDetailQuery = new UserExamPaperRecordDetailQuery();
								userExamPaperRecordDetailQuery.setUserExamPaperRecordId(userExamPaperRecord.getId());
								userExamPaperRecordDetailQuery.setStatus(DataStatus.Y.getCode());
								doQuestionCount = this.userExamPaperRecordDetailService.count(userExamPaperRecordDetailQuery);
								if (PublicOver.Y.getCode().equals(userExamPaperRecord.getOver())){
									userDailyLearningPlanDayDetailModify.setOver(PublicOver.Y.getCode());
									finishedNumber = finishedNumber + 1;
								}else {
									userDailyLearningPlanDayDetailModify.setOver(PublicOver.N.getCode());
								}
							}else {
								userDailyLearningPlanDayDetailModify.setOver(PublicOver.N.getCode());
							}
							userDailyLearningPlanDayDetailModify.setTaskNumber(String.valueOf(questionCount));
							userDailyLearningPlanDayDetailModify.setCompletedNumber(doQuestionCount);

							this.userDailyLearningPlanDayDetailService.modifyById(userDailyLearningPlanDayDetailModify);

						}else {
							logger.info("userDailyLearningPlanDayDetail表id为{}的数据,任务类型不存在",userDailyLearningPlanDayDetail.getId());
						}
					}
					//修改当日计划的完成字段
					UserDailyLearningPlanDay userDailyLearningPlanDayModify = new UserDailyLearningPlanDay();
					userDailyLearningPlanDayModify.setId(userDailyLearningPlanDay.getId());
					userDailyLearningPlanDayModify.setModifyTime(serverTime);
					if (finishedNumber == userDailyLearningPlanDayDetailsList.size()){
						userDailyLearningPlanDayModify.setOver(PublicOver.Y.getCode());
					}else {
						userDailyLearningPlanDayModify.setOver(PublicOver.N.getCode());
					}
					this.userDailyLearningPlanDayService.modifyById(userDailyLearningPlanDayModify,false);


				}else {//如果没绑定视频或者直播,试卷,直接默认完成计划
					//修改当日计划的完成字段
					UserDailyLearningPlanDay userDailyLearningPlanDayModify = new UserDailyLearningPlanDay();
					userDailyLearningPlanDayModify.setId(userDailyLearningPlanDay.getId());
					userDailyLearningPlanDayModify.setModifyTime(serverTime);
					userDailyLearningPlanDayModify.setOver(PublicOver.Y.getCode());
					this.userDailyLearningPlanDayService.modifyById(userDailyLearningPlanDayModify,false);
				}
			}

			logger.info("modify user daily learning plan day detail task end");

		} catch (Exception e) {
			logger.error(e.getMessage(), e);
		}
	}


}
