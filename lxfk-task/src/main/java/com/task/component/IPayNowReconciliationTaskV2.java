package com.task.component;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.locks.Lock;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.aliyun.dingtalkrobot_1_0.models.BatchSendOTORequest;
import com.common.bean.DingTalkRequest;
import com.common.bean.SMSPayload;
import com.common.client.IPayNowServcieClient;
import com.common.constant.CacheKey;
import com.common.constant.DataStatus;
import com.common.constant.DepartmentCatalog;
import com.common.constant.Device;
import com.common.constant.DingMessageKey;
import com.common.constant.IPayNowTransStatus;
import com.common.constant.OrderAudit;
import com.common.constant.OrderStage;
import com.common.constant.PayChannel;
import com.common.constant.SMSType;
import com.common.constant.TokenType;
import com.common.constant.UserCatalog;
import com.common.constant.UserLogAction;
import com.common.constant.UserStage;
import com.common.mq.MQTag;
import com.common.mq.MQTopic;
import com.common.util.DateUtil;
import com.domain.Customer;
import com.domain.Department;
import com.domain.Order;
import com.domain.OrderFlow;
import com.domain.OrderFlowSplit;
import com.domain.OrderQrcode;
import com.domain.SysUser;
import com.domain.SysUserDepartment;
import com.domain.User;
import com.domain.UserLog;
import com.domain.complex.OrderFlowQuery;
import com.domain.complex.OrderFlowSplitQuery;
import com.domain.complex.OrderQrcodeQuery;
import com.domain.complex.OrderQueryV2;
import com.domain.complex.SysUserDepartmentQuery;
import com.domain.complex.SysUserMasterQuery;
import com.domain.complex.UserLogQuery;
import com.service.CustomerService;
import com.service.DepartmentService;
import com.service.OrderFlowService;
import com.service.OrderFlowSplitService;
import com.service.OrderQrcodeService;
import com.service.OrderService;
import com.service.SysUserDepartmentService;
import com.service.SysUserMasterService;
import com.service.SysUserService;
import com.service.UserLogService;
import com.service.UserService;
import com.task.bean.DingMessageSampleTextParam;
import com.task.config.MQProducer;
import com.task.constant.App;
import com.task.sequence.LogSequence;

//@Component
public class IPayNowReconciliationTaskV2 extends BaseTask {
    @Autowired
    private OrderService orderService;
    @Autowired
    private OrderFlowService orderFlowService;
    @Autowired
    private OrderFlowSplitService orderFlowSplitService;
    @Autowired
    private UserService userService;
    @Autowired
    private CustomerService customerService;
    @Autowired
    private SysUserMasterService sysUserMasterService;
    @Autowired
    private SysUserService sysUserService;
    @Autowired
    private SysUserDepartmentService sysUserDepartmentService;
    @Autowired
    private DepartmentService departmentService;
    @Autowired
    private UserLogService userLogService;
    @Autowired
    private RedisTemplate<String, String> redisTemplate;
    @Autowired
    private OrderQrcodeService orderQrcodeService;

    /**
     * 通过查询订单接口实现对账功能
     * <AUTHOR>
     * @date 2025-08-17
     */
    @Scheduled(initialDelay = 2 * 60 * 1000, fixedRate= 5 * 60 * 1000)
    public void start() {
        try {
            logger.info("IPayNowReconciliationTaskV2 Start");
            Date serverTime = getServerTime();
            Date minutesAgo10 = DateUtil.add(serverTime, Calendar.MINUTE, -10);
            Date hoursAgo24M10 = DateUtil.add(minutesAgo10, Calendar.HOUR, -24);

            //1.查询所有未支付的订单流水 无退款,也不用管支付方式
            OrderFlowQuery orderFlowQuery = new OrderFlowQuery();
            orderFlowQuery.setStatus(DataStatus.Y.getCode());
            orderFlowQuery.setStage(OrderStage.N.getCode());
            orderFlowQuery.setMinPayTime(hoursAgo24M10);
            orderFlowQuery.setMaxPayTime(minutesAgo10);
            List<OrderFlow> orderFlowList = orderFlowService.findAll(orderFlowQuery);
            if (isEmpty(orderFlowList) || orderFlowList.isEmpty()){
                logger.info("IPayNowReconciliationTaskV2 End: orderFlowList is empty");
                return;
            }

            //2.查询每个未支付订单的真实支付状态
            IPayNowServcieClient iPayNowServcieClient = new IPayNowServcieClient();
            for (OrderFlow orderFlow : orderFlowList) {
                OrderQrcodeQuery orderQrcodeQuery = new OrderQrcodeQuery();
                orderQrcodeQuery.setStatus(DataStatus.Y.getCode());
                orderQrcodeQuery.setOrderFlowId(orderFlow.getId());
                OrderQrcode orderQrcode = orderQrcodeService.findMax(orderQrcodeQuery);
                if (isEmpty(orderQrcode) || isEmpty(orderQrcode.getPayCode())){
                    logger.info("Continue: there is not orderQrcode, orderFlowId[{}]", orderFlow.getId());
                    continue;
                }

                //查询订单支付状态
                Map<String, String> resultMap = this.getTransStatus(iPayNowServcieClient, orderQrcode.getPayCode());
                IPayNowTransStatus transStatus = IPayNowTransStatus.get(resultMap.get("transStatus"));
                if (isEmpty(transStatus)){
                    logger.info("Continue: there is not transStatus, orderFlowId[{}]", orderFlow.getId());
                    continue;
                }

                //3.只处理 订单支付成功
                if (!IPayNowTransStatus.A001.equals(transStatus)){
                    continue;
                }

                //4.锁定当前订单流水,防止第三方回调并发,下面的this.createNotify()内部会再次检查订单流水的审核状态,所以即使拿到锁,但是订单已经被处理过了,也不用担心重复处理的问题
                Lock lock = getLock(redisTemplate, CacheKey.I_PAY_NOW_CREATE_NOTIFY_LOCK, orderQrcode.getPayCode());
                try {
                    if (!lock.tryLock()) {
                        logger.info("Continue: there is locked, orderFlowId[{}]", orderFlow.getId());
                        continue;
                    }

                    //5.支付成功的走一遍订单支付成功的通知逻辑
                    logger.info("支付成功, orderFlowId[{}]", orderFlow.getId());
                    this.createNotify(resultMap);
                } finally {
                    if (lock != null) {
                        lock.unlock();
                    }
                }
            }
            logger.info("IPayNowReconciliationTaskV2 End");
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
    }

    /**
     * 支付通知 从 /v2/ipaynow/cms/pay/notify/create 复制而来
     * @param resultMap 调用MQ002-支付订单查询接口后的返回值封装的map
     */
    private void createNotify(Map<String, String> resultMap){
        //商户订单号
//        String orderNo = this.httpServletRequest.getParameter("mhtOrderNo").substring(24);
        String orderNo = resultMap.get("mhtOrderNo").substring(24);
        // 支付方式
//        String payChannelType = this.httpServletRequest.getParameter("payChannelType");
        String payChannelType = resultMap.get("payChannelType");
        // 设备类型
//        String deviceType = this.httpServletRequest.getParameter("deviceType");
        String deviceType = resultMap.get("deviceType");
        logger.info("deviceType : {}",deviceType);
        // 查询该支付订单信息
        OrderFlow orderFlow = this.orderFlowService.findById(Integer.parseInt(orderNo));
        Order order = this.orderService.findById(orderFlow.getOrderId());
        OrderFlowSplitQuery orderFlowSplitQuery = new OrderFlowSplitQuery();
        orderFlowSplitQuery.setOrderFlowId(orderFlow.getId());
        List<OrderFlowSplit> orderFlowSplitList = orderFlowSplitService.findAll(orderFlowSplitQuery);
        if (!orderFlowSplitList.isEmpty() && order != null && OrderAudit.ING.getCode().equals(orderFlow.getAudit())) {
            //业务逻辑审核前同一流水下只会有一条拆分数据
            OrderFlowSplit orderFlowSplit = orderFlowSplitList.get(0);
            Date datetime = this.getServerTime();
            User user = this.userService.findById(order.getUserId());
            if (user != null) {
                //查询用户手机号
                Customer customer = this.customerService.findById(user.getCustomerId());
                User userModify = new User();
                userModify.setId(user.getId());
                userModify.setCatalog(UserCatalog.C2.getCode());
                userModify.setStage(UserStage.N.getCode());
                userModify.setModifyTime(datetime);
                // 更新订单
                OrderQueryV2 orderQueryV2 = new OrderQueryV2();
                // 记录用户当时成单状态
                orderQueryV2.setUserCatalog(user.getCatalog());
                orderQueryV2.setOrderFlowId(orderFlow.getId());
                // 12 支付宝 36 支付宝花呗分期
                if ("12".equals(payChannelType) || "36".equals(payChannelType)) {
                    orderQueryV2.setPayChannel(PayChannel.IPAYNOW_ALIPAY.getCode());
                }
                // 13 微信
                if ("13".equals(payChannelType)) {
                    orderQueryV2.setPayChannel(PayChannel.IPAYNOW_WXPAY.getCode());
                }
                if ("01".equals(deviceType)) {
                    // 12 支付宝
                    if ("12".equals(payChannelType)) {
                        orderQueryV2.setPayChannel(PayChannel.IPAYNOW_LX_APP_ALIPAY.getCode());
                    }
                    // 13 微信
                    if ("13".equals(payChannelType)) {
                        orderQueryV2.setPayChannel(PayChannel.IPAYNOW_LX_APP_WXPAY.getCode());
                    }
                }
                if (!isEmpty(orderFlowSplit.getChanceUserId())) {
                    //查询业绩坐席是不是班主任
                    SysUser sysUser = sysUserService.findById(orderFlowSplit.getChanceUserId());
                    if (!isEmpty(sysUser) && DataStatus.Y.getCode().equals(sysUser.getStatus())) {
                        SysUserDepartmentQuery sysUserDepartmentQuery = new SysUserDepartmentQuery();
                        sysUserDepartmentQuery.setStatus(DataStatus.Y.getCode());
                        sysUserDepartmentQuery.setUserId(sysUser.getId());
                        List<SysUserDepartment> sysUserDepartments = sysUserDepartmentService.findAll(sysUserDepartmentQuery);
                        if (!isEmpty(sysUserDepartments) && !sysUserDepartments.isEmpty()) {
                            if (!isEmpty(sysUserDepartments.get(0).getDepartmentId())) {
                                Department department = departmentService.findById(sysUserDepartments.get(0).getDepartmentId());
                                // 如果所属部门是班主任
                                if (!isEmpty(department) && DataStatus.Y.getCode().equals(department.getStatus()) && DepartmentCatalog.BANZHUREN.getCode().equals(department.getCatalog())) {
                                    orderQueryV2.setSysCatalog(DepartmentCatalog.BANZHUREN.getCode());
                                    // 设置为已认领
                                    userModify.setStage(UserStage.Y.getCode());
                                }
                            }
                        }
                    }
                }
                // 检查是否已经分配过班主任
                SysUserMasterQuery sysUserMasterQuery = new SysUserMasterQuery();
                sysUserMasterQuery.setUserId(user.getId());
                sysUserMasterQuery.setStatus(DataStatus.Y.getCode());
                Integer count = sysUserMasterService.countByQuery(sysUserMasterQuery);
                if (count > 0) {
                    userModify.setStage(UserStage.Y.getCode());
                }
                // 流转明细记录用户成单
                UserLog userLog = new UserLog();
                userLog.setUserId(user.getId());
                userLog.setAuthUserId(orderFlowSplit.getChanceUserId());
                userLog.setAction(UserLogAction.C1009.getCode());
                userLog.setCreateTime(datetime);
                userLog.setModifyTime(datetime);
                userLog.setStatus(DataStatus.Y.getCode());
                // 订单审核通过
                this.orderService.paySuccess(orderQueryV2, userModify, userLog);
                //发送报单信息
                Department department = new Department();
                if (!isEmpty(orderFlowSplit.getChanceUserId())) {
                    //查询业绩坐席是不是班主任
                    SysUser sysUser = sysUserService.findById(orderFlowSplit.getChanceUserId());
                    if (!isEmpty(sysUser) && DataStatus.Y.getCode().equals(sysUser.getStatus())) {
                        SysUserDepartmentQuery sysUserDepartmentQuery = new SysUserDepartmentQuery();
                        sysUserDepartmentQuery.setStatus(DataStatus.Y.getCode());
                        sysUserDepartmentQuery.setUserId(sysUser.getId());
                        List<SysUserDepartment> sysUserDepartments = sysUserDepartmentService.findAll(sysUserDepartmentQuery);
                        if (!isEmpty(sysUserDepartments) && !sysUserDepartments.isEmpty()) {
                            if (!isEmpty(sysUserDepartments.get(0).getDepartmentId())) {
                                department = departmentService.findById(sysUserDepartments.get(0).getDepartmentId());
                                //保单机器人异步处理
                                DingTalkRequest dingTalkRequest = new DingTalkRequest();
                                dingTalkRequest.setDepartmentId(department.getId());
                                dingTalkRequest.setAmount(orderFlow.getAmount());
                                dingTalkRequest.setSysUserName(sysUser.getName());
                                MQProducer.INSTANCE.sendOneway(LogSequence.get(), MQTopic.LXFK_API, MQTag.LXFK_API_DING_TALK_ROBOT_MESSAGE, this.getJSON(dingTalkRequest));
                            }
                        }
                    }

                    //手动回库数据3小时内被他人认领且认领后48小时内成单发送提醒
                    UserLogQuery userLogQuery = new UserLogQuery();
                    userLogQuery.setUserId(userModify.getId());
                    List<String> actions = new ArrayList<>();
                    actions.add(UserLogAction.C1005.getCode());
                    actions.add(UserLogAction.C1006.getCode());
                    userLogQuery.setActions(actions);
                    //查询是否是48小时内认领
                    userLogQuery.setCreateTimeStart(DateUtil.getFutureHour(datetime, -48));
                    List<UserLog> userLogs = userLogService.findAll(userLogQuery);
                    if (!userLogs.isEmpty()){
                        //存在48小时内认领，查询是否在认领前3小时内手动回库
                        actions.clear();
                        actions.add(UserLogAction.C1008.getCode());
                        userLogQuery.setActions(actions);
                        userLogQuery.setCreateTimeStart(DateUtil.getFutureHour(userLogs.get(0).getCreateTime(), -3));
                        userLogQuery.setCreateTimeEnd(userLogs.get(0).getCreateTime());
                        List<UserLog> userLogList = userLogService.findAll(userLogQuery);
                        if (!userLogList.isEmpty()){
                            //存在手动回库操作，发送提醒
                            //发送钉钉通知
                            BatchSendOTORequest batchSendOTORequest = new BatchSendOTORequest();
                            batchSendOTORequest.setRobotCode(App.LXFK_MESSAGE_ROBOT_CODE);
                            batchSendOTORequest.setMsgKey(DingMessageKey.SAMPLE_TEXT.getCode());
                            List<String> dingUserIds = new ArrayList<>();
                            //提醒人员 王晓蔓 刘振茹 黄志坤
                            List<Integer> sysUserIds = new ArrayList<>();
                            sysUserIds.add(10000288);
                            sysUserIds.add(10021997);
                            sysUserIds.add(10000093);
                            List<SysUser> sysUserList = sysUserService.findByIds(sysUserIds);
                            for (SysUser dingSysUser : sysUserList) {
                                if (!isEmpty(dingSysUser.getDingUserId())) {
                                    dingUserIds.add(dingSysUser.getDingUserId());
                                }
                            }
                            batchSendOTORequest.setUserIds(dingUserIds);
                            //设置通知内容
                            DingMessageSampleTextParam sampleTextParam = new DingMessageSampleTextParam();
                            String content = "您好，" + sysUser.getUsername() + "在众律智学成交一笔3小时内回库机会认领48小时内成交订单，该线索编号：" +
                                    customer.getUsername() + "  开课手机号：" + customer.getUsername();
                            sampleTextParam.setContent(content);
                            batchSendOTORequest.setMsgParam(this.getJSON(sampleTextParam));
                            //发送通知给指定处理人员
                            MQProducer.INSTANCE.sendOneway(LogSequence.get(), MQTopic.LXFK_API, MQTag.LXFK_API_DING_MESSAGE_MESSAGE, this.getJSON(batchSendOTORequest));
                        }
                    }
                }
                // 发送短信通知
                SMSPayload smsPayload = new SMSPayload();
                smsPayload.setMobile(customer.getUsername());
                smsPayload.setCreateTime(datetime);
                String code = LogSequence.get();
                smsPayload.setCode(code);
                smsPayload.setType(SMSType.C1.getCode());
                // 设置模板id
                smsPayload.setTemplateId(com.common.constant.App.FEIGE_ORDER_PASS_TEMPLATE_ID);
                List<String> datas = new ArrayList<>();
                datas.add(this.getMobile(customer.getUsername()));
                datas.add(order.getCode());
                datas.add(orderFlow.getAmount().toString());
                // 设置发送的内容(填充模板用)
                smsPayload.setDatas(datas);
                String content = String.format(com.common.constant.App.FEIGE_ORDER_PASS_SMS_CONTENT, this.getMobile(customer.getUsername()), order.getCode(), orderFlow.getAmount().toString());
                if (DepartmentCatalog.BANZHUREN.getCode().equals(department.getCatalog())) {
                    content = String.format(com.common.constant.App.MASTER_FEIGE_ORDER_PASS_SMS_CONTENT, this.getMobile(customer.getUsername()), order.getCode(), orderFlow.getAmount().toString());
                    smsPayload.setTemplateId(com.common.constant.App.MASTER_FEIGE_ORDER_PASS_TEMPLATE_ID);
                }
                // 设置发送的内容(记录到数据库用)
                smsPayload.setContent(content);
                // 发送到mq
                MQProducer.INSTANCE.sendOneway(code, MQTopic.LXFK_API, MQTag.LXFK_API_SMS, this.getJSON(smsPayload));
                // 如果是全款 或者是尾款 需要重新登录 签署协议
//                    if (!OrderFlowCatalog.C1000.getCode().equals(orderFlow.getCatalog())) {
                // 删除官网的token
                this.redisTemplate.delete(CacheKey.USER_TOKEN + TokenType.C.name() + Device.PC.name() + String.valueOf(user.getCustomerId()));
                // 删除安卓和IOS的token
                this.redisTemplate.delete(CacheKey.USER_TOKEN + TokenType.C.name() + Device.MD.name() + String.valueOf(user.getCustomerId()));
//                    }
            } else {
                //更新订单
                OrderQueryV2 orderQueryV2 = new OrderQueryV2();
                orderQueryV2.setOrderFlowId(orderFlow.getId());
                // 12 支付宝
                if ("12".equals(payChannelType)) {
                    orderQueryV2.setPayChannel(PayChannel.IPAYNOW_ALIPAY.getCode());
                }
                // 13 微信
                if ("13".equals(payChannelType)) {
                    orderQueryV2.setPayChannel(PayChannel.IPAYNOW_WXPAY.getCode());
                }
                if ("01".equals(deviceType)) {
                    // 12 支付宝
                    if ("12".equals(payChannelType)) {
                        orderQueryV2.setPayChannel(PayChannel.IPAYNOW_LX_APP_ALIPAY.getCode());
                    }
                    // 13 微信
                    if ("13".equals(payChannelType)) {
                        orderQueryV2.setPayChannel(PayChannel.IPAYNOW_LX_APP_WXPAY.getCode());
                    }
                }
                this.orderService.paySuccess(orderQueryV2, null, null);
            }
        }
    }

    /**
     * 获取订单状态
     */
    private Map<String, String> getTransStatus(IPayNowServcieClient iPayNowServcieClient, String payCode){
        Map<String, String> dataMap = new HashMap<>();
        dataMap.put("funcode","MQ002");
        dataMap.put("version", "1.0.2");
        dataMap.put("deviceType", "20");
        dataMap.put("appId", IPayNowServcieClient.APP_ID);
        dataMap.put("mhtOrderNo", payCode);
        dataMap.put("mhtCharset", StandardCharsets.UTF_8.name());
        dataMap.put("mhtSignType", "MD5");
        String mhtSignature = iPayNowServcieClient.getFormDataParamMD5(dataMap, IPayNowServcieClient.APP_KEY, StandardCharsets.UTF_8.name());
        dataMap.put("mhtSignature", mhtSignature);
        String response = iPayNowServcieClient.execute(iPayNowServcieClient.getMultiValueMap(dataMap));

        return convertToHashMap(response);
    }

    /**
     * 转换Map
     */
    public Map<String, String> convertToHashMap(String dataString) {
        Map<String, String> resultMap = new HashMap<>();
        if (isEmpty(dataString)){
            return resultMap;
        }

        // 数据是键值对形式，用&分隔
        String[] pairs = dataString.split("&");

        for (String pair : pairs) {
            // 每个键值对用=分隔
            int splitIndex = pair.indexOf("=");
            if (splitIndex != -1) {
                String key = pair.substring(0, splitIndex);
                String value = pair.substring(splitIndex + 1);
                resultMap.put(key, value);
            }
        }

        return resultMap;
    }

    String getMobile(String str){
        if (isEmpty(str)){
            return null;
        }else if (str.length() < 3){
            return repeat(App.MULTI,str.length());
        }else if (str.length() < 5){
            String replacement = "$1" + repeat(App.MULTI,str.length() - 2) + "$2";
            return str.replaceAll("(\\w).*(\\w)",replacement);
        } else if (str.length() < 11){
            String replacement = "$1" + repeat(App.MULTI,str.length() - 4) + "$2";
            return str.replaceAll("(\\w{2}).*(\\w{2})",replacement);
        } else{
            String replacement = "$1" + repeat(App.MULTI,str.length() - 7) + "$2";
            return str.replaceAll("(\\w{3}).*(\\w{4})",replacement);
        }
    }
    public static String repeat(String str, int count) {
        if (str == null || count <= 0) {
            return "";
        }
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < count; i++) {
            sb.append(str);
        }
        return sb.toString();
    }
}
