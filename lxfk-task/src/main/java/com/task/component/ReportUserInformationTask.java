package com.task.component;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.service.ReportUserInformationService;

@Component
public class ReportUserInformationTask extends BaseTask {
	
	@Autowired
	private ReportUserInformationService reportUserInformationService;

	/**
	 * 同步用户信息(每30分钟执行一次)
	 */
	@Scheduled(initialDelay = 10 * 1000, fixedRate= 30 * 60 * 1000)
	public void syncUserInformation(){
		try {
			logger.info("sync user information start");

			logger.info("sync user information end");
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
		}
	}

	/**
	 * 初始化同步用户信息(每年12月8号凌晨执行一次)
	 */
	@Scheduled(cron = "0 0 0 8 12 ?")
	public void initSyncUserInformation(){
		try {
			logger.info("init sync user information start");

			logger.info("init sync user information end");
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
		}
	}
}
