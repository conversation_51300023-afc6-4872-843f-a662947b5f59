package com.service;

import java.util.List;
import java.util.Map;

import com.domain.UserInformationCommentFlow;
import com.domain.complex.UserInformationCommentFlowQuery;
/**
 * 用户信息备注明细表业务层
 *
 * @date 2025-08-11 17:24:47
 */
public interface UserInformationCommentFlowService {

    Integer create(UserInformationCommentFlow userInformationCommentFlow);

    Integer modifyById(UserInformationCommentFlow userInformationCommentFlow);

    UserInformationCommentFlow findById(Long id);

    List<UserInformationCommentFlow> find(UserInformationCommentFlowQuery userInformationCommentFlowQuery);

    List<UserInformationCommentFlow> findAll(UserInformationCommentFlowQuery userInformationCommentFlowQuery);

    Integer count(UserInformationCommentFlowQuery userInformationCommentFlowQuery);

    List<UserInformationCommentFlow> findByIds(List<Long> ids);

    Map<Long, UserInformationCommentFlow> findMapByIds(List<Long> ids);

    Integer createBatch(List<UserInformationCommentFlow> userInformationCommentFlows);

    Integer modifyBatch(List<UserInformationCommentFlow> userInformationCommentFlows);

}

