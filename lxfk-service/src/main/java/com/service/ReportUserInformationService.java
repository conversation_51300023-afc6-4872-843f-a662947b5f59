package com.service;

import java.util.List;
import java.util.Map;

import com.domain.ReportUserInformation;
import com.domain.complex.ReportUserInformationQuery;
/**
 * 用户信息统计表业务层
 *
 * @date 2025-08-12 11:17:25
 */
public interface ReportUserInformationService {

    Integer create(ReportUserInformation reportUserInformation);

    Integer modifyById(ReportUserInformation reportUserInformation);

    ReportUserInformation findById(Integer id);

    List<ReportUserInformation> find(ReportUserInformationQuery reportUserInformationQuery);

    List<ReportUserInformation> findAll(ReportUserInformationQuery reportUserInformationQuery);

    Integer count(ReportUserInformationQuery reportUserInformationQuery);

    List<ReportUserInformation> findByIds(List<Integer> ids);

    Map<Integer, ReportUserInformation> findMapByIds(List<Integer> ids);

    Integer createBatch(List<ReportUserInformation> reportUserInformations);

    Integer modifyBatch(List<ReportUserInformation> reportUserInformations);

}

