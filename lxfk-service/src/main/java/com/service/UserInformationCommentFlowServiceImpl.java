package com.service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.domain.UserInformationCommentFlow;
import com.domain.complex.UserInformationCommentFlowQuery;

import com.dao.UserInformationCommentFlowDao;

@Service
public class UserInformationCommentFlowServiceImpl extends BaseService implements UserInformationCommentFlowService {

    @Autowired
    private UserInformationCommentFlowDao userInformationCommentFlowDao;

    @Override
    public Integer create(UserInformationCommentFlow userInformationCommentFlow) {
        return userInformationCommentFlowDao.insert(userInformationCommentFlow);
    }

    @Override
    public Integer modifyById(UserInformationCommentFlow userInformationCommentFlow) {
        return userInformationCommentFlowDao.updateById(userInformationCommentFlow);
    }

    @Override
    public UserInformationCommentFlow findById(Long id) {
        return userInformationCommentFlowDao.selectById(id);
    }

    @Override
    public List<UserInformationCommentFlow> find(UserInformationCommentFlowQuery userInformationCommentFlowQuery) {
        return userInformationCommentFlowDao.select(userInformationCommentFlowQuery);
    }

    @Override
    public List<UserInformationCommentFlow> findAll(UserInformationCommentFlowQuery userInformationCommentFlowQuery) {
        return userInformationCommentFlowDao.selectAll(userInformationCommentFlowQuery);
    }

    @Override
    public Integer count(UserInformationCommentFlowQuery userInformationCommentFlowQuery) {
        return userInformationCommentFlowDao.count(userInformationCommentFlowQuery);
    }

    @Override
    public List<UserInformationCommentFlow> findByIds(List<Long> ids) {
        return userInformationCommentFlowDao.selectByIds(ids);
    }

    @Override
    public Map<Long, UserInformationCommentFlow> findMapByIds(List<Long> ids) {
        Map<Long, UserInformationCommentFlow> userInformationCommentFlowMap = new HashMap<>();
        if (ids != null && !ids.isEmpty()){
            List<UserInformationCommentFlow> userInformationCommentFlows = userInformationCommentFlowDao.selectByIds(ids);
            for (UserInformationCommentFlow userInformationCommentFlow : userInformationCommentFlows) {
                userInformationCommentFlowMap.put(userInformationCommentFlow.getId(),userInformationCommentFlow);
            }
        }
        return userInformationCommentFlowMap;
    }

    @Override
    public Integer createBatch(List<UserInformationCommentFlow> userInformationCommentFlows) {
        return userInformationCommentFlowDao.insertBatch(userInformationCommentFlows);
    }

    @Override
    @Transactional
    public Integer modifyBatch(List<UserInformationCommentFlow> userInformationCommentFlows) {
        Integer result = 0;
        if (!this.isEmpty(userInformationCommentFlows) && !userInformationCommentFlows.isEmpty()){
            for (UserInformationCommentFlow userInformationCommentFlow : userInformationCommentFlows) {
                userInformationCommentFlowDao.updateById(userInformationCommentFlow);
                result ++;
            }
        }
        return result;
    }
}