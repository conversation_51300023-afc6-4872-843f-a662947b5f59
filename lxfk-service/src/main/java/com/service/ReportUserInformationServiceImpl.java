package com.service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.domain.ReportUserInformation;
import com.domain.complex.ReportUserInformationQuery;

import com.dao.ReportUserInformationDao;

@Service
public class ReportUserInformationServiceImpl extends BaseService implements ReportUserInformationService {

    @Autowired
    private ReportUserInformationDao reportUserInformationDao;

    @Override
    public Integer create(ReportUserInformation reportUserInformation) {
        return reportUserInformationDao.insert(reportUserInformation);
    }

    @Override
    public Integer modifyById(ReportUserInformation reportUserInformation) {
        return reportUserInformationDao.updateById(reportUserInformation);
    }

    @Override
    public ReportUserInformation findById(Integer id) {
        return reportUserInformationDao.selectById(id);
    }

    @Override
    public List<ReportUserInformation> find(ReportUserInformationQuery reportUserInformationQuery) {
        return reportUserInformationDao.select(reportUserInformationQuery);
    }

    @Override
    public List<ReportUserInformation> findAll(ReportUserInformationQuery reportUserInformationQuery) {
        return reportUserInformationDao.selectAll(reportUserInformationQuery);
    }

    @Override
    public Integer count(ReportUserInformationQuery reportUserInformationQuery) {
        return reportUserInformationDao.count(reportUserInformationQuery);
    }

    @Override
    public List<ReportUserInformation> findByIds(List<Integer> ids) {
        return reportUserInformationDao.selectByIds(ids);
    }

    @Override
    public Map<Integer, ReportUserInformation> findMapByIds(List<Integer> ids) {
        Map<Integer, ReportUserInformation> reportUserInformationMap = new HashMap<>();
        if (ids != null && !ids.isEmpty()){
            List<ReportUserInformation> reportUserInformations = reportUserInformationDao.selectByIds(ids);
            for (ReportUserInformation reportUserInformation : reportUserInformations) {
                reportUserInformationMap.put(reportUserInformation.getId(),reportUserInformation);
            }
        }
        return reportUserInformationMap;
    }

    @Override
    public Integer createBatch(List<ReportUserInformation> reportUserInformations) {
        return reportUserInformationDao.insertBatch(reportUserInformations);
    }

    @Override
    @Transactional
    public Integer modifyBatch(List<ReportUserInformation> reportUserInformations) {
        Integer result = 0;
        if (!this.isEmpty(reportUserInformations) && !reportUserInformations.isEmpty()){
            for (ReportUserInformation reportUserInformation : reportUserInformations) {
                reportUserInformationDao.updateById(reportUserInformation);
                result ++;
            }
        }
        return result;
    }
}