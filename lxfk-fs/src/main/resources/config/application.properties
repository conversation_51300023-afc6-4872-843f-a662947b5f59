###########################################################
server.session.cookie.http-only=true
server.session.cookie.max-age=120000000
server.session.timeout=120000000
###########################################################
spring.datasource.type=com.alibaba.druid.pool.DruidDataSource
spring.datasource.url=**********************************************************
spring.datasource.username=lxfk
spring.datasource.password=lxfk
spring.datasource.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.initialSize=5
spring.datasource.minIdle=5
spring.datasource.maxActive=20
spring.datasource.maxWait=60000
spring.datasource.timeBetweenEvictionRunsMillis=60000
spring.datasource.minEvictableIdleTimeMillis=300000
spring.datasource.validationQuery=SELECT 1 FROM DUAL
spring.datasource.testWhileIdle=true
spring.datasource.testOnBorrow=false
spring.datasource.testOnReturn=false
spring.datasource.poolPreparedStatements=true
spring.datasource.maxPoolPreparedStatementPerConnectionSize=20
spring.datasource.filters=stat,wall,log4j
spring.datasource.connectionProperties=druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
spring.datasource.useGlobalDataSourceStat=true
###########################################################
# logback
logging.config=classpath:config/logback-spring.xml
###########################################################
# file
spring.file.location.tmp=/home/<USER>/tmp
spring.file.location=/home/<USER>/
ali.oss.endpoint=oss-cn-beijing.aliyuncs.com
ali.oss.lan.endpoint=oss-cn-beijing.aliyuncs.com
ali.oss.bucket=zlzx-dev-bucket
###########################################################
spring.thymeleaf.cache=false
spring.thymeleaf.check-template-location=true 
spring.thymeleaf.content-type=text/html 
spring.thymeleaf.enabled=true
spring.thymeleaf.encoding=UTF-8
spring.thymeleaf.mode=HTML5
spring.thymeleaf.prefix=classpath:/templates/
spring.thymeleaf.suffix=.html
###########################################################
spring.redis.host=127.0.0.1
spring.redis.password=38659215534F141B5496BDF477D909A0
spring.redis.port=6379
###########################################################
spring.session.store-type=redis