<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE xml>
<configuration scan="true" scanPeriod="300 seconds" debug="false">
	<include resource="org/springframework/boot/logging/logback/defaults.xml" />
    <property name="LOG_FILE" value="${LOG_FILE:-${LOG_PATH:-${LOG_TEMP:-${java.io.tmpdir:-/tmp}}}/spring.log}"/>
    <include resource="org/springframework/boot/logging/logback/console-appender.xml" />
	<property name="filename" value="zlzx-fs" />
	<property name="home" value="/Users/<USER>/develop/logs/dev/${filename}" />
	<property name="maxHistory" value="72"/>
	<appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
		<encoder>
			<pattern>%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger - %msg%n</pattern>
		</encoder>
	</appender>
	<appender name="ERROR" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<file>${home}/${filename}-error.log</file>  
		<filter class="ch.qos.logback.classic.filter.LevelFilter">
			<level>ERROR</level>
			<onMatch>ACCEPT</onMatch>
			<onMismatch>DENY</onMismatch>
		</filter>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<fileNamePattern>${home}/${filename}-error.log.%d{yyyy-MM-dd-HH}</fileNamePattern>
			<maxHistory>${maxHistory}</maxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger - %msg%n</pattern>
		</encoder>
	</appender>
     <appender name="ALL" class="ch.qos.logback.core.rolling.RollingFileAppender">
     	<file>${home}/${filename}.log</file>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">  
            <fileNamePattern>${home}/${filename}.log.%d{yyyy-MM-dd-HH}</fileNamePattern>
            <maxHistory>${maxHistory}</maxHistory>
        	</rollingPolicy>
        	<encoder>
        		<charset>utf-8</charset>
        		<pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger - %msg%n</pattern>
        	</encoder>
     </appender>
     <logger name="com.dao" level="DEBUG"></logger>
     <root level="INFO">
     	<appender-ref ref="STDOUT" />
     	<appender-ref ref="ERROR" />
     	<appender-ref ref="ALL" />
     </root>  
</configuration>