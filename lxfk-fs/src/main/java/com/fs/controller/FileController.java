package com.fs.controller;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Date;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Controller;
import org.springframework.util.AntPathMatcher;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.HandlerMapping;

import com.common.client.ALiOSSClient;
import com.common.constant.DataStatus;
import com.common.util.DateUtil;
import com.common.util.FileUtil;
import com.fs.bean.Response;
import com.fs.constant.App;
import com.fs.sequence.FileNameSequence;
import com.service.FileService;

import net.coobird.thumbnailator.Thumbnails;

@Controller
public class FileController extends BaseController {
	private Logger logger = LoggerFactory.getLogger(FileController.class);

	@Value("${spring.file.location}")
	private String location;

	@Autowired
	private FileService fileService;

	@RequestMapping(value = "/v1/file/upload", method = RequestMethod.POST)
	public @ResponseBody Response<?> upload(@RequestParam("file") MultipartFile file){
		com.domain.File f = new com.domain.File();
		if (!file.isEmpty()) {
			try {
				String originalFilename = file.getOriginalFilename();
				logger.info("file original name {}",originalFilename);
				logger.info("file size {}", file.getSize());
				logger.info("file content type {}", file.getContentType());
				// 生成目标文件
				File destFile = this.getDestFile(originalFilename);
				// 保存到磁盘
				file.transferTo(destFile);
				String[] arr = originalFilename.split("\\" + App.DOT);
				String fileType = arr[arr.length - 1];
				// 保存到数据库
				Date date = this.getServerTime();
				f.setName(file.getOriginalFilename());
				f.setType(file.getContentType());
				f.setSize(file.getSize());
				f.setUrl(destFile.getAbsolutePath().replace(location, ""));
				// 存储到阿里云oss
				ALiOSSClient client = new ALiOSSClient();
				String fileName = destFile.getAbsolutePath().replace(location + "/", "");
				String ossUrl = client.upload(fileName, new FileInputStream(destFile));
				f.setOssUrl(ossUrl);
				f.setStatus(DataStatus.Y.getCode());
				f.setModifyTime(date);
				f.setCreateTime(date);
				fileService.create(f);
				destFile.delete();
            } catch (Exception e) {
				logger.error(e.getMessage(), e);
				return new Response<com.domain.File>(ERROR,FAILURE);
			}
        } else {
			logger.info("file is empty");
			return new Response<com.domain.File>(ERROR,FAILURE);
        }
		//文件扩展名
//	    	String fileName = null;
//	    	String fileExtension = null;
//	    	if(f.getUrl() != null){
//	    		int pos = f.getUrl().lastIndexOf(App.DOT);
//	    		fileName = f.getUrl().substring(0,pos);
//	    		fileExtension = f.getUrl().substring(pos);
//	    	}
//		return new Response<String>(OK,SUCCESS,this.append(EncryptUtil.encodeWithAES(fileName),fileExtension));
		return new Response<com.domain.File>(OK,SUCCESS,f);
	}

	@RequestMapping(value = "/v1/file/download")
	public String download( @ModelAttribute com.domain.File file){
		ALiOSSClient ossClient = new ALiOSSClient();
		String ossUrl = ossClient.generatePresignedUrl(file.getUrl().substring(1));
		return this.redirect(ossUrl);
	}

	@RequestMapping(value = "/v1/file/image")
	public String image( @ModelAttribute com.domain.File file){
		ALiOSSClient ossClient = new ALiOSSClient();
		String ossUrl = ossClient.generatePresignedUrl(file.getUrl().substring(1));
		return this.redirect(ossUrl);
	}

	@RequestMapping("/v1/file/mp3")
	public String mp3(@ModelAttribute com.domain.File file){
		ALiOSSClient ossClient = new ALiOSSClient();
		String ossUrl = ossClient.generatePresignedUrl(file.getUrl().substring(1),7200 * 1000L);
		return this.redirect(ossUrl);
	}

	@RequestMapping(value = "/v1/file/preview")
	public String preview( @ModelAttribute com.domain.File file){
		ALiOSSClient ossClient = new ALiOSSClient();
		String ossUrl = ossClient.generatePresignedUrl(file.getUrl().substring(1));
		return this.redirect(ossUrl);
	}
    
    @RequestMapping(value = "/v1/file/image/{year}/{month}/{day}/{hour}/{minutes}/{url:.+}")
	public void viewImage(
			HttpServletResponse response,
			HttpServletRequest request,
			@PathVariable("year") String year,
			@PathVariable("month") String month,
			@PathVariable("day") String day,
			@PathVariable("hour") String hour,
			@PathVariable("minutes") String minutes,
			@PathVariable("url") String url
	){
		String pattern = (String)
				request.getAttribute(HandlerMapping.BEST_MATCHING_PATTERN_ATTRIBUTE);

		String restOfUrl = new AntPathMatcher().extractPathWithinPattern(pattern,
				request.getServletPath());
		System.out.println(restOfUrl);
		com.domain.File file = new com.domain.File();
		if(file != null){
			if(file.getId() != null){
				file = this.fileService.findById(file.getId());
			}else if(file.getUrl() != null){
				file = this.fileService.findByUrl(file.getUrl());
			}else{
				return;
			}
		}
		File destFile = new File(this.location,file.getUrl());
		FileInputStream fis = null;
		OutputStream os = null;
		try{
			response.setContentType(file.getType());
			fis = new FileInputStream(destFile);
			byte[] data = new byte[fis.available()];
			fis.read(data);
			fis.close();
			os = response.getOutputStream();
			os.write(data);
			os.flush();
			os.close();
		}catch (Exception e) {
			logger.error(e.getMessage(), e);
		}finally{
			if(os != null){
				try{
					os.close();
				}catch (Exception e) {
					logger.error(e.getMessage(), e);
				}
			}
			if(fis != null){
				try{
					fis.close();
				}catch (Exception e) {
					logger.error(e.getMessage(), e);
				}
			}
		}
	}
    
    @RequestMapping(value = "/v1/file/image/thumbnail/{year}/{month}/{day}/{hour}/{minutes}/{url:.+}")
	public void viewThumbnailImage(
			HttpServletResponse response,
			HttpServletRequest request,
			@PathVariable("year") String year,
			@PathVariable("month") String month,
			@PathVariable("day") String day,
			@PathVariable("hour") String hour,
			@PathVariable("minutes") String minutes,
			@PathVariable("url") String url
	){
		com.domain.File file = new com.domain.File();
		file.setUrl(this.append(year,month,day,hour,minutes,url));
		if(file != null){
			if(file.getId() != null){
				file = this.fileService.findById(file.getId());
			}else if(file.getUrl() != null){
				//文件扩展名
				//	    		String fileName = null;
				//	    		String fileExtension = null;
				//			if(file.getUrl() != null){
				//	    	    		int pos = file.getUrl().lastIndexOf(App.DOT);
				//	    	    		fileName = file.getUrl().substring(0,pos);
				//	    	    		fileExtension = file.getUrl().substring(pos);
				//	    	    	}
				//			file = this.fileService.findByUrl(this.append(EncryptUtil.decodeWithAES(fileName),fileExtension));
				file = this.fileService.findByUrl(file.getUrl());
			}else{

			}
		}
		ByteArrayOutputStream fos = new ByteArrayOutputStream();
		OutputStream os = null;
		try{
			Thumbnails.of(this.location + file.getUrl())
					//    	    	.sourceRegion(Positions.CENTER,800,800)
					.scale(0.5f)
					.toOutputStream(fos);
			response.setContentType(file.getType());
			os = response.getOutputStream();
			os.write(fos.toByteArray());
			os.flush();
			os.close();
		}catch (Exception e) {
			logger.error(e.getMessage(), e);
		}finally{
			if(os != null){
				try{
					os.close();
				}catch (Exception e) {
					logger.error(e.getMessage(), e);
				}
			}
			if(fos != null){
				try{
					fos.close();
				}catch (Exception e) {
					logger.error(e.getMessage(), e);
				}
			}
		}
	}
    
    @RequestMapping("/v1/file/image/thumbnail/**")
	public void thumbnail(HttpServletResponse response, @ModelAttribute com.domain.File file){
		if(file != null){
			if(file.getId() != null){
				file = this.fileService.findById(file.getId());
			}else if(file.getUrl() != null){
				//文件扩展名
//		    		String fileName = null;
//		    		String fileExtension = null;
//				if(file.getUrl() != null){
//		    	    		int pos = file.getUrl().lastIndexOf(App.DOT);
//		    	    		fileName = file.getUrl().substring(0,pos);
//		    	    		fileExtension = file.getUrl().substring(pos);
//		    	    	}
				//			file = this.fileService.findByUrl(this.append(EncryptUtil.decodeWithAES(fileName),fileExtension));
				file = this.fileService.findByUrl(file.getUrl());
			}else{

			}
		}
		ByteArrayOutputStream fos = new ByteArrayOutputStream();
		OutputStream os = null;
		try{
			Thumbnails.of(this.location + file.getUrl())
					//    	    	.sourceRegion(Positions.CENTER,800,800)
					.scale(0.5f)
					.toOutputStream(fos);
			response.setContentType(file.getType());
			os = response.getOutputStream();
			os.write(fos.toByteArray());
			os.flush();
			os.close();
		}catch (Exception e) {
			logger.error(e.getMessage(), e);
		}finally{
			if(os != null){
				try{
					os.close();
				}catch (Exception e) {
					logger.error(e.getMessage(), e);
				}
			}
			if(fos != null){
				try{
					fos.close();
				}catch (Exception e) {
					logger.error(e.getMessage(), e);
				}
			}
		}
	}


    private String getName(String fileName){
		fileName = fileName.replace("\\", "/");
		int index = fileName.lastIndexOf("/");
		if(index == -1){
			return fileName;
		}
		return fileName.substring(index + 1);
	}
    private String getExtension(String fileName){
		fileName = this.getName(fileName);
		int index = fileName.lastIndexOf(App.DOT);
		if(index == -1){
			return "";
		}
		return fileName.substring(index);
	}
    private File getDestFile(String fileName){
		String datetime = DateUtil.getServerTime("yyyyMMddHHmmss");
		String dir = new StringBuilder(this.location)
				.append("/").append(datetime.substring(0,4))
				.append("/").append(datetime.substring(4,6))
				.append("/").append(datetime.substring(6,8))
				.append("/").append(datetime.substring(8,10))
				.append("/").append(datetime.substring(10,12)).toString();
		FileUtil.make(dir);
		File file = new File(dir,new StringBuilder()
				.append(datetime.substring(12,14))
				.append(FileNameSequence.nextValue())
				.append(this.getExtension(fileName)).toString());
		return file;
	}
}
