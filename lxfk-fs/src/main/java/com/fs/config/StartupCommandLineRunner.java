package com.fs.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import com.common.constant.App;


@Component
@Order(Ordered.HIGHEST_PRECEDENCE)
public class StartupCommand<PERSON>ineRunner implements CommandLineRunner {
	


	@Value("${ali.oss.bucket}")
	private String bucketName;

	@Value("${ali.oss.endpoint}")
	private String endpoint;

	@Value("${ali.oss.lan.endpoint}")
	private String lanEndpoint;

	public void run(String... args) throws Exception {
		this.loadApplicationValues();
	}
	
	private void loadApplicationValues(){
		App.FILE_BUCKET_NAME = this.bucketName;
		App.FILE_ENDPOINT = this.endpoint;
		App.FILE_LAN_ENDPOINT = this.lanEndpoint;
	}

	
}